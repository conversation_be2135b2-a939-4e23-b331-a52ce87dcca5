{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n    \n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span>\n              </Link>\n            </div>\n            \n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n                        \n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n                        \n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;CAC5C;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAyC,cAAW;;8BACjE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;;;;;;8CAO3D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/dashboard/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport Link from 'next/link';\nimport {\n  PlusIcon,\n  EyeIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  ChatBubbleLeftRightIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nexport default function Dashboard() {\n  const { data: session } = useSession();\n  const [stats, setStats] = useState({\n    totalPosts: 0,\n    activePosts: 0,\n    resolvedPosts: 0,\n    totalViews: 0\n  });\n  const [recentItems, setRecentItems] = useState([]);\n  const [myClaims, setMyClaims] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (session?.user?.id) {\n      fetchDashboardData();\n    }\n  }, [session?.user?.id]);\n\n  const fetchDashboardData = async () => {\n    try {\n      // Fetch user's items\n      const response = await fetch(`/api/items?postedBy=${session.user.id}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        const userItems = data.items;\n        const activePosts = userItems.filter(item => item.status === 'active').length;\n        const resolvedPosts = userItems.filter(item => item.status === 'resolved').length;\n        const totalViews = userItems.reduce((sum, item) => sum + (item.views || 0), 0);\n\n        setStats({\n          totalPosts: userItems.length,\n          activePosts,\n          resolvedPosts,\n          totalViews\n        });\n\n        setRecentItems(userItems.slice(0, 5));\n      }\n\n      // Fetch user's claims\n      const claimsResponse = await fetch('/api/claims?type=sent');\n      const claimsData = await claimsResponse.json();\n\n      if (claimsResponse.ok) {\n        setMyClaims(claimsData.claims.slice(0, 5));\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) return 'Yesterday';\n    if (diffDays < 7) return `${diffDays} days ago`;\n    return date.toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-8\"></div>\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n              {[...Array(4)].map((_, i) => (\n                <div key={i} className=\"bg-white p-6 rounded-lg shadow\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            Welcome back, {session?.user?.name}!\n          </h1>\n          <p className=\"mt-2 text-gray-600\">\n            Here's an overview of your lost and found activity\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <PlusIcon className=\"h-8 w-8 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Total Posts</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalPosts}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <ClockIcon className=\"h-8 w-8 text-yellow-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Active Posts</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.activePosts}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <CheckCircleIcon className=\"h-8 w-8 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Resolved</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.resolvedPosts}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <EyeIcon className=\"h-8 w-8 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-500\">Total Views</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalViews}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Recent Posts */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <div className=\"flex justify-between items-center\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Recent Posts</h3>\n                  <Link href=\"/items/post\" className=\"btn-primary\">\n                    <PlusIcon className=\"h-4 w-4 mr-2\" />\n                    New Post\n                  </Link>\n                </div>\n              </div>\n              <div className=\"card-body\">\n                {recentItems.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {recentItems.map((item) => (\n                      <div key={item._id} className=\"border border-gray-200 rounded-lg p-4\">\n                        <div className=\"flex justify-between items-start\">\n                          <div className=\"flex-1\">\n                            <div className=\"flex items-center space-x-2\">\n                              <h4 className=\"text-lg font-medium text-gray-900\">\n                                {item.title}\n                              </h4>\n                              <span className={`badge ${\n                                item.type === 'lost' ? 'badge-danger' : 'badge-success'\n                              }`}>\n                                {item.type === 'lost' ? 'Lost' : 'Found'}\n                              </span>\n                              <span className={`badge ${\n                                item.status === 'active' ? 'badge-warning' : 'badge-info'\n                              }`}>\n                                {item.status}\n                              </span>\n                            </div>\n                            <p className=\"text-sm text-gray-500 mt-1\">\n                              Posted {formatDate(item.createdAt)}\n                            </p>\n                          </div>\n\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                            <div className=\"flex items-center\">\n                              <EyeIcon className=\"h-4 w-4 mr-1\" />\n                              {item.views} views\n                            </div>\n                            {item.claimsCount > 0 && (\n                              <div className=\"flex items-center\">\n                                <ChatBubbleLeftRightIcon className=\"h-4 w-4 mr-1\" />\n                                {item.claimsCount} claims\n                              </div>\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"mt-3 flex space-x-2\">\n                          <Link\n                            href={`/items/${item._id}`}\n                            className=\"btn-secondary text-xs\"\n                          >\n                            View Details\n                          </Link>\n                          <Link\n                            href={`/items/${item._id}/edit`}\n                            className=\"btn-secondary text-xs\"\n                          >\n                            Edit\n                          </Link>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <PlusIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No posts yet</h3>\n                    <p className=\"mt-1 text-sm text-gray-500\">\n                      Get started by posting your first lost or found item.\n                    </p>\n                    <div className=\"mt-6\">\n                      <Link href=\"/items/post\" className=\"btn-primary\">\n                        <PlusIcon className=\"h-4 w-4 mr-2\" />\n                        Post Your First Item\n                      </Link>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* My Claims */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">My Claim Requests</h3>\n            </div>\n            <div className=\"card-body\">\n              {myClaims.length > 0 ? (\n                <div className=\"space-y-3\">\n                  {myClaims.map((claim) => (\n                    <div key={claim._id} className=\"border border-gray-200 rounded-lg p-3\">\n                      <div className=\"flex justify-between items-start\">\n                        <div className=\"flex-1\">\n                          <h4 className=\"text-sm font-medium text-gray-900\">\n                            {claim.item?.title}\n                          </h4>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            Submitted {formatDate(claim.createdAt)}\n                          </p>\n                        </div>\n                        <span className={`badge text-xs ${\n                          claim.status === 'pending' ? 'badge-warning' :\n                          claim.status === 'approved' ? 'badge-success' :\n                          claim.status === 'rejected' ? 'badge-danger' : 'badge-info'\n                        }`}>\n                          {claim.status}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-6\">\n                  <p className=\"text-sm text-gray-500\">No claim requests yet</p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Quick Actions & Tips */}\n          <div className=\"space-y-6\">\n            {/* Quick Actions */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Quick Actions</h3>\n              </div>\n              <div className=\"card-body\">\n                <div className=\"space-y-3\">\n                  <Link\n                    href=\"/items/post\"\n                    className=\"flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors\"\n                  >\n                    <PlusIcon className=\"h-5 w-5 text-blue-600 mr-3\" />\n                    <span className=\"text-sm font-medium text-blue-900\">Post New Item</span>\n                  </Link>\n\n                  <Link\n                    href=\"/items/lost\"\n                    className=\"flex items-center p-3 bg-red-50 rounded-lg hover:bg-red-100 transition-colors\"\n                  >\n                    <ExclamationTriangleIcon className=\"h-5 w-5 text-red-600 mr-3\" />\n                    <span className=\"text-sm font-medium text-red-900\">Browse Lost Items</span>\n                  </Link>\n\n                  <Link\n                    href=\"/items/found\"\n                    className=\"flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors\"\n                  >\n                    <CheckCircleIcon className=\"h-5 w-5 text-green-600 mr-3\" />\n                    <span className=\"text-sm font-medium text-green-900\">Browse Found Items</span>\n                  </Link>\n\n                  <Link\n                    href=\"/chat\"\n                    className=\"flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors\"\n                  >\n                    <ChatBubbleLeftRightIcon className=\"h-5 w-5 text-purple-600 mr-3\" />\n                    <span className=\"text-sm font-medium text-purple-900\">My Conversations</span>\n                  </Link>\n                </div>\n              </div>\n            </div>\n\n            {/* Tips */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Tips for Success</h3>\n              </div>\n              <div className=\"card-body\">\n                <div className=\"space-y-3 text-sm text-gray-600\">\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3\"></div>\n                    <p>Include clear photos and detailed descriptions</p>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3\"></div>\n                    <p>Specify exact location where item was lost/found</p>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3\"></div>\n                    <p>Respond quickly to messages and claims</p>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3\"></div>\n                    <p>Mark items as resolved when found/returned</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAbA;;;;;;AAee,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,YAAY;QACZ,aAAa;QACb,eAAe;QACf,YAAY;IACd;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,SAAS,MAAM,IAAI;gBACrB;YACF;QACF;8BAAG;QAAC,SAAS,MAAM;KAAG;IAEtB,MAAM,qBAAqB;QACzB,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE;YACrE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,YAAY,KAAK,KAAK;gBAC5B,MAAM,cAAc,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,MAAM;gBAC7E,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;gBACjF,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG;gBAE5E,SAAS;oBACP,YAAY,UAAU,MAAM;oBAC5B;oBACA;oBACA;gBACF;gBAEA,eAAe,UAAU,KAAK,CAAC,GAAG;YACpC;YAEA,sBAAsB;YACtB,MAAM,iBAAiB,MAAM,MAAM;YACnC,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,IAAI,eAAe,EAAE,EAAE;gBACrB,YAAY,WAAW,MAAM,CAAC,KAAK,CAAC,GAAG;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM;QAChC,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,OAAO,KAAK,kBAAkB;IAChC;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;uCAFP;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmC;oCAChC,SAAS,MAAM;oCAAK;;;;;;;0CAErC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;;;;;;0DAE7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM5E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;;0EACjC,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;sDAK3C,6LAAC;4CAAI,WAAU;sDACZ,YAAY,MAAM,GAAG,kBACpB,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;wDAAmB,WAAU;;0EAC5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAG,WAAU;kGACX,KAAK,KAAK;;;;;;kGAEb,6LAAC;wFAAK,WAAW,CAAC,MAAM,EACtB,KAAK,IAAI,KAAK,SAAS,iBAAiB,iBACxC;kGACC,KAAK,IAAI,KAAK,SAAS,SAAS;;;;;;kGAEnC,6LAAC;wFAAK,WAAW,CAAC,MAAM,EACtB,KAAK,MAAM,KAAK,WAAW,kBAAkB,cAC7C;kGACC,KAAK,MAAM;;;;;;;;;;;;0FAGhB,6LAAC;gFAAE,WAAU;;oFAA6B;oFAChC,WAAW,KAAK,SAAS;;;;;;;;;;;;;kFAIrC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,gNAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;oFAClB,KAAK,KAAK;oFAAC;;;;;;;4EAEb,KAAK,WAAW,GAAG,mBAClB,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,gPAAA,CAAA,0BAAuB;wFAAC,WAAU;;;;;;oFAClC,KAAK,WAAW;oFAAC;;;;;;;;;;;;;;;;;;;0EAM1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;wEAC1B,WAAU;kFACX;;;;;;kFAGD,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC;wEAC/B,WAAU;kFACX;;;;;;;;;;;;;uDA/CK,KAAK,GAAG;;;;;;;;;qEAuDtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAc,WAAU;;8EACjC,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,GAAG,kBACjB,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,sBACb,6LAAC;oDAAoB,WAAU;8DAC7B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFACX,MAAM,IAAI,EAAE;;;;;;kFAEf,6LAAC;wEAAE,WAAU;;4EAA6B;4EAC7B,WAAW,MAAM,SAAS;;;;;;;;;;;;;0EAGzC,6LAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,MAAM,MAAM,KAAK,YAAY,kBAC7B,MAAM,MAAM,KAAK,aAAa,kBAC9B,MAAM,MAAM,KAAK,aAAa,iBAAiB,cAC/C;0EACC,MAAM,MAAM;;;;;;;;;;;;mDAfT,MAAM,GAAG;;;;;;;;;iEAsBvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;0CAO7C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;;;;;;;sEAGtD,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,6LAAC,gPAAA,CAAA,0BAAuB;oEAAC,WAAU;;;;;;8EACnC,6LAAC;oEAAK,WAAU;8EAAmC;;;;;;;;;;;;sEAGrD,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,6LAAC,gOAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;8EAC3B,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAGvD,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,6LAAC,gPAAA,CAAA,0BAAuB;oEAAC,WAAU;;;;;;8EACnC,6LAAC;oEAAK,WAAU;8EAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAE;;;;;;;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAE;;;;;;;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAE;;;;;;;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB;GA3WwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}]}