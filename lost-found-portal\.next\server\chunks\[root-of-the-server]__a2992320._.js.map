{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/db.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    validate: {\n      validator: function(email) {\n        // Validate UMT email domain\n        return email.endsWith('@umt.edu.pk') || email.endsWith('@student.umt.edu.pk');\n      },\n      message: 'Please use a valid UMT email address'\n    }\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters']\n  },\n  role: {\n    type: String,\n    enum: ['student', 'admin'],\n    default: 'student'\n  },\n  studentId: {\n    type: String,\n    sparse: true,\n    unique: true\n  },\n  phone: {\n    type: String,\n    trim: true\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  verificationToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordExpires: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Remove password from JSON output\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.verificationToken;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpires;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,UAAU;YACR,WAAW,SAAS,KAAK;gBACvB,4BAA4B;gBAC5B,OAAO,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC;YACzD;YACA,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;IAC1D;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAW;SAAQ;QAC1B,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,mBAAmB;QACjB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,sBAAsB;QACpB,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,iBAAiB;IACnC,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,oBAAoB;IACtC,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/auth.js"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport connectDB from './db';\nimport User from '@/models/User';\n\nexport const authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error('Email and password are required');\n        }\n\n        await connectDB();\n\n        const user = await User.findOne({ email: credentials.email });\n        \n        if (!user) {\n          throw new Error('No user found with this email');\n        }\n\n        const isPasswordValid = await user.comparePassword(credentials.password);\n        \n        if (!isPasswordValid) {\n          throw new Error('Invalid password');\n        }\n\n        if (!user.isVerified) {\n          throw new Error('Please verify your email before logging in');\n        }\n\n        return {\n          id: user._id.toString(),\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          studentId: user.studentId,\n          avatar: user.avatar\n        };\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.studentId = user.studentId;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub;\n        session.user.role = token.role;\n        session.user.studentId = token.studentId;\n        session.user.avatar = token.avatar;\n      }\n      return session;\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error'\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n// Utility functions for authentication\nexport const validateUMTEmail = (email) => {\n  const umtDomains = ['@umt.edu.pk', '@student.umt.edu.pk'];\n  return umtDomains.some(domain => email.endsWith(domain));\n};\n\nexport const generateVerificationToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\nexport const generateResetToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\n// Role-based access control\nexport const requireAuth = (handler, requiredRole = null) => {\n  return async (req, res) => {\n    const session = await getServerSession(req, res, authOptions);\n    \n    if (!session) {\n      return res.status(401).json({ error: 'Authentication required' });\n    }\n    \n    if (requiredRole && session.user.role !== requiredRole) {\n      return res.status(403).json({ error: 'Insufficient permissions' });\n    }\n    \n    req.user = session.user;\n    return handler(req, res);\n  };\n};\n\n// Check if user is admin\nexport const isAdmin = (user) => {\n  return user?.role === 'admin';\n};\n\n// Check if user owns the resource\nexport const isOwner = (user, resourceUserId) => {\n  return user?.id === resourceUserId.toString();\n};\n\n// Check if user can access resource\nexport const canAccess = (user, resourceUserId, requiredRole = null) => {\n  if (requiredRole && user?.role !== requiredRole) {\n    return false;\n  }\n  \n  return isAdmin(user) || isOwner(user, resourceUserId);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;gBAEd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,YAAY,KAAK;gBAAC;gBAE3D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,YAAY,QAAQ;gBAEvE,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;oBACrB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;oBACzB,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,aAAa;QAAC;QAAe;KAAsB;IACzD,OAAO,WAAW,IAAI,CAAC,CAAA,SAAU,MAAM,QAAQ,CAAC;AAClD;AAEO,MAAM,4BAA4B;IACvC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,MAAM,qBAAqB;IAChC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,MAAM,cAAc,CAAC,SAAS,eAAe,IAAI;IACtD,OAAO,OAAO,KAAK;QACjB,MAAM,UAAU,MAAM,iBAAiB,KAAK,KAAK;QAEjD,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA0B;QACjE;QAEA,IAAI,gBAAgB,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAc;YACtD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA2B;QAClE;QAEA,IAAI,IAAI,GAAG,QAAQ,IAAI;QACvB,OAAO,QAAQ,KAAK;IACtB;AACF;AAGO,MAAM,UAAU,CAAC;IACtB,OAAO,MAAM,SAAS;AACxB;AAGO,MAAM,UAAU,CAAC,MAAM;IAC5B,OAAO,MAAM,OAAO,eAAe,QAAQ;AAC7C;AAGO,MAAM,YAAY,CAAC,MAAM,gBAAgB,eAAe,IAAI;IACjE,IAAI,gBAAgB,MAAM,SAAS,cAAc;QAC/C,OAAO;IACT;IAEA,OAAO,QAAQ,SAAS,QAAQ,MAAM;AACxC", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/api/auth/signup/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport User from '@/models/User';\nimport { validateUMTEmail, generateVerificationToken } from '@/lib/auth';\n\nexport async function POST(request) {\n  try {\n    const { name, email, password, studentId, phone } = await request.json();\n\n    // Validate required fields\n    if (!name || !email || !password) {\n      return NextResponse.json(\n        { error: 'Name, email, and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Validate UMT email\n    if (!validateUMTEmail(email)) {\n      return NextResponse.json(\n        { error: 'Please use a valid UMT email address (@umt.edu.pk or @student.umt.edu.pk)' },\n        { status: 400 }\n      );\n    }\n\n    // Validate password length\n    if (password.length < 6) {\n      return NextResponse.json(\n        { error: 'Password must be at least 6 characters long' },\n        { status: 400 }\n      );\n    }\n\n    await connectDB();\n\n    // Check if user already exists\n    const existingUser = await User.findOne({ email });\n    if (existingUser) {\n      return NextResponse.json(\n        { error: 'User with this email already exists' },\n        { status: 400 }\n      );\n    }\n\n    // Check if student ID is already taken (if provided)\n    if (studentId) {\n      const existingStudentId = await User.findOne({ studentId });\n      if (existingStudentId) {\n        return NextResponse.json(\n          { error: 'Student ID is already registered' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Create verification token\n    const verificationToken = generateVerificationToken();\n\n    // Create new user\n    const user = new User({\n      name,\n      email,\n      password,\n      studentId,\n      phone,\n      verificationToken,\n      isVerified: false // In production, you'd send an email verification\n    });\n\n    await user.save();\n\n    // For development, we'll auto-verify the user\n    // In production, you would send an email with the verification token\n    user.isVerified = true;\n    user.verificationToken = null;\n    await user.save();\n\n    return NextResponse.json(\n      { \n        message: 'User created successfully',\n        user: {\n          id: user._id,\n          name: user.name,\n          email: user.email,\n          role: user.role,\n          studentId: user.studentId\n        }\n      },\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Signup error:', error);\n    \n    if (error.name === 'ValidationError') {\n      const errors = Object.values(error.errors).map(err => err.message);\n      return NextResponse.json(\n        { error: errors.join(', ') },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtE,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyC,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4E,GACrF;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8C,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,+BAA+B;QAC/B,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE;QAAM;QAChD,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,IAAI,WAAW;YACb,MAAM,oBAAoB,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE;YAAU;YACzD,IAAI,mBAAmB;gBACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAmC,GAC5C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,4BAA4B;QAC5B,MAAM,oBAAoB,CAAA,GAAA,oHAAA,CAAA,4BAAyB,AAAD;QAElD,kBAAkB;QAClB,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;YACpB;YACA;YACA;YACA;YACA;YACA;YACA,YAAY,MAAM,kDAAkD;QACtE;QAEA,MAAM,KAAK,IAAI;QAEf,8CAA8C;QAC9C,qEAAqE;QACrE,KAAK,UAAU,GAAG;QAClB,KAAK,iBAAiB,GAAG;QACzB,MAAM,KAAK,IAAI;QAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,GAAG;gBACZ,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,SAAS;YAC3B;QACF,GACA;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAE/B,IAAI,MAAM,IAAI,KAAK,mBAAmB;YACpC,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO;YACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,OAAO,IAAI,CAAC;YAAM,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}