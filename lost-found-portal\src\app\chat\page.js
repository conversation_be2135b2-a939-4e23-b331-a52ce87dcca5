'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  PaperAirplaneIcon,
  PhotoIcon,
  FaceSmileIcon,
  ArrowLeftIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';
import Navbar from '@/components/layout/Navbar';

export default function Chat() {
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();
  const messagesEndRef = useRef(null);

  const chatId = searchParams.get('chatId');
  const itemId = searchParams.get('itemId');
  const otherUserId = searchParams.get('otherUserId');

  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [chatInfo, setChatInfo] = useState(null);
  const [typing, setTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);

  // Fetch chat data
  useEffect(() => {
    if (session && (chatId || (itemId && otherUserId))) {
      fetchChat();
    }
  }, [session, chatId, itemId, otherUserId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchChat = async () => {
    try {
      setLoading(true);
      let url = '/api/chat';

      if (chatId) {
        url += `?chatId=${chatId}`;
      } else if (itemId && otherUserId) {
        url += `?itemId=${itemId}&otherUserId=${otherUserId}`;
      }

      const response = await fetch(url);
      const data = await response.json();

      if (response.ok) {
        setChatInfo(data.chat);
        setMessages(data.chat.messages || []);
      } else {
        console.error('Failed to fetch chat:', data.error);
        setMessages([]);
      }
    } catch (error) {
      console.error('Error fetching chat:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!newMessage.trim() || sending) return;

    setSending(true);
    const messageContent = newMessage;
    setNewMessage('');

    try {
      // Add message optimistically to UI
      const tempMessage = {
        _id: `temp_${Date.now()}`,
        sender: {
          _id: session?.user?.id,
          name: session?.user?.name,
          avatar: session?.user?.avatar
        },
        content: messageContent,
        timestamp: new Date().toISOString(),
        sending: true
      };

      setMessages(prev => [...prev, tempMessage]);

      // Send to API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatId: chatInfo?._id,
          message: messageContent
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Replace temp message with real message
        setMessages(prev =>
          prev.map(msg =>
            msg._id === tempMessage._id
              ? { ...data.newMessage, _id: data.newMessage._id }
              : msg
          )
        );
      } else {
        // Remove temp message on error
        setMessages(prev => prev.filter(msg => msg._id !== tempMessage._id));
        console.error('Failed to send message:', data.error);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove temp message on error
      setMessages(prev => prev.filter(msg => msg._id.startsWith('temp_')));
    } finally {
      setSending(false);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return `Yesterday ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[600px] flex flex-col">
          {/* Chat Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Chat about Lost Item
                </h1>
                <p className="text-sm text-gray-500">
                  Discussing item recovery
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-gray-500">Online</span>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {loading && messages.length === 0 ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              messages.map((message) => {
                const isOwn = message.sender._id === session?.user?.id;
                return (
                  <div
                    key={message._id}
                    className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
                      {/* Avatar */}
                      <div className={`flex-shrink-0 ${isOwn ? 'ml-2' : 'mr-2'}`}>
                        {message.sender.avatar ? (
                          <img
                            src={message.sender.avatar}
                            alt={message.sender.name}
                            className="h-8 w-8 rounded-full"
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-xs font-medium text-gray-700">
                              {message.sender.name?.charAt(0) || '?'}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Message Bubble */}
                      <div>
                        <div
                          className={`px-4 py-2 rounded-lg relative ${
                            isOwn
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 text-gray-900'
                          } ${message.sending ? 'opacity-70' : ''}`}
                        >
                          <p className="text-sm">{message.content}</p>
                          {message.sending && (
                            <div className="absolute -right-1 -bottom-1">
                              <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-transparent"></div>
                            </div>
                          )}
                        </div>
                        <p className={`text-xs text-gray-500 mt-1 ${isOwn ? 'text-right' : 'text-left'}`}>
                          {formatTime(message.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
            {otherUserTyping && (
              <div className="flex justify-start">
                <div className="flex max-w-xs lg:max-w-md">
                  <div className="flex-shrink-0 mr-2">
                    <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-700">...</span>
                    </div>
                  </div>
                  <div className="bg-gray-100 px-4 py-2 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="px-6 py-4 border-t border-gray-200">
            <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <PhotoIcon className="h-5 w-5" />
              </button>

              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FaceSmileIcon className="h-5 w-5" />
              </button>

              <div className="flex-1">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="form-input"
                  disabled={loading}
                />
              </div>

              <button
                type="submit"
                disabled={sending || !newMessage.trim()}
                className="btn-primary"
              >
                {sending ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <PaperAirplaneIcon className="h-4 w-4" />
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Chat Guidelines */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Chat Guidelines</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Be respectful and courteous in your communication</li>
            <li>• Arrange to meet in safe, public places on campus</li>
            <li>• Verify item ownership before returning</li>
            <li>• Report any suspicious behavior to campus security</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
