'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import { 
  PaperAirplaneIcon,
  PhotoIcon,
  FaceSmileIcon
} from '@heroicons/react/24/outline';
import Navbar from '@/components/layout/Navbar';

export default function Chat() {
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const itemId = searchParams.get('item');
  const userId = searchParams.get('user');
  
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);

  // Mock messages for demonstration
  useEffect(() => {
    setMessages([
      {
        id: 1,
        sender: {
          id: 'other-user',
          name: '<PERSON>',
          avatar: null
        },
        content: 'Hi! I think I found your lost iPhone. Can you describe it?',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        isOwn: false
      },
      {
        id: 2,
        sender: {
          id: session?.user?.id,
          name: session?.user?.name,
          avatar: session?.user?.avatar
        },
        content: 'Yes! It\'s a black iPhone 13 Pro with a blue case. Where did you find it?',
        timestamp: new Date(Date.now() - 3000000).toISOString(),
        isOwn: true
      },
      {
        id: 3,
        sender: {
          id: 'other-user',
          name: 'John Doe',
          avatar: null
        },
        content: 'I found it in the library on the 2nd floor, near the study tables. Can we meet tomorrow at 2 PM at the main gate?',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        isOwn: false
      }
    ]);
  }, [session]);

  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim()) return;

    setLoading(true);

    // Add message to local state (in production, send to API)
    const message = {
      id: Date.now(),
      sender: {
        id: session?.user?.id,
        name: session?.user?.name,
        avatar: session?.user?.avatar
      },
      content: newMessage,
      timestamp: new Date().toISOString(),
      isOwn: true
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
    setLoading(false);
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return `Yesterday ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[600px] flex flex-col">
          {/* Chat Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Chat about Lost Item
                </h1>
                <p className="text-sm text-gray-500">
                  Discussing item recovery
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-gray-500">Online</span>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isOwn ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex max-w-xs lg:max-w-md ${message.isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
                  {/* Avatar */}
                  <div className={`flex-shrink-0 ${message.isOwn ? 'ml-2' : 'mr-2'}`}>
                    {message.sender.avatar ? (
                      <img
                        src={message.sender.avatar}
                        alt={message.sender.name}
                        className="h-8 w-8 rounded-full"
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-700">
                          {message.sender.name?.charAt(0) || '?'}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Message Bubble */}
                  <div>
                    <div
                      className={`px-4 py-2 rounded-lg ${
                        message.isOwn
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                    </div>
                    <p className={`text-xs text-gray-500 mt-1 ${message.isOwn ? 'text-right' : 'text-left'}`}>
                      {formatTime(message.timestamp)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="px-6 py-4 border-t border-gray-200">
            <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <PhotoIcon className="h-5 w-5" />
              </button>
              
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FaceSmileIcon className="h-5 w-5" />
              </button>

              <div className="flex-1">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="form-input"
                  disabled={loading}
                />
              </div>

              <button
                type="submit"
                disabled={loading || !newMessage.trim()}
                className="btn-primary"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <PaperAirplaneIcon className="h-4 w-4" />
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Chat Guidelines */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Chat Guidelines</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Be respectful and courteous in your communication</li>
            <li>• Arrange to meet in safe, public places on campus</li>
            <li>• Verify item ownership before returning</li>
            <li>• Report any suspicious behavior to campus security</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
