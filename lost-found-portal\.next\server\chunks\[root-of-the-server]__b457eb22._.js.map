{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/db.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/Item.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst ItemSchema = new mongoose.Schema({\n  title: {\n    type: String,\n    required: [true, 'Item title is required'],\n    trim: true,\n    maxlength: [100, 'Title cannot be more than 100 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot be more than 500 characters']\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: [\n      'Electronics',\n      'Books & Stationery',\n      'Clothing & Accessories',\n      'ID Cards & Documents',\n      'Keys',\n      'Water Bottles',\n      'Bags & Backpacks',\n      'Sports Equipment',\n      'Jewelry',\n      'Other'\n    ]\n  },\n  type: {\n    type: String,\n    required: [true, 'Item type is required'],\n    enum: ['lost', 'found']\n  },\n  status: {\n    type: String,\n    enum: ['active', 'claimed', 'resolved', 'archived'],\n    default: 'active'\n  },\n  images: [{\n    url: {\n      type: String,\n      required: true\n    },\n    publicId: {\n      type: String,\n      required: true\n    }\n  }],\n  location: {\n    type: String,\n    required: [true, 'Location is required'],\n    trim: true,\n    maxlength: [100, 'Location cannot be more than 100 characters']\n  },\n  dateOccurred: {\n    type: Date,\n    required: [true, 'Date is required'],\n    validate: {\n      validator: function(date) {\n        return date <= new Date();\n      },\n      message: 'Date cannot be in the future'\n    }\n  },\n  contactInfo: {\n    email: {\n      type: String,\n      required: true\n    },\n    phone: {\n      type: String,\n      trim: true\n    },\n    preferredContact: {\n      type: String,\n      enum: ['email', 'phone', 'chat'],\n      default: 'email'\n    }\n  },\n  postedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  isUrgent: {\n    type: Boolean,\n    default: false\n  },\n  reward: {\n    offered: {\n      type: Boolean,\n      default: false\n    },\n    amount: {\n      type: Number,\n      min: 0\n    },\n    description: {\n      type: String,\n      trim: true\n    }\n  },\n  views: {\n    type: Number,\n    default: 0\n  },\n  claimsCount: {\n    type: Number,\n    default: 0\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes for better query performance\nItemSchema.index({ type: 1, status: 1, createdAt: -1 });\nItemSchema.index({ category: 1, type: 1 });\nItemSchema.index({ location: 1, type: 1 });\nItemSchema.index({ postedBy: 1 });\nItemSchema.index({ tags: 1 });\nItemSchema.index({ 'contactInfo.email': 1 });\n\n// Virtual for age of the post\nItemSchema.virtual('age').get(function() {\n  return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days\n});\n\n// Method to increment views\nItemSchema.methods.incrementViews = function() {\n  this.views += 1;\n  return this.save();\n};\n\n// Method to increment claims count\nItemSchema.methods.incrementClaims = function() {\n  this.claimsCount += 1;\n  return this.save();\n};\n\nexport default mongoose.models.Item || mongoose.model('Item', ItemSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAK;SAAiD;IACpE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;YAAC;YAAQ;SAAQ;IACzB;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAU;YAAW;YAAY;SAAW;QACnD,SAAS;IACX;IACA,QAAQ;QAAC;YACP,KAAK;gBACH,MAAM;gBACN,UAAU;YACZ;YACA,UAAU;gBACR,MAAM;gBACN,UAAU;YACZ;QACF;KAAE;IACF,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;QACN,WAAW;YAAC;YAAK;SAA8C;IACjE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,UAAU;YACR,WAAW,SAAS,IAAI;gBACtB,OAAO,QAAQ,IAAI;YACrB;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,OAAO;YACL,MAAM;YACN,UAAU;QACZ;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,kBAAkB;YAChB,MAAM;YACN,MAAM;gBAAC;gBAAS;gBAAS;aAAO;YAChC,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,SAAS;YACP,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,KAAK;QACP;QACA,aAAa;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;IAAG,WAAW,CAAC;AAAE;AACrD,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,MAAM;AAAE;AACxC,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,MAAM;AAAE;AACxC,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,qBAAqB;AAAE;AAE1C,8BAA8B;AAC9B,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC;IAC5B,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,IAAI,OAAO;AACnF;AAEA,4BAA4B;AAC5B,WAAW,OAAO,CAAC,cAAc,GAAG;IAClC,IAAI,CAAC,KAAK,IAAI;IACd,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,eAAe,GAAG;IACnC,IAAI,CAAC,WAAW,IAAI;IACpB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/api/items/%5Bid%5D/view/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/db';\nimport Item from '@/models/Item';\n\nexport async function POST(request, { params }) {\n  try {\n    await connectDB();\n\n    const item = await Item.findById(params.id);\n    \n    if (!item) {\n      return NextResponse.json(\n        { error: 'Item not found' },\n        { status: 404 }\n      );\n    }\n\n    // Increment views\n    item.views = (item.views || 0) + 1;\n    await item.save();\n\n    return NextResponse.json({\n      message: 'View count incremented',\n      views: item.views\n    });\n\n  } catch (error) {\n    console.error('View increment error:', error);\n    return NextResponse.json(\n      { error: 'Failed to increment view count' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAO,EAAE,EAAE,MAAM,EAAE;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;QAE1C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI;QACjC,MAAM,KAAK,IAAI;QAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,KAAK,KAAK;QACnB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}