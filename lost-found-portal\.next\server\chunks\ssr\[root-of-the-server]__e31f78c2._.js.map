{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/providers/AuthProvider.js"], "sourcesContent": ["'use client';\n\nimport { SessionProvider } from 'next-auth/react';\n\nexport function AuthProvider({ children }) {\n  return <SessionProvider>{children}</SessionProvider>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS,aAAa,EAAE,QAAQ,EAAE;IACvC,qBAAO,8OAAC,8IAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/providers/NotificationProvider.js"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\n\nconst NotificationContext = createContext();\n\nconst initialState = {\n  notifications: [],\n  unreadCount: 0,\n  loading: false,\n  error: null\n};\n\nfunction notificationReducer(state, action) {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return { ...state, loading: action.payload };\n    \n    case 'SET_NOTIFICATIONS':\n      return {\n        ...state,\n        notifications: action.payload,\n        loading: false,\n        error: null\n      };\n    \n    case 'ADD_NOTIFICATION':\n      return {\n        ...state,\n        notifications: [action.payload, ...state.notifications],\n        unreadCount: state.unreadCount + 1\n      };\n    \n    case 'MARK_AS_READ':\n      return {\n        ...state,\n        notifications: state.notifications.map(notification =>\n          notification._id === action.payload\n            ? { ...notification, isRead: true, readAt: new Date() }\n            : notification\n        ),\n        unreadCount: Math.max(0, state.unreadCount - 1)\n      };\n    \n    case 'MARK_ALL_AS_READ':\n      return {\n        ...state,\n        notifications: state.notifications.map(notification => ({\n          ...notification,\n          isRead: true,\n          readAt: new Date()\n        })),\n        unreadCount: 0\n      };\n    \n    case 'SET_UNREAD_COUNT':\n      return { ...state, unreadCount: action.payload };\n    \n    case 'SET_ERROR':\n      return { ...state, error: action.payload, loading: false };\n    \n    default:\n      return state;\n  }\n}\n\nexport function NotificationProvider({ children }) {\n  const [state, dispatch] = useReducer(notificationReducer, initialState);\n  const { data: session } = useSession();\n\n  // Fetch notifications\n  const fetchNotifications = async (options = {}) => {\n    if (!session?.user?.id) return;\n\n    dispatch({ type: 'SET_LOADING', payload: true });\n\n    try {\n      const queryParams = new URLSearchParams(options);\n      const response = await fetch(`/api/notifications?${queryParams}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch notifications');\n      }\n\n      const data = await response.json();\n      dispatch({ type: 'SET_NOTIFICATIONS', payload: data.notifications });\n      dispatch({ type: 'SET_UNREAD_COUNT', payload: data.unreadCount });\n    } catch (error) {\n      dispatch({ type: 'SET_ERROR', payload: error.message });\n    }\n  };\n\n  // Mark notification as read\n  const markAsRead = async (notificationId) => {\n    try {\n      const response = await fetch(`/api/notifications/${notificationId}/read`, {\n        method: 'PATCH'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to mark notification as read');\n      }\n\n      dispatch({ type: 'MARK_AS_READ', payload: notificationId });\n    } catch (error) {\n      dispatch({ type: 'SET_ERROR', payload: error.message });\n    }\n  };\n\n  // Mark all notifications as read\n  const markAllAsRead = async () => {\n    try {\n      const response = await fetch('/api/notifications/read-all', {\n        method: 'PATCH'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to mark all notifications as read');\n      }\n\n      dispatch({ type: 'MARK_ALL_AS_READ' });\n    } catch (error) {\n      dispatch({ type: 'SET_ERROR', payload: error.message });\n    }\n  };\n\n  // Add new notification (for real-time updates)\n  const addNotification = (notification) => {\n    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });\n  };\n\n  // Fetch notifications on session change\n  useEffect(() => {\n    if (session?.user?.id) {\n      fetchNotifications();\n    }\n  }, [session?.user?.id]);\n\n  const value = {\n    ...state,\n    fetchNotifications,\n    markAsRead,\n    markAllAsRead,\n    addNotification\n  };\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  );\n}\n\nexport function useNotifications() {\n  const context = useContext(NotificationContext);\n  if (!context) {\n    throw new Error('useNotifications must be used within a NotificationProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAExC,MAAM,eAAe;IACnB,eAAe,EAAE;IACjB,aAAa;IACb,SAAS;IACT,OAAO;AACT;AAEA,SAAS,oBAAoB,KAAK,EAAE,MAAM;IACxC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;YAAC;QAE7C,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,OAAO,OAAO;gBAC7B,SAAS;gBACT,OAAO;YACT;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe;oBAAC,OAAO,OAAO;uBAAK,MAAM,aAAa;iBAAC;gBACvD,aAAa,MAAM,WAAW,GAAG;YACnC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eACrC,aAAa,GAAG,KAAK,OAAO,OAAO,GAC/B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;wBAAM,QAAQ,IAAI;oBAAO,IACpD;gBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;YAC/C;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eAAgB,CAAC;wBACtD,GAAG,YAAY;wBACf,QAAQ;wBACR,QAAQ,IAAI;oBACd,CAAC;gBACD,aAAa;YACf;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QAEjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;gBAAE,SAAS;YAAM;QAE3D;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,EAAE,QAAQ,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB;IAC1D,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,sBAAsB;IACtB,MAAM,qBAAqB,OAAO,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,MAAM,IAAI;QAExB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAK;QAE9C,IAAI;YACF,MAAM,cAAc,IAAI,gBAAgB;YACxC,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,aAAa;YAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS;gBAAE,MAAM;gBAAqB,SAAS,KAAK,aAAa;YAAC;YAClE,SAAS;gBAAE,MAAM;gBAAoB,SAAS,KAAK,WAAW;YAAC;QACjE,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO;YAAC;QACvD;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,eAAe,KAAK,CAAC,EAAE;gBACxE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS;gBAAE,MAAM;gBAAgB,SAAS;YAAe;QAC3D,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO;YAAC;QACvD;IACF;IAEA,iCAAiC;IACjC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS;gBAAE,MAAM;YAAmB;QACtC,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO;YAAC;QACvD;IACF;IAEA,+CAA+C;IAC/C,MAAM,kBAAkB,CAAC;QACvB,SAAS;YAAE,MAAM;YAAoB,SAAS;QAAa;IAC7D;IAEA,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB;QACF;IACF,GAAG;QAAC,SAAS,MAAM;KAAG;IAEtB,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/providers/SocketProvider.js"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { io } from 'socket.io-client';\nimport { useNotifications } from './NotificationProvider';\n\nconst SocketContext = createContext();\n\nexport function SocketProvider({ children }) {\n  const [socket, setSocket] = useState(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const { data: session } = useSession();\n  const { addNotification } = useNotifications();\n\n  useEffect(() => {\n    if (session?.user?.id) {\n      // Initialize socket connection\n      const socketInstance = io(process.env.NEXT_PUBLIC_SOCKET_URL || '', {\n        auth: {\n          userId: session.user.id\n        }\n      });\n\n      socketInstance.on('connect', () => {\n        setIsConnected(true);\n        console.log('Socket connected');\n      });\n\n      socketInstance.on('disconnect', () => {\n        setIsConnected(false);\n        console.log('Socket disconnected');\n      });\n\n      // Listen for new notifications\n      socketInstance.on('new_notification', (notification) => {\n        addNotification(notification);\n        \n        // Show browser notification if permission granted\n        if (Notification.permission === 'granted') {\n          new Notification(notification.title, {\n            body: notification.message,\n            icon: '/favicon.ico'\n          });\n        }\n      });\n\n      // Listen for new messages\n      socketInstance.on('new_message', (message) => {\n        // Handle new chat messages\n        console.log('New message received:', message);\n      });\n\n      // Listen for item updates\n      socketInstance.on('item_updated', (item) => {\n        // Handle item updates\n        console.log('Item updated:', item);\n      });\n\n      // Listen for claim updates\n      socketInstance.on('claim_updated', (claim) => {\n        // Handle claim updates\n        console.log('Claim updated:', claim);\n      });\n\n      setSocket(socketInstance);\n\n      return () => {\n        socketInstance.disconnect();\n      };\n    }\n  }, [session?.user?.id, addNotification]);\n\n  // Request notification permission\n  useEffect(() => {\n    if (typeof window !== 'undefined' && 'Notification' in window) {\n      if (Notification.permission === 'default') {\n        Notification.requestPermission();\n      }\n    }\n  }, []);\n\n  const value = {\n    socket,\n    isConnected,\n    emit: (event, data) => {\n      if (socket) {\n        socket.emit(event, data);\n      }\n    },\n    joinRoom: (roomId) => {\n      if (socket) {\n        socket.emit('join_room', roomId);\n      }\n    },\n    leaveRoom: (roomId) => {\n      if (socket) {\n        socket.emit('leave_room', roomId);\n      }\n    }\n  };\n\n  return (\n    <SocketContext.Provider value={value}>\n      {children}\n    </SocketContext.Provider>\n  );\n}\n\nexport function useSocket() {\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAE3B,SAAS,eAAe,EAAE,QAAQ,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,mBAAgB,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,GAAG,CAAC,sBAAsB,IAAI,IAAI;gBAClE,MAAM;oBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACzB;YACF;YAEA,eAAe,EAAE,CAAC,WAAW;gBAC3B,eAAe;gBACf,QAAQ,GAAG,CAAC;YACd;YAEA,eAAe,EAAE,CAAC,cAAc;gBAC9B,eAAe;gBACf,QAAQ,GAAG,CAAC;YACd;YAEA,+BAA+B;YAC/B,eAAe,EAAE,CAAC,oBAAoB,CAAC;gBACrC,gBAAgB;gBAEhB,kDAAkD;gBAClD,IAAI,aAAa,UAAU,KAAK,WAAW;oBACzC,IAAI,aAAa,aAAa,KAAK,EAAE;wBACnC,MAAM,aAAa,OAAO;wBAC1B,MAAM;oBACR;gBACF;YACF;YAEA,0BAA0B;YAC1B,eAAe,EAAE,CAAC,eAAe,CAAC;gBAChC,2BAA2B;gBAC3B,QAAQ,GAAG,CAAC,yBAAyB;YACvC;YAEA,0BAA0B;YAC1B,eAAe,EAAE,CAAC,gBAAgB,CAAC;gBACjC,sBAAsB;gBACtB,QAAQ,GAAG,CAAC,iBAAiB;YAC/B;YAEA,2BAA2B;YAC3B,eAAe,EAAE,CAAC,iBAAiB,CAAC;gBAClC,uBAAuB;gBACvB,QAAQ,GAAG,CAAC,kBAAkB;YAChC;YAEA,UAAU;YAEV,OAAO;gBACL,eAAe,UAAU;YAC3B;QACF;IACF,GAAG;QAAC,SAAS,MAAM;QAAI;KAAgB;IAEvC,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAkB,eAAe,kBAAkB,QAAQ;;QAI/D;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA;QACA,MAAM,CAAC,OAAO;YACZ,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC,OAAO;YACrB;QACF;QACA,UAAU,CAAC;YACT,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC,aAAa;YAC3B;QACF;QACA,WAAW,CAAC;YACV,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC,cAAc;YAC5B;QACF;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}