{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/db.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/Chat.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MessageSchema = new mongoose.Schema({\n  sender: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  content: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: [1000, 'Message cannot be more than 1000 characters']\n  },\n  messageType: {\n    type: String,\n    enum: ['text', 'image', 'file', 'system'],\n    default: 'text'\n  },\n  attachments: [{\n    url: {\n      type: String,\n      required: true\n    },\n    publicId: {\n      type: String\n    },\n    fileName: {\n      type: String\n    },\n    fileSize: {\n      type: Number\n    },\n    mimeType: {\n      type: String\n    }\n  }],\n  isRead: {\n    type: Boolean,\n    default: false\n  },\n  readAt: {\n    type: Date\n  },\n  isEdited: {\n    type: Boolean,\n    default: false\n  },\n  editedAt: {\n    type: Date\n  }\n}, {\n  timestamps: true\n});\n\nconst ChatSchema = new mongoose.Schema({\n  participants: [{\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  }],\n  relatedItem: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'Item',\n    required: true\n  },\n  relatedClaim: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'Claim'\n  },\n  chatType: {\n    type: String,\n    enum: ['item_inquiry', 'claim_discussion', 'support'],\n    default: 'item_inquiry'\n  },\n  status: {\n    type: String,\n    enum: ['active', 'closed', 'archived'],\n    default: 'active'\n  },\n  messages: [MessageSchema],\n  lastMessage: {\n    content: {\n      type: String\n    },\n    sender: {\n      type: mongoose.Schema.Types.ObjectId,\n      ref: 'User'\n    },\n    timestamp: {\n      type: Date\n    }\n  },\n  unreadCount: {\n    type: Map,\n    of: Number,\n    default: {}\n  },\n  isEncrypted: {\n    type: Boolean,\n    default: false\n  },\n  closedAt: {\n    type: Date\n  },\n  closedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User'\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes\nChatSchema.index({ participants: 1, status: 1 });\nChatSchema.index({ relatedItem: 1 });\nChatSchema.index({ relatedClaim: 1 });\nChatSchema.index({ 'lastMessage.timestamp': -1 });\n\n// Virtual for chat age\nChatSchema.virtual('age').get(function() {\n  return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days\n});\n\n// Method to add message\nChatSchema.methods.addMessage = function(senderId, content, messageType = 'text', attachments = []) {\n  const message = {\n    sender: senderId,\n    content,\n    messageType,\n    attachments\n  };\n  \n  this.messages.push(message);\n  this.lastMessage = {\n    content,\n    sender: senderId,\n    timestamp: new Date()\n  };\n  \n  // Update unread count for other participants\n  this.participants.forEach(participantId => {\n    if (participantId.toString() !== senderId.toString()) {\n      const currentCount = this.unreadCount.get(participantId.toString()) || 0;\n      this.unreadCount.set(participantId.toString(), currentCount + 1);\n    }\n  });\n  \n  return this.save();\n};\n\n// Method to mark messages as read\nChatSchema.methods.markAsRead = function(userId) {\n  this.unreadCount.set(userId.toString(), 0);\n  \n  // Mark unread messages as read\n  this.messages.forEach(message => {\n    if (message.sender.toString() !== userId.toString() && !message.isRead) {\n      message.isRead = true;\n      message.readAt = new Date();\n    }\n  });\n  \n  return this.save();\n};\n\n// Method to close chat\nChatSchema.methods.closeChat = function(userId) {\n  this.status = 'closed';\n  this.closedAt = new Date();\n  this.closedBy = userId;\n  return this.save();\n};\n\n// Static method to find or create chat\nChatSchema.statics.findOrCreateChat = async function(participants, relatedItem, relatedClaim = null) {\n  let chat = await this.findOne({\n    participants: { $all: participants },\n    relatedItem,\n    status: 'active'\n  });\n  \n  if (!chat) {\n    chat = new this({\n      participants,\n      relatedItem,\n      relatedClaim,\n      unreadCount: new Map()\n    });\n    await chat.save();\n  }\n  \n  return chat;\n};\n\nexport default mongoose.models.Chat || mongoose.model('Chat', ChatSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACxC,QAAQ;QACN,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;YAAC;YAAM;SAA8C;IAClE;IACA,aAAa;QACX,MAAM;QACN,MAAM;YAAC;YAAQ;YAAS;YAAQ;SAAS;QACzC,SAAS;IACX;IACA,aAAa;QAAC;YACZ,KAAK;gBACH,MAAM;gBACN,UAAU;YACZ;YACA,UAAU;gBACR,MAAM;YACR;YACA,UAAU;gBACR,MAAM;YACR;YACA,UAAU;gBA<PERSON>,MAAM;YACR;YACA,UAAU;gBACR,MAAM;YACR;QACF;KAAE;IACF,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,cAAc;QAAC;YACb,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;YACL,UAAU;QACZ;KAAE;IACF,aAAa;QACX,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,cAAc;QACZ,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;IACP;IACA,UAAU;QACR,MAAM;QACN,MAAM;YAAC;YAAgB;YAAoB;SAAU;QACrD,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAU;YAAU;SAAW;QACtC,SAAS;IACX;IACA,UAAU;QAAC;KAAc;IACzB,aAAa;QACX,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;QACP;QACA,WAAW;YACT,MAAM;QACR;IACF;IACA,aAAa;QACX,MAAM;QACN,IAAI;QACJ,SAAS,CAAC;IACZ;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;IACR;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;IACP;AACF,GAAG;IACD,YAAY;AACd;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,cAAc;IAAG,QAAQ;AAAE;AAC9C,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,cAAc;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,yBAAyB,CAAC;AAAE;AAE/C,uBAAuB;AACvB,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC;IAC5B,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,IAAI,OAAO;AACnF;AAEA,wBAAwB;AACxB,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,QAAQ,EAAE,OAAO,EAAE,cAAc,MAAM,EAAE,cAAc,EAAE;IAChG,MAAM,UAAU;QACd,QAAQ;QACR;QACA;QACA;IACF;IAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACnB,IAAI,CAAC,WAAW,GAAG;QACjB;QACA,QAAQ;QACR,WAAW,IAAI;IACjB;IAEA,6CAA6C;IAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;QACxB,IAAI,cAAc,QAAQ,OAAO,SAAS,QAAQ,IAAI;YACpD,MAAM,eAAe,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO;YACvE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,QAAQ,IAAI,eAAe;QAChE;IACF;IAEA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,kCAAkC;AAClC,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,MAAM;IAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,QAAQ,IAAI;IAExC,+BAA+B;IAC/B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACpB,IAAI,QAAQ,MAAM,CAAC,QAAQ,OAAO,OAAO,QAAQ,MAAM,CAAC,QAAQ,MAAM,EAAE;YACtE,QAAQ,MAAM,GAAG;YACjB,QAAQ,MAAM,GAAG,IAAI;QACvB;IACF;IAEA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,uBAAuB;AACvB,WAAW,OAAO,CAAC,SAAS,GAAG,SAAS,MAAM;IAC5C,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,QAAQ,GAAG;IAChB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,uCAAuC;AACvC,WAAW,OAAO,CAAC,gBAAgB,GAAG,eAAe,YAAY,EAAE,WAAW,EAAE,eAAe,IAAI;IACjG,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;QAC5B,cAAc;YAAE,MAAM;QAAa;QACnC;QACA,QAAQ;IACV;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,IAAI,CAAC;YACd;YACA;YACA;YACA,aAAa,IAAI;QACnB;QACA,MAAM,KAAK,IAAI;IACjB;IAEA,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    validate: {\n      validator: function(email) {\n        // Validate UMT email domain\n        return email.endsWith('@umt.edu.pk') || email.endsWith('@student.umt.edu.pk');\n      },\n      message: 'Please use a valid UMT email address'\n    }\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters']\n  },\n  role: {\n    type: String,\n    enum: ['student', 'admin'],\n    default: 'student'\n  },\n  studentId: {\n    type: String,\n    sparse: true,\n    unique: true\n  },\n  phone: {\n    type: String,\n    trim: true\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  verificationToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordExpires: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Remove password from JSON output\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.verificationToken;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpires;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,UAAU;YACR,WAAW,SAAS,KAAK;gBACvB,4BAA4B;gBAC5B,OAAO,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC;YACzD;YACA,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;IAC1D;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAW;SAAQ;QAC1B,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,mBAAmB;QACjB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,sBAAsB;QACpB,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,iBAAiB;IACnC,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,oBAAoB;IACtC,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/auth.js"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { getServerSession } from 'next-auth';\nimport connectDB from './db';\nimport User from '@/models/User';\n\nexport const authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error('Email and password are required');\n        }\n\n        await connectDB();\n\n        const user = await User.findOne({ email: credentials.email });\n\n        if (!user) {\n          throw new Error('No user found with this email');\n        }\n\n        const isPasswordValid = await user.comparePassword(credentials.password);\n\n        if (!isPasswordValid) {\n          throw new Error('Invalid password');\n        }\n\n        if (!user.isVerified) {\n          throw new Error('Please verify your email before logging in');\n        }\n\n        return {\n          id: user._id.toString(),\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          studentId: user.studentId,\n          avatar: user.avatar\n        };\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.studentId = user.studentId;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub;\n        session.user.role = token.role;\n        session.user.studentId = token.studentId;\n        session.user.avatar = token.avatar;\n      }\n      return session;\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error'\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n// Utility functions for authentication\nexport const validateUMTEmail = (email) => {\n  const umtDomains = ['@umt.edu.pk', '@student.umt.edu.pk'];\n  return umtDomains.some(domain => email.endsWith(domain));\n};\n\nexport const generateVerificationToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\nexport const generateResetToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\n// Role-based access control\nexport const requireAuth = (handler, requiredRole = null) => {\n  return async (req, res) => {\n    const session = await getServerSession(req, res, authOptions);\n\n    if (!session) {\n      return res.status(401).json({ error: 'Authentication required' });\n    }\n\n    if (requiredRole && session.user.role !== requiredRole) {\n      return res.status(403).json({ error: 'Insufficient permissions' });\n    }\n\n    req.user = session.user;\n    return handler(req, res);\n  };\n};\n\n// Check if user is admin\nexport const isAdmin = (user) => {\n  return user?.role === 'admin';\n};\n\n// Check if user owns the resource\nexport const isOwner = (user, resourceUserId) => {\n  return user?.id === resourceUserId.toString();\n};\n\n// Check if user can access resource\nexport const canAccess = (user, resourceUserId, requiredRole = null) => {\n  if (requiredRole && user?.role !== requiredRole) {\n    return false;\n  }\n\n  return isAdmin(user) || isOwner(user, resourceUserId);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;gBAEd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,YAAY,KAAK;gBAAC;gBAE3D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,YAAY,QAAQ;gBAEvE,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;oBACrB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;oBACzB,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,aAAa;QAAC;QAAe;KAAsB;IACzD,OAAO,WAAW,IAAI,CAAC,CAAA,SAAU,MAAM,QAAQ,CAAC;AAClD;AAEO,MAAM,4BAA4B;IACvC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,MAAM,qBAAqB;IAChC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,MAAM,cAAc,CAAC,SAAS,eAAe,IAAI;IACtD,OAAO,OAAO,KAAK;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,KAAK;QAEjD,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA0B;QACjE;QAEA,IAAI,gBAAgB,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAc;YACtD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA2B;QAClE;QAEA,IAAI,IAAI,GAAG,QAAQ,IAAI;QACvB,OAAO,QAAQ,KAAK;IACtB;AACF;AAGO,MAAM,UAAU,CAAC;IACtB,OAAO,MAAM,SAAS;AACxB;AAGO,MAAM,UAAU,CAAC,MAAM;IAC5B,OAAO,MAAM,OAAO,eAAe,QAAQ;AAC7C;AAGO,MAAM,YAAY,CAAC,MAAM,gBAAgB,eAAe,IAAI;IACjE,IAAI,gBAAgB,MAAM,SAAS,cAAc;QAC/C,OAAO;IACT;IAEA,OAAO,QAAQ,SAAS,QAAQ,MAAM;AACxC", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/api/chat/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport connectDB from '@/lib/db';\nimport Chat from '@/models/Chat';\nimport { authOptions } from '@/lib/auth';\n\nexport async function GET(request) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const chatId = searchParams.get('chatId');\n    const itemId = searchParams.get('itemId');\n    const otherUserId = searchParams.get('otherUserId');\n\n    if (chatId) {\n      // Get specific chat\n      const chat = await Chat.findById(chatId)\n        .populate('participants', 'name avatar email')\n        .populate('relatedItem', 'title type')\n        .lean();\n\n      if (!chat) {\n        return NextResponse.json(\n          { error: 'Chat not found' },\n          { status: 404 }\n        );\n      }\n\n      // Check if user is participant\n      const isParticipant = chat.participants.some(\n        p => p._id.toString() === session.user.id\n      );\n\n      if (!isParticipant) {\n        return NextResponse.json(\n          { error: 'Access denied' },\n          { status: 403 }\n        );\n      }\n\n      return NextResponse.json({ chat });\n    }\n\n    if (itemId && otherUserId) {\n      // Find or create chat for item discussion\n      let chat = await Chat.findOrCreateChat(\n        [session.user.id, otherUserId],\n        itemId\n      );\n\n      await chat.populate([\n        { path: 'participants', select: 'name avatar email' },\n        { path: 'relatedItem', select: 'title type' },\n        { path: 'messages.sender', select: 'name avatar' }\n      ]);\n\n      return NextResponse.json({ chat });\n    }\n\n    // Get all user's chats\n    const chats = await Chat.find({\n      participants: session.user.id,\n      status: 'active'\n    })\n    .populate('participants', 'name avatar email')\n    .populate('relatedItem', 'title type')\n    .sort({ updatedAt: -1 })\n    .lean();\n\n    return NextResponse.json({ chats });\n\n  } catch (error) {\n    console.error('Chat fetch error:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch chat' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    await connectDB();\n\n    const { chatId, message, messageType = 'text' } = await request.json();\n\n    if (!chatId || !message) {\n      return NextResponse.json(\n        { error: 'Chat ID and message are required' },\n        { status: 400 }\n      );\n    }\n\n    const chat = await Chat.findById(chatId);\n\n    if (!chat) {\n      return NextResponse.json(\n        { error: 'Chat not found' },\n        { status: 404 }\n      );\n    }\n\n    // Check if user is participant\n    const isParticipant = chat.participants.some(\n      p => p.toString() === session.user.id\n    );\n\n    if (!isParticipant) {\n      return NextResponse.json(\n        { error: 'Access denied' },\n        { status: 403 }\n      );\n    }\n\n    // Add message using the model method\n    await chat.addMessage(session.user.id, message, messageType);\n\n    // Populate the new message for response\n    await chat.populate('messages.sender', 'name avatar');\n    const populatedMessage = chat.messages[chat.messages.length - 1];\n\n    // Emit real-time message (in production, you'd use actual Socket.IO)\n    if (global.io) {\n      global.io.to(`chat_${chatId}`).emit('new_message', {\n        ...populatedMessage.toObject(),\n        chatId\n      });\n    }\n\n    return NextResponse.json({\n      message: 'Message sent successfully',\n      newMessage: populatedMessage\n    });\n\n  } catch (error) {\n    console.error('Message send error:', error);\n    return NextResponse.json(\n      { error: 'Failed to send message' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,IAAI,QAAQ;YACV,oBAAoB;YACpB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAC9B,QAAQ,CAAC,gBAAgB,qBACzB,QAAQ,CAAC,eAAe,cACxB,IAAI;YAEP,IAAI,CAAC,MAAM;gBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiB,GAC1B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,+BAA+B;YAC/B,MAAM,gBAAgB,KAAK,YAAY,CAAC,IAAI,CAC1C,CAAA,IAAK,EAAE,GAAG,CAAC,QAAQ,OAAO,QAAQ,IAAI,CAAC,EAAE;YAG3C,IAAI,CAAC,eAAe;gBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAgB,GACzB;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAK;QAClC;QAEA,IAAI,UAAU,aAAa;YACzB,0CAA0C;YAC1C,IAAI,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,gBAAgB,CACpC;gBAAC,QAAQ,IAAI,CAAC,EAAE;gBAAE;aAAY,EAC9B;YAGF,MAAM,KAAK,QAAQ,CAAC;gBAClB;oBAAE,MAAM;oBAAgB,QAAQ;gBAAoB;gBACpD;oBAAE,MAAM;oBAAe,QAAQ;gBAAa;gBAC5C;oBAAE,MAAM;oBAAmB,QAAQ;gBAAc;aAClD;YAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAK;QAClC;QAEA,uBAAuB;QACvB,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YAC5B,cAAc,QAAQ,IAAI,CAAC,EAAE;YAC7B,QAAQ;QACV,GACC,QAAQ,CAAC,gBAAgB,qBACzB,QAAQ,CAAC,eAAe,cACxB,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,IAAI;QAEL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IAEnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpE,IAAI,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QAEjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,gBAAgB,KAAK,YAAY,CAAC,IAAI,CAC1C,CAAA,IAAK,EAAE,QAAQ,OAAO,QAAQ,IAAI,CAAC,EAAE;QAGvC,IAAI,CAAC,eAAe;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,qCAAqC;QACrC,MAAM,KAAK,UAAU,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,SAAS;QAEhD,wCAAwC;QACxC,MAAM,KAAK,QAAQ,CAAC,mBAAmB;QACvC,MAAM,mBAAmB,KAAK,QAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,EAAE;QAEhE,qEAAqE;QACrE,IAAI,OAAO,EAAE,EAAE;YACb,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,eAAe;gBACjD,GAAG,iBAAiB,QAAQ,EAAE;gBAC9B;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY;QACd;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}