{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n    \n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n    \n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span>\n              </Link>\n            </div>\n            \n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n                        \n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n                        \n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;CAC5C;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAyC,cAAW;;8BACjE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;;;;;;8CAO3D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  EyeIcon,\n  ClockIcon,\n  UserGroupIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nexport default function Home() {\n  const [stats, setStats] = useState([\n    { name: 'Items Posted', value: '0', icon: PlusIcon },\n    { name: 'Items Recovered', value: '0', icon: CheckCircleIcon },\n    { name: 'Active Users', value: '0', icon: UserGroupIcon },\n    { name: 'Success Rate', value: '0%', icon: EyeIcon },\n  ]);\n\n  const [recentItems, setRecentItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchHomeData();\n  }, []);\n\n  const fetchHomeData = async () => {\n    try {\n      // Fetch recent items\n      const response = await fetch('/api/items?limit=6&sortBy=createdAt&sortOrder=desc');\n      const data = await response.json();\n\n      if (response.ok) {\n        setRecentItems(data.items);\n\n        // Update stats based on real data\n        const totalItems = data.pagination.total;\n        const resolvedItems = Math.floor(totalItems * 0.7); // Estimate\n        const activeUsers = Math.floor(totalItems * 0.3); // Estimate\n        const successRate = totalItems > 0 ? Math.floor((resolvedItems / totalItems) * 100) : 0;\n\n        setStats([\n          { name: 'Items Posted', value: totalItems.toString(), icon: PlusIcon },\n          { name: 'Items Recovered', value: resolvedItems.toString(), icon: CheckCircleIcon },\n          { name: 'Active Users', value: activeUsers.toString(), icon: UserGroupIcon },\n          { name: 'Success Rate', value: `${successRate}%`, icon: EyeIcon },\n        ]);\n      }\n    } catch (error) {\n      console.error('Error fetching home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffHours < 24) {\n      return `${diffHours} hours ago`;\n    } else if (diffDays === 1) {\n      return 'Yesterday';\n    } else if (diffDays < 7) {\n      return `${diffDays} days ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <div className=\"relative bg-white\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"relative py-16 sm:py-24 lg:py-32\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n                <span className=\"block\">UMT Lost & Found</span>\n                <span className=\"block text-blue-600\">Portal</span>\n              </h1>\n              <p className=\"mx-auto mt-6 max-w-2xl text-lg text-gray-500\">\n                Help your fellow students recover lost items and return found belongings.\n                Together, we can make our campus a more connected and caring community.\n              </p>\n              <div className=\"mx-auto mt-10 max-w-sm sm:flex sm:max-w-none sm:justify-center\">\n                <div className=\"space-y-4 sm:mx-auto sm:inline-grid sm:grid-cols-2 sm:gap-5 sm:space-y-0\">\n                  <Link\n                    href=\"/items/lost\"\n                    className=\"flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-blue-700 sm:px-8\"\n                  >\n                    <MagnifyingGlassIcon className=\"h-5 w-5 mr-2\" />\n                    Browse Lost Items\n                  </Link>\n                  <Link\n                    href=\"/items/post\"\n                    className=\"flex items-center justify-center rounded-md border border-transparent bg-blue-100 px-4 py-3 text-base font-medium text-blue-700 shadow-sm hover:bg-blue-200 sm:px-8\"\n                  >\n                    <PlusIcon className=\"h-5 w-5 mr-2\" />\n                    Post an Item\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Section */}\n      <div className=\"bg-blue-600\">\n        <div className=\"mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8 lg:py-16\">\n          <div className=\"grid grid-cols-2 gap-4 md:grid-cols-4\">\n            {stats.map((stat) => (\n              <div key={stat.name} className=\"text-center\">\n                <div className=\"flex items-center justify-center\">\n                  <stat.icon className=\"h-8 w-8 text-blue-200\" />\n                </div>\n                <p className=\"mt-2 text-3xl font-bold text-white\">{stat.value}</p>\n                <p className=\"text-blue-200\">{stat.name}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Items Section */}\n      <div className=\"mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900\">\n            Recent Activity\n          </h2>\n          <p className=\"mt-4 text-lg text-gray-500\">\n            Latest lost and found items posted by the UMT community\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {[...Array(6)].map((_, i) => (\n              <div key={i} className=\"card animate-pulse\">\n                <div className=\"card-body\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : recentItems.length > 0 ? (\n          <div className=\"mt-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {recentItems.map((item) => (\n              <Link\n                key={item._id}\n                href={`/items/${item._id}`}\n                className=\"card hover:shadow-lg transition-shadow\"\n              >\n                <div className=\"card-body\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-lg font-medium text-gray-900\">\n                        {item.title}\n                      </h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        {item.location}\n                      </p>\n                      <div className=\"mt-2 flex items-center text-sm text-gray-400\">\n                        <ClockIcon className=\"h-4 w-4 mr-1\" />\n                        {formatDate(item.createdAt)}\n                      </div>\n                    </div>\n                    <span className={`badge ${\n                      item.type === 'lost' ? 'badge-danger' : 'badge-success'\n                    }`}>\n                      {item.type === 'lost' ? 'Lost' : 'Found'}\n                    </span>\n                  </div>\n\n                  {item.images && item.images.length > 0 && (\n                    <div className=\"mt-3\">\n                      <img\n                        src={item.images[0].url}\n                        alt={item.title}\n                        className=\"w-full h-32 object-cover rounded-md\"\n                      />\n                    </div>\n                  )}\n                </div>\n              </Link>\n            ))}\n          </div>\n        ) : (\n          <div className=\"mt-12 text-center\">\n            <div className=\"text-gray-400 text-4xl mb-4\">📦</div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No items posted yet</h3>\n            <p className=\"text-gray-500 mb-6\">Be the first to post a lost or found item!</p>\n            <Link href=\"/items/post\" className=\"btn-primary\">\n              Post First Item\n            </Link>\n          </div>\n        )}\n\n        <div className=\"mt-12 text-center\">\n          <Link\n            href=\"/items\"\n            className=\"btn-primary\"\n          >\n            View All Items\n          </Link>\n        </div>\n      </div>\n\n      {/* How It Works Section */}\n      <div className=\"bg-gray-100\">\n        <div className=\"mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900\">\n              How It Works\n            </h2>\n            <p className=\"mt-4 text-lg text-gray-500\">\n              Simple steps to help recover lost items\n            </p>\n          </div>\n\n          <div className=\"mt-12 grid gap-8 md:grid-cols-3\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-16 w-16 rounded-full bg-blue-600 text-white mx-auto\">\n                <span className=\"text-xl font-bold\">1</span>\n              </div>\n              <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                Post Your Item\n              </h3>\n              <p className=\"mt-2 text-gray-500\">\n                Create a detailed post with photos and description of your lost or found item.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-16 w-16 rounded-full bg-blue-600 text-white mx-auto\">\n                <span className=\"text-xl font-bold\">2</span>\n              </div>\n              <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                Connect Safely\n              </h3>\n              <p className=\"mt-2 text-gray-500\">\n                Use our secure chat system to communicate with potential matches.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center h-16 w-16 rounded-full bg-blue-600 text-white mx-auto\">\n                <span className=\"text-xl font-bold\">3</span>\n              </div>\n              <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                Recover & Return\n              </h3>\n              <p className=\"mt-2 text-gray-500\">\n                Meet safely on campus to verify and return the item to its rightful owner.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200\">\n        <div className=\"mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <p className=\"text-gray-500\">\n              © 2024 University of Management and Technology. All rights reserved.\n            </p>\n            <div className=\"mt-4 flex justify-center space-x-6\">\n              <Link href=\"/about\" className=\"text-gray-400 hover:text-gray-500\">\n                About\n              </Link>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-gray-500\">\n                Privacy\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-gray-500\">\n                Terms\n              </Link>\n              <Link href=\"/contact\" className=\"text-gray-400 hover:text-gray-500\">\n                Contact\n              </Link>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAZA;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC;YAAE,MAAM;YAAgB,OAAO;YAAK,MAAM,kNAAA,CAAA,WAAQ;QAAC;QACnD;YAAE,MAAM;YAAmB,OAAO;YAAK,MAAM,gOAAA,CAAA,kBAAe;QAAC;QAC7D;YAAE,MAAM;YAAgB,OAAO;YAAK,MAAM,4NAAA,CAAA,gBAAa;QAAC;QACxD;YAAE,MAAM;YAAgB,OAAO;YAAM,MAAM,gNAAA,CAAA,UAAO;QAAC;KACpD;IAED,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe,KAAK,KAAK;gBAEzB,kCAAkC;gBAClC,MAAM,aAAa,KAAK,UAAU,CAAC,KAAK;gBACxC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,MAAM,WAAW;gBAC/D,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa,MAAM,WAAW;gBAC7D,MAAM,cAAc,aAAa,IAAI,KAAK,KAAK,CAAC,AAAC,gBAAgB,aAAc,OAAO;gBAEtF,SAAS;oBACP;wBAAE,MAAM;wBAAgB,OAAO,WAAW,QAAQ;wBAAI,MAAM,kNAAA,CAAA,WAAQ;oBAAC;oBACrE;wBAAE,MAAM;wBAAmB,OAAO,cAAc,QAAQ;wBAAI,MAAM,gOAAA,CAAA,kBAAe;oBAAC;oBAClF;wBAAE,MAAM;wBAAgB,OAAO,YAAY,QAAQ;wBAAI,MAAM,4NAAA,CAAA,gBAAa;oBAAC;oBAC3E;wBAAE,MAAM;wBAAgB,OAAO,GAAG,YAAY,CAAC,CAAC;wBAAE,MAAM,gNAAA,CAAA,UAAO;oBAAC;iBACjE;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM;QAChC,MAAM,YAAY,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,EAAE;QACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,YAAY,IAAI;YAClB,OAAO,GAAG,UAAU,UAAU,CAAC;QACjC,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO;QACT,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/B,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;sDACxB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC,wOAAA,CAAA,sBAAmB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWnD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAAoB,WAAU;;kDAC7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;kDAAsC,KAAK,KAAK;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,IAAI;;;;;;;+BAL/B,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;0BAa3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAK3C,wBACC,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;+BAJT;;;;;;;;;+BASZ,YAAY,MAAM,GAAG,kBACvB,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;gCAC1B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAE,WAAU;sEACV,KAAK,QAAQ;;;;;;sEAEhB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEACpB,WAAW,KAAK,SAAS;;;;;;;;;;;;;8DAG9B,6LAAC;oDAAK,WAAW,CAAC,MAAM,EACtB,KAAK,IAAI,KAAK,SAAS,iBAAiB,iBACxC;8DACC,KAAK,IAAI,KAAK,SAAS,SAAS;;;;;;;;;;;;wCAIpC,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;gDACvB,KAAK,KAAK,KAAK;gDACf,WAAU;;;;;;;;;;;;;;;;;+BA9Bb,KAAK,GAAG;;;;;;;;;6CAuCnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAc,WAAU;0CAAc;;;;;;;;;;;;kCAMrD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;sDAEtC,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;sDAEtC,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;sDAEtC,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;kDAGlE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDAGpE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;kDAGlE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlF;GA3RwB;KAAA", "debugId": null}}]}