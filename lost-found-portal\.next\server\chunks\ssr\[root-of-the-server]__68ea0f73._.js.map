{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/auth/signup/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\nexport default function SignUp() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    studentId: '',\n    phone: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const router = useRouter();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const validateForm = () => {\n    if (!formData.name || !formData.email || !formData.password) {\n      setError('Please fill in all required fields');\n      return false;\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n\n    if (!formData.email.endsWith('@umt.edu.pk') && !formData.email.endsWith('@student.umt.edu.pk')) {\n      setError('Please use a valid UMT email address');\n      return false;\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    if (!validateForm()) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/auth/signup', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          studentId: formData.studentId,\n          phone: formData.phone\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setError(data.error || 'Something went wrong');\n      } else {\n        setSuccess('Account created successfully! You can now sign in.');\n        setTimeout(() => {\n          router.push('/auth/signin');\n        }, 2000);\n      }\n    } catch (error) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">LF</span>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link\n              href=\"/auth/signin\"\n              className=\"font-medium text-blue-600 hover:text-blue-500\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          {success && (\n            <div className=\"rounded-md bg-green-50 p-4\">\n              <div className=\"text-sm text-green-700\">{success}</div>\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Full Name *\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                className=\"form-input mt-1\"\n                placeholder=\"Enter your full name\"\n                value={formData.name}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                UMT Email Address *\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                className=\"form-input mt-1\"\n                placeholder=\"<EMAIL>\"\n                value={formData.email}\n                onChange={handleChange}\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">\n                Use your official UMT email address\n              </p>\n            </div>\n\n            <div>\n              <label htmlFor=\"studentId\" className=\"block text-sm font-medium text-gray-700\">\n                Student ID (Optional)\n              </label>\n              <input\n                id=\"studentId\"\n                name=\"studentId\"\n                type=\"text\"\n                className=\"form-input mt-1\"\n                placeholder=\"e.g., FA21-BSE-123\"\n                value={formData.studentId}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n                Phone Number (Optional)\n              </label>\n              <input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                className=\"form-input mt-1\"\n                placeholder=\"+92 300 1234567\"\n                value={formData.phone}\n                onChange={handleChange}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password *\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  className=\"form-input pr-10\"\n                  placeholder=\"Create a strong password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password *\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  required\n                  className=\"form-input pr-10\"\n                  placeholder=\"Confirm your password\"\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                >\n                  {showConfirmPassword ? (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary w-full justify-center\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Creating account...\n                </div>\n              ) : (\n                'Create account'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <Link\n              href=\"/\"\n              className=\"text-sm text-gray-600 hover:text-gray-500\"\n            >\n              ← Back to home\n            </Link>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,WAAW;QACX,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YAC3D,SAAS;YACT,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,SAAS,KAAK,CAAC,QAAQ,CAAC,wBAAwB;YAC9F,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI,CAAC,gBAAgB;YACnB,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,WAAW,SAAS,SAAS;oBAC7B,OAAO,SAAS,KAAK;gBACvB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;YACzB,OAAO;gBACL,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;wBACxC,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;wBAI1C,yBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,IAAI;4CACpB,UAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,KAAK;4CACrB,UAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA0C;;;;;;sDAG/E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,SAAS;4CACzB,UAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,KAAK;4CACrB,UAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,8OAAC,6MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMhC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,sBAAsB,SAAS;oDACrC,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,eAAe;oDAC/B,UAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,uBAAuB,CAAC;8DAEtC,oCACC,8OAAC,6MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOlC,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,wBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAIxF;;;;;;;;;;;sCAKN,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}