{"name": "lost-found-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@next-auth/mongodb-adapter": "^1.1.3", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "date-fns": "^4.1.0", "mongoose": "^8.15.1", "multer": "^2.0.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4"}}