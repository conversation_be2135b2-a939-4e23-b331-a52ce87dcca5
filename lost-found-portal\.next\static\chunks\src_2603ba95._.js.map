{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n  { name: 'Messages', href: '/chat/list' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n\n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n\n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto  px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex mx-auto h-16 w-[90%] items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                {/* <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span> */}\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n\n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n\n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAY,MAAM;IAAa;CACxC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAgC,cAAW;;8BACxD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;8CASrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/items/%5Bid%5D/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport {\n  MapPinIcon,\n  CalendarIcon,\n  TagIcon,\n  EyeIcon,\n  ChatBubbleLeftRightIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  PhoneIcon,\n  EnvelopeIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nexport default function ItemDetail() {\n  const { id } = useParams();\n  const { data: session } = useSession();\n  const router = useRouter();\n  const [item, setItem] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedImage, setSelectedImage] = useState(0);\n\n  useEffect(() => {\n    if (id) {\n      fetchItem();\n    }\n  }, [id]);\n\n  const fetchItem = async () => {\n    try {\n      const response = await fetch(`/api/items/${id}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setItem(data.item);\n        // Increment view count\n        incrementViews();\n      } else {\n        setError(data.error || 'Item not found');\n      }\n    } catch (error) {\n      setError('Failed to load item');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const incrementViews = async () => {\n    try {\n      await fetch(`/api/items/${id}/view`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.error('Failed to increment views:', error);\n    }\n  };\n\n  const handleClaim = () => {\n    if (!session) {\n      router.push('/auth/signin');\n      return;\n    }\n    router.push(`/items/${id}/claim`);\n  };\n\n  const handleContact = () => {\n    if (!session) {\n      router.push('/auth/signin');\n      return;\n    }\n    router.push(`/chat?item=${id}&user=${item.postedBy._id}`);\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-4\"></div>\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              <div className=\"h-64 bg-gray-200 rounded\"></div>\n              <div className=\"space-y-4\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                <div className=\"h-20 bg-gray-200 rounded\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !item) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Item Not Found</h1>\n            <p className=\"text-gray-600 mb-8\">{error}</p>\n            <Link href=\"/items\" className=\"btn-primary\">\n              Browse Items\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <nav className=\"flex mb-8\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-4\">\n            <li>\n              <Link href=\"/\" className=\"text-gray-400 hover:text-gray-500\">\n                Home\n              </Link>\n            </li>\n            <li>\n              <span className=\"text-gray-400\">/</span>\n            </li>\n            <li>\n              <Link\n                href={`/items/${item.type}`}\n                className=\"text-gray-400 hover:text-gray-500\"\n              >\n                {item.type === 'lost' ? 'Lost Items' : 'Found Items'}\n              </Link>\n            </li>\n            <li>\n              <span className=\"text-gray-400\">/</span>\n            </li>\n            <li>\n              <span className=\"text-gray-900\">{item.title}</span>\n            </li>\n          </ol>\n        </nav>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Images */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                {item.images && item.images.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {/* Main Image */}\n                    <div className=\"aspect-w-16 aspect-h-12\">\n                      <img\n                        src={item.images[selectedImage]?.url || '/placeholder-image.jpg'}\n                        alt={item.title}\n                        className=\"w-full h-96 object-cover rounded-lg\"\n                      />\n                    </div>\n\n                    {/* Thumbnail Images */}\n                    {item.images.length > 1 && (\n                      <div className=\"flex space-x-2 overflow-x-auto\">\n                        {item.images.map((image, index) => (\n                          <button\n                            key={index}\n                            onClick={() => setSelectedImage(index)}\n                            className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${\n                              selectedImage === index\n                                ? 'border-blue-500'\n                                : 'border-gray-200 hover:border-gray-300'\n                            }`}\n                          >\n                            <img\n                              src={image.url}\n                              alt={`${item.title} ${index + 1}`}\n                              className=\"w-full h-full object-cover\"\n                            />\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"aspect-w-16 aspect-h-12 bg-gray-100 rounded-lg flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"text-4xl text-gray-400 mb-2\">📷</div>\n                      <p className=\"text-gray-500\">No images available</p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Description */}\n            <div className=\"card mt-6\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Description</h3>\n              </div>\n              <div className=\"card-body\">\n                <p className=\"text-gray-700 whitespace-pre-wrap\">{item.description}</p>\n\n                {item.tags && item.tags.length > 0 && (\n                  <div className=\"mt-4\">\n                    <div className=\"flex items-center mb-2\">\n                      <TagIcon className=\"h-4 w-4 text-gray-400 mr-1\" />\n                      <span className=\"text-sm font-medium text-gray-700\">Tags:</span>\n                    </div>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {item.tags.map((tag, index) => (\n                        <span\n                          key={index}\n                          className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\"\n                        >\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Item Details & Actions */}\n          <div className=\"space-y-6\">\n            {/* Item Info */}\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <h1 className=\"text-2xl font-bold text-gray-900\">{item.title}</h1>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className={`badge ${\n                      item.type === 'lost' ? 'badge-danger' : 'badge-success'\n                    }`}>\n                      {item.type === 'lost' ? 'Lost' : 'Found'}\n                    </span>\n                    {item.isUrgent && (\n                      <span className=\"badge badge-warning\">\n                        <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                        Urgent\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <MapPinIcon className=\"h-4 w-4 mr-2\" />\n                    <span>{item.type === 'lost' ? 'Lost at' : 'Found at'}: {item.location}</span>\n                  </div>\n\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                    <span>{item.type === 'lost' ? 'Lost on' : 'Found on'}: {formatDate(item.dateOccurred)}</span>\n                  </div>\n\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <EyeIcon className=\"h-4 w-4 mr-2\" />\n                    <span>{item.views || 0} views</span>\n                  </div>\n\n                  <div className=\"pt-2 border-t border-gray-200\">\n                    <span className=\"badge badge-info\">{item.category}</span>\n                  </div>\n                </div>\n\n                {item.reward?.offered && (\n                  <div className=\"mt-4 p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <span className=\"text-green-600 font-medium\">💰 Reward Offered</span>\n                    </div>\n                    {item.reward.amount && (\n                      <p className=\"text-sm text-green-700 mt-1\">\n                        Amount: PKR {item.reward.amount}\n                      </p>\n                    )}\n                    {item.reward.description && (\n                      <p className=\"text-sm text-green-700 mt-1\">\n                        {item.reward.description}\n                      </p>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Posted By */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Posted By</h3>\n              </div>\n              <div className=\"card-body\">\n                <div className=\"flex items-center\">\n                  {item.postedBy.avatar ? (\n                    <img\n                      src={item.postedBy.avatar}\n                      alt={item.postedBy.name}\n                      className=\"h-10 w-10 rounded-full\"\n                    />\n                  ) : (\n                    <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                      <span className=\"text-sm font-medium text-gray-700\">\n                        {item.postedBy.name.charAt(0)}\n                      </span>\n                    </div>\n                  )}\n                  <div className=\"ml-3\">\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      {item.postedBy.name}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      Posted {formatDate(item.createdAt)}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Actions */}\n            {session && session.user.id !== item.postedBy._id && (\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Actions</h3>\n                </div>\n                <div className=\"card-body space-y-3\">\n                  {item.type === 'found' ? (\n                    <button\n                      onClick={handleClaim}\n                      className=\"btn-primary w-full\"\n                    >\n                      <CheckCircleIcon className=\"h-4 w-4 mr-2\" />\n                      Claim This Item\n                    </button>\n                  ) : (\n                    <button\n                      onClick={handleContact}\n                      className=\"btn-primary w-full\"\n                    >\n                      <ChatBubbleLeftRightIcon className=\"h-4 w-4 mr-2\" />\n                      I Found This Item\n                    </button>\n                  )}\n\n                  <button\n                    onClick={() => router.push(`/chat?itemId=${id}&otherUserId=${item.postedBy._id}`)}\n                    className=\"btn-secondary w-full\"\n                  >\n                    <ChatBubbleLeftRightIcon className=\"h-4 w-4 mr-2\" />\n                    Send Message\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Contact Info (if available) */}\n            {item.contactInfo && (\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Contact Information</h3>\n                </div>\n                <div className=\"card-body space-y-2\">\n                  {item.contactInfo.email && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <EnvelopeIcon className=\"h-4 w-4 mr-2\" />\n                      <a\n                        href={`mailto:${item.contactInfo.email}`}\n                        className=\"text-blue-600 hover:text-blue-500\"\n                      >\n                        {item.contactInfo.email}\n                      </a>\n                    </div>\n                  )}\n\n                  {item.contactInfo.phone && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <PhoneIcon className=\"h-4 w-4 mr-2\" />\n                      <a\n                        href={`tel:${item.contactInfo.phone}`}\n                        className=\"text-blue-600 hover:text-blue-500\"\n                      >\n                        {item.contactInfo.phone}\n                      </a>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAjBA;;;;;;;AAmBe,SAAS;;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,IAAI;gBACN;YACF;QACF;+BAAG;QAAC;KAAG;IAEP,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,IAAI;gBACjB,uBAAuB;gBACvB;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,MAAM,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,EAAE;gBACnC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QACA,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC;IAClC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QACA,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE;IAC1D;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO7B;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;IAOtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;wBAAY,cAAW;kCACpC,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAoC;;;;;;;;;;;8CAI/D,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;wCAC3B,WAAU;kDAET,KAAK,IAAI,KAAK,SAAS,eAAe;;;;;;;;;;;8CAG3C,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAiB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,kBACnC,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK,KAAK,MAAM,CAAC,cAAc,EAAE,OAAO;4DACxC,KAAK,KAAK,KAAK;4DACf,WAAU;;;;;;;;;;;oDAKb,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,6LAAC;wDAAI,WAAU;kEACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;gEAEC,SAAS,IAAM,iBAAiB;gEAChC,WAAW,CAAC,4DAA4D,EACtE,kBAAkB,QACd,oBACA,yCACJ;0EAEF,cAAA,6LAAC;oEACC,KAAK,MAAM,GAAG;oEACd,KAAK,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG;oEACjC,WAAU;;;;;;+DAXP;;;;;;;;;;;;;;;qEAmBf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8B;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,KAAK,WAAW;;;;;;oDAEjE,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,gNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAEtD,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAcrB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC,KAAK,KAAK;;;;;;sEAC5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,MAAM,EACtB,KAAK,IAAI,KAAK,SAAS,iBAAiB,iBACxC;8EACC,KAAK,IAAI,KAAK,SAAS,SAAS;;;;;;gEAElC,KAAK,QAAQ,kBACZ,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,gPAAA,CAAA,0BAAuB;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;8DAO5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;;wEAAM,KAAK,IAAI,KAAK,SAAS,YAAY;wEAAW;wEAAG,KAAK,QAAQ;;;;;;;;;;;;;sEAGvE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,0NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;8EACxB,6LAAC;;wEAAM,KAAK,IAAI,KAAK,SAAS,YAAY;wEAAW;wEAAG,WAAW,KAAK,YAAY;;;;;;;;;;;;;sEAGtF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;;wEAAM,KAAK,KAAK,IAAI;wEAAE;;;;;;;;;;;;;sEAGzB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAoB,KAAK,QAAQ;;;;;;;;;;;;;;;;;gDAIpD,KAAK,MAAM,EAAE,yBACZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;wDAE9C,KAAK,MAAM,CAAC,MAAM,kBACjB,6LAAC;4DAAE,WAAU;;gEAA8B;gEAC5B,KAAK,MAAM,CAAC,MAAM;;;;;;;wDAGlC,KAAK,MAAM,CAAC,WAAW,kBACtB,6LAAC;4DAAE,WAAU;sEACV,KAAK,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDASpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,QAAQ,CAAC,MAAM,iBACnB,6LAAC;4DACC,KAAK,KAAK,QAAQ,CAAC,MAAM;4DACzB,KAAK,KAAK,QAAQ,CAAC,IAAI;4DACvB,WAAU;;;;;iFAGZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;sEAIjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ,CAAC,IAAI;;;;;;8EAErB,6LAAC;oEAAE,WAAU;;wEAAwB;wEAC3B,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ1C,WAAW,QAAQ,IAAI,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,GAAG,kBAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,KAAK,wBACb,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC,gOAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;4DAAiB;;;;;;6EAI9C,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC,gPAAA,CAAA,0BAAuB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAKxD,6LAAC;wDACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,GAAG,aAAa,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE;wDAChF,WAAU;;0EAEV,6LAAC,gPAAA,CAAA,0BAAuB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;oCAQ3D,KAAK,WAAW,kBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,WAAW,CAAC,KAAK,kBACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;gEACC,MAAM,CAAC,OAAO,EAAE,KAAK,WAAW,CAAC,KAAK,EAAE;gEACxC,WAAU;0EAET,KAAK,WAAW,CAAC,KAAK;;;;;;;;;;;;oDAK5B,KAAK,WAAW,CAAC,KAAK,kBACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEACC,MAAM,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC,KAAK,EAAE;gEACrC,WAAU;0EAET,KAAK,WAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/C;GApYwB;;QACP,qIAAA,CAAA,YAAS;QACE,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}