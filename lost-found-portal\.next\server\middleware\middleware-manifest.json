{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "57bd321bd43e154ff132ba9989d2f0e8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "294a2e402867bbe8ac66bb5d12b66cc9dee1aa29a3feb52c4aa157318f83730c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f05f5771dffb2dd258fbd6ffd6a7d2e917fc744616f48ae75332767dc020dbe8"}}}, "instrumentation": null, "functions": {}}