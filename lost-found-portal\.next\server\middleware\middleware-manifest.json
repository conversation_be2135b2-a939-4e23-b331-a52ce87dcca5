{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "78fc854fac90e140743d34659d0286e3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "017eaa84657435c8aab25c209ee268cd0f142909cd35d8b104c10e89c202a681", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d511e9db6a5ded2566d8647db24b94415b7a85e28e9c57212afc3be099aa87e7"}}}, "instrumentation": null, "functions": {}}