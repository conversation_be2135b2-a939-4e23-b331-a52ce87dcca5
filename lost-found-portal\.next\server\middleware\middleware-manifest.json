{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "6d66ab25ff66c313b7db6bf5f6ed0957", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1155c6a784317049d73f74c48ff8c90593275c3d37c01dbc1c80e0ab1136270f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "79bdf2449537d4900fd019f8d9547ccae9f316f1a320599abdcd76a0d26f1f21"}}}, "instrumentation": null, "functions": {}}