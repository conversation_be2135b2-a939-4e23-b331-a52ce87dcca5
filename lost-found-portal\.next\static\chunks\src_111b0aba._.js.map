{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n  { name: 'Messages', href: '/chat/list' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n\n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n\n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto  px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex mx-auto h-16 w-[90%] items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                {/* <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span> */}\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          {/* <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-[85%] pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div> */}\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n\n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n\n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAY,MAAM;IAAa;CACxC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAgC,cAAW;;8BACxD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;8CASrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCA0BxB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/imageSimilarity.js"], "sourcesContent": ["// Image Similarity Service using Canvas API and basic computer vision techniques\n// This is a client-side implementation that works without external AI services\n\nexport class ImageSimilarityService {\n  constructor() {\n    this.canvas = null;\n    this.ctx = null;\n    this.initCanvas();\n  }\n\n  initCanvas() {\n    if (typeof window !== 'undefined') {\n      this.canvas = document.createElement('canvas');\n      this.ctx = this.canvas.getContext('2d');\n    }\n  }\n\n  // Convert image to a standardized format for comparison\n  async imageToCanvas(imageUrl, size = 64) {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      img.crossOrigin = 'anonymous';\n      \n      img.onload = () => {\n        this.canvas.width = size;\n        this.canvas.height = size;\n        \n        // Draw image scaled to standard size\n        this.ctx.drawImage(img, 0, 0, size, size);\n        \n        // Get image data\n        const imageData = this.ctx.getImageData(0, 0, size, size);\n        resolve(imageData);\n      };\n      \n      img.onerror = reject;\n      img.src = imageUrl;\n    });\n  }\n\n  // Extract color histogram from image\n  extractColorHistogram(imageData) {\n    const data = imageData.data;\n    const histogram = {\n      red: new Array(256).fill(0),\n      green: new Array(256).fill(0),\n      blue: new Array(256).fill(0)\n    };\n\n    for (let i = 0; i < data.length; i += 4) {\n      histogram.red[data[i]]++;\n      histogram.green[data[i + 1]]++;\n      histogram.blue[data[i + 2]]++;\n    }\n\n    return histogram;\n  }\n\n  // Extract dominant colors\n  extractDominantColors(imageData, numColors = 5) {\n    const data = imageData.data;\n    const colorMap = new Map();\n\n    // Sample every 4th pixel for performance\n    for (let i = 0; i < data.length; i += 16) {\n      const r = Math.floor(data[i] / 32) * 32;\n      const g = Math.floor(data[i + 1] / 32) * 32;\n      const b = Math.floor(data[i + 2] / 32) * 32;\n      const color = `${r},${g},${b}`;\n      \n      colorMap.set(color, (colorMap.get(color) || 0) + 1);\n    }\n\n    // Sort by frequency and return top colors\n    return Array.from(colorMap.entries())\n      .sort((a, b) => b[1] - a[1])\n      .slice(0, numColors)\n      .map(([color, count]) => {\n        const [r, g, b] = color.split(',').map(Number);\n        return { r, g, b, count };\n      });\n  }\n\n  // Calculate edge density (simple edge detection)\n  calculateEdgeDensity(imageData) {\n    const data = imageData.data;\n    const width = imageData.width;\n    const height = imageData.height;\n    let edgeCount = 0;\n\n    for (let y = 1; y < height - 1; y++) {\n      for (let x = 1; x < width - 1; x++) {\n        const idx = (y * width + x) * 4;\n        \n        // Convert to grayscale\n        const current = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;\n        const right = (data[idx + 4] + data[idx + 5] + data[idx + 6]) / 3;\n        const bottom = (data[idx + width * 4] + data[idx + width * 4 + 1] + data[idx + width * 4 + 2]) / 3;\n        \n        // Simple edge detection\n        const edgeStrength = Math.abs(current - right) + Math.abs(current - bottom);\n        if (edgeStrength > 30) {\n          edgeCount++;\n        }\n      }\n    }\n\n    return edgeCount / (width * height);\n  }\n\n  // Extract comprehensive features from image\n  async extractFeatures(imageUrl) {\n    try {\n      const imageData = await this.imageToCanvas(imageUrl);\n      \n      const features = {\n        colorHistogram: this.extractColorHistogram(imageData),\n        dominantColors: this.extractDominantColors(imageData),\n        edgeDensity: this.calculateEdgeDensity(imageData),\n        brightness: this.calculateBrightness(imageData),\n        contrast: this.calculateContrast(imageData)\n      };\n\n      return features;\n    } catch (error) {\n      console.error('Error extracting features:', error);\n      return null;\n    }\n  }\n\n  calculateBrightness(imageData) {\n    const data = imageData.data;\n    let total = 0;\n    let count = 0;\n\n    for (let i = 0; i < data.length; i += 4) {\n      total += (data[i] + data[i + 1] + data[i + 2]) / 3;\n      count++;\n    }\n\n    return total / count;\n  }\n\n  calculateContrast(imageData) {\n    const data = imageData.data;\n    const brightness = this.calculateBrightness(imageData);\n    let variance = 0;\n    let count = 0;\n\n    for (let i = 0; i < data.length; i += 4) {\n      const pixelBrightness = (data[i] + data[i + 1] + data[i + 2]) / 3;\n      variance += Math.pow(pixelBrightness - brightness, 2);\n      count++;\n    }\n\n    return Math.sqrt(variance / count);\n  }\n\n  // Compare two sets of features and return similarity score (0-1)\n  compareFeatures(features1, features2) {\n    if (!features1 || !features2) return 0;\n\n    let totalScore = 0;\n    let weights = 0;\n\n    // Compare color histograms\n    const colorScore = this.compareColorHistograms(features1.colorHistogram, features2.colorHistogram);\n    totalScore += colorScore * 0.3;\n    weights += 0.3;\n\n    // Compare dominant colors\n    const dominantColorScore = this.compareDominantColors(features1.dominantColors, features2.dominantColors);\n    totalScore += dominantColorScore * 0.25;\n    weights += 0.25;\n\n    // Compare edge density\n    const edgeScore = 1 - Math.abs(features1.edgeDensity - features2.edgeDensity) / Math.max(features1.edgeDensity, features2.edgeDensity, 0.1);\n    totalScore += edgeScore * 0.2;\n    weights += 0.2;\n\n    // Compare brightness\n    const brightnessScore = 1 - Math.abs(features1.brightness - features2.brightness) / 255;\n    totalScore += brightnessScore * 0.15;\n    weights += 0.15;\n\n    // Compare contrast\n    const contrastScore = 1 - Math.abs(features1.contrast - features2.contrast) / Math.max(features1.contrast, features2.contrast, 1);\n    totalScore += contrastScore * 0.1;\n    weights += 0.1;\n\n    return totalScore / weights;\n  }\n\n  compareColorHistograms(hist1, hist2) {\n    let similarity = 0;\n    const channels = ['red', 'green', 'blue'];\n\n    for (const channel of channels) {\n      let channelSimilarity = 0;\n      for (let i = 0; i < 256; i++) {\n        channelSimilarity += Math.min(hist1[channel][i], hist2[channel][i]);\n      }\n      similarity += channelSimilarity;\n    }\n\n    return similarity / (64 * 64 * 3); // Normalize by total pixels * channels\n  }\n\n  compareDominantColors(colors1, colors2) {\n    let totalSimilarity = 0;\n    let comparisons = 0;\n\n    for (const color1 of colors1) {\n      let bestMatch = 0;\n      for (const color2 of colors2) {\n        const distance = Math.sqrt(\n          Math.pow(color1.r - color2.r, 2) +\n          Math.pow(color1.g - color2.g, 2) +\n          Math.pow(color1.b - color2.b, 2)\n        );\n        const similarity = 1 - (distance / (255 * Math.sqrt(3)));\n        bestMatch = Math.max(bestMatch, similarity);\n      }\n      totalSimilarity += bestMatch;\n      comparisons++;\n    }\n\n    return comparisons > 0 ? totalSimilarity / comparisons : 0;\n  }\n\n  // Find similar items from a list\n  async findSimilarItems(targetImageUrl, itemsList, threshold = 0.3) {\n    const targetFeatures = await this.extractFeatures(targetImageUrl);\n    if (!targetFeatures) return [];\n\n    const similarities = [];\n\n    for (const item of itemsList) {\n      if (!item.images || item.images.length === 0) continue;\n\n      // Compare with the first image of each item\n      const itemFeatures = await this.extractFeatures(item.images[0].url);\n      if (itemFeatures) {\n        const similarity = this.compareFeatures(targetFeatures, itemFeatures);\n        \n        if (similarity >= threshold) {\n          similarities.push({\n            item,\n            similarity,\n            matchedImage: item.images[0].url\n          });\n        }\n      }\n    }\n\n    // Sort by similarity score (highest first)\n    return similarities.sort((a, b) => b.similarity - a.similarity);\n  }\n}\n\n// Singleton instance\nlet imageSimilarityService = null;\n\nexport function getImageSimilarityService() {\n  if (!imageSimilarityService && typeof window !== 'undefined') {\n    imageSimilarityService = new ImageSimilarityService();\n  }\n  return imageSimilarityService;\n}\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF,+EAA+E;;;;;AAExE,MAAM;IACX,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,UAAU;IACjB;IAEA,aAAa;QACX,wCAAmC;YACjC,IAAI,CAAC,MAAM,GAAG,SAAS,aAAa,CAAC;YACrC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACpC;IACF;IAEA,wDAAwD;IACxD,MAAM,cAAc,QAAQ,EAAE,OAAO,EAAE,EAAE;QACvC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,IAAI,WAAW,GAAG;YAElB,IAAI,MAAM,GAAG;gBACX,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;gBACpB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;gBAErB,qCAAqC;gBACrC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,MAAM;gBAEpC,iBAAiB;gBACjB,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,MAAM;gBACpD,QAAQ;YACV;YAEA,IAAI,OAAO,GAAG;YACd,IAAI,GAAG,GAAG;QACZ;IACF;IAEA,qCAAqC;IACrC,sBAAsB,SAAS,EAAE;QAC/B,MAAM,OAAO,UAAU,IAAI;QAC3B,MAAM,YAAY;YAChB,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC;YACzB,OAAO,IAAI,MAAM,KAAK,IAAI,CAAC;YAC3B,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC;QAC5B;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,UAAU,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC5B,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC7B;QAEA,OAAO;IACT;IAEA,0BAA0B;IAC1B,sBAAsB,SAAS,EAAE,YAAY,CAAC,EAAE;QAC9C,MAAM,OAAO,UAAU,IAAI;QAC3B,MAAM,WAAW,IAAI;QAErB,yCAAyC;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,GAAI;YACxC,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM;YACrC,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM;YACzC,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM;YACzC,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;YAE9B,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,UAAU,CAAC,IAAI;QACnD;QAEA,0CAA0C;QAC1C,OAAO,MAAM,IAAI,CAAC,SAAS,OAAO,IAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG,WACT,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM;YAClB,MAAM,CAAC,GAAG,GAAG,EAAE,GAAG,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC;YACvC,OAAO;gBAAE;gBAAG;gBAAG;gBAAG;YAAM;QAC1B;IACJ;IAEA,iDAAiD;IACjD,qBAAqB,SAAS,EAAE;QAC9B,MAAM,OAAO,UAAU,IAAI;QAC3B,MAAM,QAAQ,UAAU,KAAK;QAC7B,MAAM,SAAS,UAAU,MAAM;QAC/B,IAAI,YAAY;QAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAK;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;gBAClC,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;gBAE9B,uBAAuB;gBACvB,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI;gBAC9D,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI;gBAChE,MAAM,SAAS,CAAC,IAAI,CAAC,MAAM,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,QAAQ,IAAI,EAAE,IAAI;gBAEjG,wBAAwB;gBACxB,MAAM,eAAe,KAAK,GAAG,CAAC,UAAU,SAAS,KAAK,GAAG,CAAC,UAAU;gBACpE,IAAI,eAAe,IAAI;oBACrB;gBACF;YACF;QACF;QAEA,OAAO,YAAY,CAAC,QAAQ,MAAM;IACpC;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,QAAQ,EAAE;QAC9B,IAAI;YACF,MAAM,YAAY,MAAM,IAAI,CAAC,aAAa,CAAC;YAE3C,MAAM,WAAW;gBACf,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;gBAC3C,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;gBAC3C,aAAa,IAAI,CAAC,oBAAoB,CAAC;gBACvC,YAAY,IAAI,CAAC,mBAAmB,CAAC;gBACrC,UAAU,IAAI,CAAC,iBAAiB,CAAC;YACnC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,oBAAoB,SAAS,EAAE;QAC7B,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,QAAQ;QACZ,IAAI,QAAQ;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI;YACjD;QACF;QAEA,OAAO,QAAQ;IACjB;IAEA,kBAAkB,SAAS,EAAE;QAC3B,MAAM,OAAO,UAAU,IAAI;QAC3B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAC5C,IAAI,WAAW;QACf,IAAI,QAAQ;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,kBAAkB,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI;YAChE,YAAY,KAAK,GAAG,CAAC,kBAAkB,YAAY;YACnD;QACF;QAEA,OAAO,KAAK,IAAI,CAAC,WAAW;IAC9B;IAEA,iEAAiE;IACjE,gBAAgB,SAAS,EAAE,SAAS,EAAE;QACpC,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO;QAErC,IAAI,aAAa;QACjB,IAAI,UAAU;QAEd,2BAA2B;QAC3B,MAAM,aAAa,IAAI,CAAC,sBAAsB,CAAC,UAAU,cAAc,EAAE,UAAU,cAAc;QACjG,cAAc,aAAa;QAC3B,WAAW;QAEX,0BAA0B;QAC1B,MAAM,qBAAqB,IAAI,CAAC,qBAAqB,CAAC,UAAU,cAAc,EAAE,UAAU,cAAc;QACxG,cAAc,qBAAqB;QACnC,WAAW;QAEX,uBAAuB;QACvB,MAAM,YAAY,IAAI,KAAK,GAAG,CAAC,UAAU,WAAW,GAAG,UAAU,WAAW,IAAI,KAAK,GAAG,CAAC,UAAU,WAAW,EAAE,UAAU,WAAW,EAAE;QACvI,cAAc,YAAY;QAC1B,WAAW;QAEX,qBAAqB;QACrB,MAAM,kBAAkB,IAAI,KAAK,GAAG,CAAC,UAAU,UAAU,GAAG,UAAU,UAAU,IAAI;QACpF,cAAc,kBAAkB;QAChC,WAAW;QAEX,mBAAmB;QACnB,MAAM,gBAAgB,IAAI,KAAK,GAAG,CAAC,UAAU,QAAQ,GAAG,UAAU,QAAQ,IAAI,KAAK,GAAG,CAAC,UAAU,QAAQ,EAAE,UAAU,QAAQ,EAAE;QAC/H,cAAc,gBAAgB;QAC9B,WAAW;QAEX,OAAO,aAAa;IACtB;IAEA,uBAAuB,KAAK,EAAE,KAAK,EAAE;QACnC,IAAI,aAAa;QACjB,MAAM,WAAW;YAAC;YAAO;YAAS;SAAO;QAEzC,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI,oBAAoB;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,qBAAqB,KAAK,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE;YACpE;YACA,cAAc;QAChB;QAEA,OAAO,aAAa,CAAC,KAAK,KAAK,CAAC,GAAG,uCAAuC;IAC5E;IAEA,sBAAsB,OAAO,EAAE,OAAO,EAAE;QACtC,IAAI,kBAAkB;QACtB,IAAI,cAAc;QAElB,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI,YAAY;YAChB,KAAK,MAAM,UAAU,QAAS;gBAC5B,MAAM,WAAW,KAAK,IAAI,CACxB,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE,KAC9B,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE,KAC9B,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE;gBAEhC,MAAM,aAAa,IAAK,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE;gBACtD,YAAY,KAAK,GAAG,CAAC,WAAW;YAClC;YACA,mBAAmB;YACnB;QACF;QAEA,OAAO,cAAc,IAAI,kBAAkB,cAAc;IAC3D;IAEA,iCAAiC;IACjC,MAAM,iBAAiB,cAAc,EAAE,SAAS,EAAE,YAAY,GAAG,EAAE;QACjE,MAAM,iBAAiB,MAAM,IAAI,CAAC,eAAe,CAAC;QAClD,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAE9B,MAAM,eAAe,EAAE;QAEvB,KAAK,MAAM,QAAQ,UAAW;YAC5B,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,KAAK,GAAG;YAE9C,4CAA4C;YAC5C,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;YAClE,IAAI,cAAc;gBAChB,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,gBAAgB;gBAExD,IAAI,cAAc,WAAW;oBAC3B,aAAa,IAAI,CAAC;wBAChB;wBACA;wBACA,cAAc,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;oBAClC;gBACF;YACF;QACF;QAEA,2CAA2C;QAC3C,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAChE;AACF;AAEA,qBAAqB;AACrB,IAAI,yBAAyB;AAEtB,SAAS;IACd,IAAI,CAAC,0BAA0B,aAAkB,aAAa;QAC5D,yBAAyB,IAAI;IAC/B;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/features/ImageSimilarityRecommendations.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  SparklesIcon,\n  EyeIcon,\n  MapPinIcon,\n  ClockIcon,\n  ChatBubbleLeftRightIcon,\n  MagnifyingGlassIcon\n} from '@heroicons/react/24/outline';\nimport { getImageSimilarityService } from '@/lib/imageSimilarity';\n\nexport default function ImageSimilarityRecommendations({ \n  imageUrl, \n  itemType, \n  category, \n  excludeItemId,\n  onRecommendationsFound \n}) {\n  const [recommendations, setRecommendations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [processingMethod, setProcessingMethod] = useState('client'); // 'client' or 'server'\n\n  useEffect(() => {\n    if (imageUrl) {\n      findSimilarItems();\n    }\n  }, [imageUrl, itemType, category]);\n\n  const findSimilarItems = async () => {\n    if (!imageUrl) return;\n\n    setLoading(true);\n    setError('');\n\n    try {\n      if (processingMethod === 'client') {\n        await findSimilarItemsClient();\n      } else {\n        await findSimilarItemsServer();\n      }\n    } catch (error) {\n      console.error('Error finding similar items:', error);\n      setError('Failed to find similar items. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const findSimilarItemsClient = async () => {\n    // First, get items to compare against\n    const response = await fetch(`/api/items/similarity?type=${itemType}&category=${category || 'all'}&excludeItemId=${excludeItemId || ''}&limit=20`);\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new Error(data.error || 'Failed to fetch items');\n    }\n\n    if (data.items.length === 0) {\n      setRecommendations([]);\n      onRecommendationsFound?.(0);\n      return;\n    }\n\n    // Use client-side image similarity service\n    const similarityService = getImageSimilarityService();\n    if (!similarityService) {\n      // Fallback to server-side processing\n      await findSimilarItemsServer();\n      return;\n    }\n\n    const similarItems = await similarityService.findSimilarItems(\n      imageUrl, \n      data.items, \n      0.3 // similarity threshold\n    );\n\n    setRecommendations(similarItems.slice(0, 6)); // Show top 6 recommendations\n    onRecommendationsFound?.(similarItems.length);\n  };\n\n  const findSimilarItemsServer = async () => {\n    const response = await fetch('/api/items/similarity', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        imageUrl,\n        itemType,\n        category,\n        excludeItemId,\n        limit: 6\n      }),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new Error(data.error || 'Failed to find similar items');\n    }\n\n    setRecommendations(data.similarItems || []);\n    onRecommendationsFound?.(data.similarItems?.length || 0);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Yesterday';\n    if (diffDays < 7) return `${diffDays} days ago`;\n    return date.toLocaleDateString();\n  };\n\n  const getConfidenceColor = (confidence) => {\n    switch (confidence) {\n      case 'high': return 'text-green-600 bg-green-100';\n      case 'medium': return 'text-yellow-600 bg-yellow-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getSimilarityPercentage = (similarity) => {\n    return Math.round(similarity * 100);\n  };\n\n  if (!imageUrl) return null;\n\n  return (\n    <div className=\"mt-8\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center\">\n          <SparklesIcon className=\"h-6 w-6 text-purple-600 mr-2\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            AI-Powered Similar Items\n          </h3>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => setProcessingMethod(processingMethod === 'client' ? 'server' : 'client')}\n            className=\"text-xs text-gray-500 hover:text-gray-700\"\n          >\n            Mode: {processingMethod}\n          </button>\n          \n          <button\n            onClick={findSimilarItems}\n            disabled={loading}\n            className=\"btn-secondary text-sm\"\n          >\n            {loading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-2\"></div>\n                Analyzing...\n              </div>\n            ) : (\n              <>\n                <MagnifyingGlassIcon className=\"h-3 w-3 mr-1\" />\n                Refresh\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {loading && (\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Analyzing image and finding similar items...</p>\n          <p className=\"text-xs text-gray-500 mt-1\">\n            Using {processingMethod === 'client' ? 'client-side AI' : 'server-side analysis'}\n          </p>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <button onClick={findSimilarItems} className=\"btn-primary\">\n            Try Again\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && recommendations.length === 0 && (\n        <div className=\"text-center py-8\">\n          <SparklesIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h4 className=\"text-lg font-medium text-gray-900 mb-2\">No Similar Items Found</h4>\n          <p className=\"text-gray-600\">\n            We couldn't find any visually similar items at the moment. \n            Try adjusting your search criteria or check back later.\n          </p>\n        </div>\n      )}\n\n      {!loading && !error && recommendations.length > 0 && (\n        <div>\n          <div className=\"mb-4 p-3 bg-purple-50 rounded-lg\">\n            <p className=\"text-sm text-purple-700\">\n              <SparklesIcon className=\"h-4 w-4 inline mr-1\" />\n              Found {recommendations.length} visually similar {itemType === 'lost' ? 'found' : 'lost'} items using AI image analysis\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {recommendations.map((rec, index) => (\n              <Link\n                key={rec.item._id}\n                href={`/items/${rec.item._id}`}\n                className=\"card hover:shadow-lg transition-shadow\"\n              >\n                <div className=\"card-body\">\n                  {/* Similarity Score */}\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getConfidenceColor(rec.confidence)}`}>\n                      {getSimilarityPercentage(rec.similarity)}% match\n                    </span>\n                    <span className={`text-xs px-2 py-1 rounded-full ${\n                      rec.item.type === 'lost' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'\n                    }`}>\n                      {rec.item.type === 'lost' ? 'Lost' : 'Found'}\n                    </span>\n                  </div>\n\n                  {/* Image */}\n                  <div className=\"mb-3\">\n                    {rec.matchedImage ? (\n                      <img\n                        src={rec.matchedImage}\n                        alt={rec.item.title}\n                        className=\"w-full h-32 object-cover rounded-md\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-32 bg-gray-100 rounded-md flex items-center justify-center\">\n                        <span className=\"text-gray-400 text-sm\">No Image</span>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Item Info */}\n                  <h4 className=\"font-medium text-gray-900 line-clamp-2 mb-2\">\n                    {rec.item.title}\n                  </h4>\n\n                  <p className=\"text-sm text-gray-600 line-clamp-2 mb-3\">\n                    {rec.item.description}\n                  </p>\n\n                  {/* Metadata */}\n                  <div className=\"space-y-1 text-xs text-gray-500\">\n                    <div className=\"flex items-center\">\n                      <MapPinIcon className=\"h-3 w-3 mr-1\" />\n                      {rec.item.location}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <ClockIcon className=\"h-3 w-3 mr-1\" />\n                      {formatDate(rec.item.createdAt)}\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <EyeIcon className=\"h-3 w-3 mr-1\" />\n                        {rec.item.views || 0} views\n                      </div>\n                      <div className=\"flex items-center\">\n                        <ChatBubbleLeftRightIcon className=\"h-3 w-3 mr-1\" />\n                        Contact\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Match Factors (for debugging/transparency) */}\n                  {rec.factors && (\n                    <div className=\"mt-2 pt-2 border-t border-gray-100\">\n                      <p className=\"text-xs text-gray-400\">\n                        Match factors: {rec.factors.map(f => f.type).join(', ')}\n                      </p>\n                    </div>\n                  )}\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-xs text-gray-500\">\n              Recommendations powered by AI image analysis. \n              Results are based on visual similarity, color patterns, and metadata matching.\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAZA;;;;;AAce,SAAS,+BAA+B,EACrD,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,sBAAsB,EACvB;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,uBAAuB;IAE3F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oDAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;mDAAG;QAAC;QAAU;QAAU;KAAS;IAEjC,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,qBAAqB,UAAU;gBACjC,MAAM;YACR,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,sCAAsC;QACtC,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE,YAAY,MAAM,eAAe,EAAE,iBAAiB,GAAG,SAAS,CAAC;QACjJ,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;YAC3B,mBAAmB,EAAE;YACrB,yBAAyB;YACzB;QACF;QAEA,2CAA2C;QAC3C,MAAM,oBAAoB,CAAA,GAAA,gIAAA,CAAA,4BAAyB,AAAD;QAClD,IAAI,CAAC,mBAAmB;YACtB,qCAAqC;YACrC,MAAM;YACN;QACF;QAEA,MAAM,eAAe,MAAM,kBAAkB,gBAAgB,CAC3D,UACA,KAAK,KAAK,EACV,IAAI,uBAAuB;;QAG7B,mBAAmB,aAAa,KAAK,CAAC,GAAG,KAAK,6BAA6B;QAC3E,yBAAyB,aAAa,MAAM;IAC9C;IAEA,MAAM,yBAAyB;QAC7B,MAAM,WAAW,MAAM,MAAM,yBAAyB;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;gBACA;gBACA,OAAO;YACT;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,mBAAmB,KAAK,YAAY,IAAI,EAAE;QAC1C,yBAAyB,KAAK,YAAY,EAAE,UAAU;IACxD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM;QAChC,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE3D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,OAAO,KAAK,kBAAkB;IAChC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAO,KAAK,KAAK,CAAC,aAAa;IACjC;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0NAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAKtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,oBAAoB,qBAAqB,WAAW,WAAW;gCAC9E,WAAU;;oCACX;oCACQ;;;;;;;0CAGT,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,wBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAA0E;;;;;;yDAI3F;;sDACE,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;YAQzD,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBAAE,WAAU;;4BAA6B;4BACjC,qBAAqB,WAAW,mBAAmB;;;;;;;;;;;;;YAK/D,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAO,SAAS;wBAAkB,WAAU;kCAAc;;;;;;;;;;;;YAM9D,CAAC,WAAW,CAAC,SAAS,gBAAgB,MAAM,KAAK,mBAChD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0NAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAOhC,CAAC,WAAW,CAAC,SAAS,gBAAgB,MAAM,GAAG,mBAC9C,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;8CACX,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAwB;gCACzC,gBAAgB,MAAM;gCAAC;gCAAmB,aAAa,SAAS,UAAU;gCAAO;;;;;;;;;;;;kCAI5F,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,KAAK,sBACzB,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE;gCAC9B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,IAAI,UAAU,GAAG;;wDAChG,wBAAwB,IAAI,UAAU;wDAAE;;;;;;;8DAE3C,6LAAC;oDAAK,WAAW,CAAC,+BAA+B,EAC/C,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,4BAA4B,+BACvD;8DACC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,SAAS;;;;;;;;;;;;sDAKzC,6LAAC;4CAAI,WAAU;sDACZ,IAAI,YAAY,iBACf,6LAAC;gDACC,KAAK,IAAI,YAAY;gDACrB,KAAK,IAAI,IAAI,CAAC,KAAK;gDACnB,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;sDAM9C,6LAAC;4CAAG,WAAU;sDACX,IAAI,IAAI,CAAC,KAAK;;;;;;sDAGjB,6LAAC;4CAAE,WAAU;sDACV,IAAI,IAAI,CAAC,WAAW;;;;;;sDAIvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,IAAI,IAAI,CAAC,QAAQ;;;;;;;8DAEpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDACpB,WAAW,IAAI,IAAI,CAAC,SAAS;;;;;;;8DAEhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAClB,IAAI,IAAI,CAAC,KAAK,IAAI;gEAAE;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gPAAA,CAAA,0BAAuB;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAOzD,IAAI,OAAO,kBACV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAAwB;oDACnB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;+BAnErD,IAAI,IAAI,CAAC,GAAG;;;;;;;;;;kCA4EvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AASjD;GAhSwB;KAAA", "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/items/%5Bid%5D/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport {\n  MapPinIcon,\n  CalendarIcon,\n  TagIcon,\n  EyeIcon,\n  ChatBubbleLeftRightIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  PencilIcon,\n  TrashIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\nimport ImageSimilarityRecommendations from '@/components/features/ImageSimilarityRecommendations';\n\nexport default function ItemDetail() {\n  const { id } = useParams();\n  const { data: session } = useSession();\n  const router = useRouter();\n  const [item, setItem] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [deleting, setDeleting] = useState(false);\n\n  useEffect(() => {\n    if (id) {\n      fetchItem();\n    }\n  }, [id]);\n\n  const fetchItem = async () => {\n    try {\n      const response = await fetch(`/api/items/${id}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setItem(data.item);\n        // Increment view count\n        incrementViews();\n      } else {\n        setError(data.error || 'Item not found');\n      }\n    } catch (error) {\n      setError('Failed to load item');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const incrementViews = async () => {\n    try {\n      await fetch(`/api/items/${id}/view`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.error('Failed to increment views:', error);\n    }\n  };\n\n  const handleClaim = () => {\n    if (!session) {\n      router.push('/auth/signin');\n      return;\n    }\n    router.push(`/items/${id}/claim`);\n  };\n\n  const handleContact = () => {\n    if (!session) {\n      router.push('/auth/signin');\n      return;\n    }\n    router.push(`/chat?item=${id}&user=${item.postedBy._id}`);\n  };\n\n  const handleEdit = () => {\n    router.push(`/items/${id}/edit`);\n  };\n\n  const handleDelete = async () => {\n    if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {\n      return;\n    }\n\n    setDeleting(true);\n    try {\n      const response = await fetch(`/api/items/${id}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        router.push('/dashboard');\n      } else {\n        const data = await response.json();\n        alert(data.error || 'Failed to delete item');\n      }\n    } catch (error) {\n      console.error('Error deleting item:', error);\n      alert('Failed to delete item');\n    } finally {\n      setDeleting(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-4\"></div>\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              <div className=\"h-64 bg-gray-200 rounded\"></div>\n              <div className=\"space-y-4\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                <div className=\"h-20 bg-gray-200 rounded\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !item) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Item Not Found</h1>\n            <p className=\"text-gray-600 mb-8\">{error}</p>\n            <Link href=\"/items\" className=\"btn-primary\">\n              Browse Items\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Breadcrumb */}\n        <nav className=\"flex mb-8\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-4\">\n            <li>\n              <Link href=\"/\" className=\"text-gray-400 hover:text-gray-500\">\n                Home\n              </Link>\n            </li>\n            <li>\n              <span className=\"text-gray-400\">/</span>\n            </li>\n            <li>\n              <Link\n                href={`/items/${item.type}`}\n                className=\"text-gray-400 hover:text-gray-500\"\n              >\n                {item.type === 'lost' ? 'Lost Items' : 'Found Items'}\n              </Link>\n            </li>\n            <li>\n              <span className=\"text-gray-400\">/</span>\n            </li>\n            <li>\n              <span className=\"text-gray-900\">{item.title}</span>\n            </li>\n          </ol>\n        </nav>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Images */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                {item.images && item.images.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {/* Main Image */}\n                    <div className=\"aspect-w-16 aspect-h-12\">\n                      <img\n                        src={item.images[selectedImage]?.url || '/placeholder-image.jpg'}\n                        alt={item.title}\n                        className=\"w-full h-96 object-cover rounded-lg\"\n                      />\n                    </div>\n\n                    {/* Thumbnail Images */}\n                    {item.images.length > 1 && (\n                      <div className=\"flex space-x-2 overflow-x-auto\">\n                        {item.images.map((image, index) => (\n                          <button\n                            key={index}\n                            onClick={() => setSelectedImage(index)}\n                            className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${\n                              selectedImage === index\n                                ? 'border-blue-500'\n                                : 'border-gray-200 hover:border-gray-300'\n                            }`}\n                          >\n                            <img\n                              src={image.url}\n                              alt={`${item.title} ${index + 1}`}\n                              className=\"w-full h-full object-cover\"\n                            />\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"aspect-w-16 aspect-h-12 bg-gray-100 rounded-lg flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"text-4xl text-gray-400 mb-2\">📷</div>\n                      <p className=\"text-gray-500\">No images available</p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Description */}\n            <div className=\"card mt-6\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Description</h3>\n              </div>\n              <div className=\"card-body\">\n                <p className=\"text-gray-700 whitespace-pre-wrap\">{item.description}</p>\n\n                {item.tags && item.tags.length > 0 && (\n                  <div className=\"mt-4\">\n                    <div className=\"flex items-center mb-2\">\n                      <TagIcon className=\"h-4 w-4 text-gray-400 mr-1\" />\n                      <span className=\"text-sm font-medium text-gray-700\">Tags:</span>\n                    </div>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {item.tags.map((tag, index) => (\n                        <span\n                          key={index}\n                          className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\"\n                        >\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Item Details & Actions */}\n          <div className=\"space-y-6\">\n            {/* Item Info */}\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <h1 className=\"text-2xl font-bold text-gray-900\">{item.title}</h1>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className={`badge ${\n                      item.type === 'lost' ? 'badge-danger' : 'badge-success'\n                    }`}>\n                      {item.type === 'lost' ? 'Lost' : 'Found'}\n                    </span>\n                    {item.isUrgent && (\n                      <span className=\"badge badge-warning\">\n                        <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                        Urgent\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <MapPinIcon className=\"h-4 w-4 mr-2\" />\n                    <span>{item.type === 'lost' ? 'Lost at' : 'Found at'}: {item.location}</span>\n                  </div>\n\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                    <span>{item.type === 'lost' ? 'Lost on' : 'Found on'}: {formatDate(item.dateOccurred)}</span>\n                  </div>\n\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <EyeIcon className=\"h-4 w-4 mr-2\" />\n                    <span>{item.views || 0} views</span>\n                  </div>\n\n                  <div className=\"pt-2 border-t border-gray-200\">\n                    <span className=\"badge badge-info\">{item.category}</span>\n                  </div>\n                </div>\n\n                {item.reward?.offered && (\n                  <div className=\"mt-4 p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <span className=\"text-green-600 font-medium\">💰 Reward Offered</span>\n                    </div>\n                    {item.reward.amount && (\n                      <p className=\"text-sm text-green-700 mt-1\">\n                        Amount: PKR {item.reward.amount}\n                      </p>\n                    )}\n                    {item.reward.description && (\n                      <p className=\"text-sm text-green-700 mt-1\">\n                        {item.reward.description}\n                      </p>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Posted By */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Posted By</h3>\n              </div>\n              <div className=\"card-body\">\n                <div className=\"flex items-center\">\n                  {item.postedBy.avatar ? (\n                    <img\n                      src={item.postedBy.avatar}\n                      alt={item.postedBy.name}\n                      className=\"h-10 w-10 rounded-full\"\n                    />\n                  ) : (\n                    <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                      <span className=\"text-sm font-medium text-gray-700\">\n                        {item.postedBy.name.charAt(0)}\n                      </span>\n                    </div>\n                  )}\n                  <div className=\"ml-3\">\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      {item.postedBy.name}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      Posted {formatDate(item.createdAt)}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Actions */}\n            {session && (\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Actions</h3>\n                </div>\n                <div className=\"card-body space-y-3\">\n                  {session.user.id === item.postedBy._id ? (\n                    // Owner actions\n                    <>\n                      <button\n                        onClick={handleEdit}\n                        className=\"btn-primary w-full\"\n                      >\n                        <PencilIcon className=\"h-4 w-4 mr-2\" />\n                        Edit Item\n                      </button>\n                      <button\n                        onClick={handleDelete}\n                        disabled={deleting}\n                        className=\"btn-danger w-full\"\n                      >\n                        {deleting ? (\n                          <div className=\"flex items-center justify-center\">\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                            Deleting...\n                          </div>\n                        ) : (\n                          <>\n                            <TrashIcon className=\"h-4 w-4 mr-2\" />\n                            Delete Item\n                          </>\n                        )}\n                      </button>\n                    </>\n                  ) : (\n                    // Other user actions\n                    <>\n                      {item.type === 'found' ? (\n                        <button\n                          onClick={handleClaim}\n                          className=\"btn-primary w-full\"\n                        >\n                          <CheckCircleIcon className=\"h-4 w-4 mr-2\" />\n                          Claim This Item\n                        </button>\n                      ) : (\n                        <button\n                         onClick={() => router.push(`/chat?itemId=${id}&otherUserId=${item.postedBy._id}`)}\n                          className=\"btn-primary w-full\"\n                        >\n                          <ChatBubbleLeftRightIcon className=\"h-4 w-4 mr-2\" />\n                          I Found This Item\n                        </button>\n                      )}\n\n                      <button\n                        onClick={() => router.push(`/chat?itemId=${id}&otherUserId=${item.postedBy._id}`)}\n                        className=\"btn-secondary w-full\"\n                      >\n                        <ChatBubbleLeftRightIcon className=\"h-4 w-4 mr-2\" />\n                        Send Message\n                      </button>\n                    </>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Contact Info (if available) */}\n            {item.contactInfo && (\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Contact Information</h3>\n                </div>\n                <div className=\"card-body space-y-2\">\n                  {item.contactInfo.email && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <EnvelopeIcon className=\"h-4 w-4 mr-2\" />\n                      <a\n                        href={`mailto:${item.contactInfo.email}`}\n                        className=\"text-blue-600 hover:text-blue-500\"\n                      >\n                        {item.contactInfo.email}\n                      </a>\n                    </div>\n                  )}\n\n                  {item.contactInfo.phone && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <PhoneIcon className=\"h-4 w-4 mr-2\" />\n                      <a\n                        href={`tel:${item.contactInfo.phone}`}\n                        className=\"text-blue-600 hover:text-blue-500\"\n                      >\n                        {item.contactInfo.phone}\n                      </a>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* AI-Powered Similar Items Recommendations */}\n        {item.images && item.images.length > 0 && (\n          <ImageSimilarityRecommendations\n            imageUrl={item.images[0].url}\n            itemType={item.type}\n            category={item.category}\n            excludeItemId={item._id}\n            onRecommendationsFound={(count) => {\n              console.log(`Found ${count} similar items`);\n            }}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AApBA;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,IAAI;gBACN;YACF;QACF;+BAAG;QAAC;KAAG;IAEP,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,IAAI;gBACjB,uBAAuB;gBACvB;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,MAAM,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,EAAE;gBACnC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QACA,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC;IAClC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QACA,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE;IAC1D;IAEA,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC;IACjC;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,6EAA6E;YACxF;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,KAAK,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO7B;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;IAOtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;wBAAY,cAAW;kCACpC,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAoC;;;;;;;;;;;8CAI/D,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;wCAC3B,WAAU;kDAET,KAAK,IAAI,KAAK,SAAS,eAAe;;;;;;;;;;;8CAG3C,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAiB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,kBACnC,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK,KAAK,MAAM,CAAC,cAAc,EAAE,OAAO;4DACxC,KAAK,KAAK,KAAK;4DACf,WAAU;;;;;;;;;;;oDAKb,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,6LAAC;wDAAI,WAAU;kEACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;gEAEC,SAAS,IAAM,iBAAiB;gEAChC,WAAW,CAAC,4DAA4D,EACtE,kBAAkB,QACd,oBACA,yCACJ;0EAEF,cAAA,6LAAC;oEACC,KAAK,MAAM,GAAG;oEACd,KAAK,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG;oEACjC,WAAU;;;;;;+DAXP;;;;;;;;;;;;;;;qEAmBf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8B;;;;;;sEAC7C,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,KAAK,WAAW;;;;;;oDAEjE,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,gNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAEtD,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAcrB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC,KAAK,KAAK;;;;;;sEAC5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,MAAM,EACtB,KAAK,IAAI,KAAK,SAAS,iBAAiB,iBACxC;8EACC,KAAK,IAAI,KAAK,SAAS,SAAS;;;;;;gEAElC,KAAK,QAAQ,kBACZ,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,gPAAA,CAAA,0BAAuB;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;8DAO5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;;wEAAM,KAAK,IAAI,KAAK,SAAS,YAAY;wEAAW;wEAAG,KAAK,QAAQ;;;;;;;;;;;;;sEAGvE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,0NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;8EACxB,6LAAC;;wEAAM,KAAK,IAAI,KAAK,SAAS,YAAY;wEAAW;wEAAG,WAAW,KAAK,YAAY;;;;;;;;;;;;;sEAGtF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;;wEAAM,KAAK,KAAK,IAAI;wEAAE;;;;;;;;;;;;;sEAGzB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAoB,KAAK,QAAQ;;;;;;;;;;;;;;;;;gDAIpD,KAAK,MAAM,EAAE,yBACZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;wDAE9C,KAAK,MAAM,CAAC,MAAM,kBACjB,6LAAC;4DAAE,WAAU;;gEAA8B;gEAC5B,KAAK,MAAM,CAAC,MAAM;;;;;;;wDAGlC,KAAK,MAAM,CAAC,WAAW,kBACtB,6LAAC;4DAAE,WAAU;sEACV,KAAK,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDASpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,QAAQ,CAAC,MAAM,iBACnB,6LAAC;4DACC,KAAK,KAAK,QAAQ,CAAC,MAAM;4DACzB,KAAK,KAAK,QAAQ,CAAC,IAAI;4DACvB,WAAU;;;;;iFAGZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;sEAIjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ,CAAC,IAAI;;;;;;8EAErB,6LAAC;oEAAE,WAAU;;wEAAwB;wEAC3B,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ1C,yBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,GAAG,GACpC,gBAAgB;8DAChB;;sEACE,6LAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGzC,6LAAC;4DACC,SAAS;4DACT,UAAU;4DACV,WAAU;sEAET,yBACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;oEAAuE;;;;;;qFAIxF;;kFACE,6LAAC,oNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;mEAO9C,qBAAqB;8DACrB;;wDACG,KAAK,IAAI,KAAK,wBACb,6LAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,6LAAC,gOAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;gEAAiB;;;;;;iFAI9C,6LAAC;4DACA,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,GAAG,aAAa,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE;4DAC/E,WAAU;;8EAEV,6LAAC,gPAAA,CAAA,0BAAuB;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAKxD,6LAAC;4DACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,GAAG,aAAa,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE;4DAChF,WAAU;;8EAEV,6LAAC,gPAAA,CAAA,0BAAuB;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;oCAU/D,KAAK,WAAW,kBACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,WAAW,CAAC,KAAK,kBACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;gEACC,MAAM,CAAC,OAAO,EAAE,KAAK,WAAW,CAAC,KAAK,EAAE;gEACxC,WAAU;0EAET,KAAK,WAAW,CAAC,KAAK;;;;;;;;;;;;oDAK5B,KAAK,WAAW,CAAC,KAAK,kBACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEACC,MAAM,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC,KAAK,EAAE;gEACrC,WAAU;0EAET,KAAK,WAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWtC,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,6LAAC,kKAAA,CAAA,UAA8B;wBAC7B,UAAU,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;wBAC5B,UAAU,KAAK,IAAI;wBACnB,UAAU,KAAK,QAAQ;wBACvB,eAAe,KAAK,GAAG;wBACvB,wBAAwB,CAAC;4BACvB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,cAAc,CAAC;wBAC5C;;;;;;;;;;;;;;;;;;AAMZ;GAhdwB;;QACP,qIAAA,CAAA,YAAS;QACE,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}