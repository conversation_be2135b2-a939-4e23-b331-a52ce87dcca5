{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/PhotoIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/XMarkIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction XMarkIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6 18 18 6M6 6l12 12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XMarkIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/CalendarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CalendarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CalendarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/MapPinIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/TagIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction TagIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6 6h.008v.008H6V6Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TagIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/Bars3Icon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction Bars3Icon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(Bars3Icon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/BellIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction BellIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BellIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/UserCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction UserCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,EACtB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/Cog6ToothIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction Cog6ToothIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(Cog6ToothIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,EACrB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js"], "sourcesContent": ["import * as React from \"react\";\n/** @deprecated */\nfunction ArrowRightOnRectangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowRightOnRectangleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,gBAAgB,GAChB,SAAS,0BAA0B,EACjC,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40swc/helpers/cjs/_tagged_template_literal_loose.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _tagged_template_literal_loose(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    strings.raw = raw;\n\n    return strings;\n}\nexports._ = _tagged_template_literal_loose;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,+BAA+B,OAAO,EAAE,GAAG;IAChD,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,QAAQ,GAAG,GAAG;IAEd,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = React.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gJACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,MAAM,oBAAoB,EACjD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/use-sync-external-store/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.production.js');\n} else {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "file": "useLayoutEffect.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAOM,MAAM,4CAAkB,OAAO,aAAa,cAC/C,CAAA,yMAAA,UAAI,EAAE,eAAe,GACrB,KAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "file": "useEffectEvent.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useEffectEvent.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAKM,SAAS,0CAAmC,EAAM;IACvD,MAAM,MAAM,CAAA,yMAAA,SAAK,EAAwB;IACzC,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAG;IACP,aAAa;IACb,OAAO,CAAA,yMAAA,cAAU,EAAK,CAAC,GAAG;QACxB,MAAM,IAAI,IAAI,OAAO;QACrB,OAAO,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,KAAO;IAChB,GAAG,EAAE;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "file": "isFocusable.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isFocusable.ts"], "sourcesContent": ["const focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR);\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,0CAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mDAA6B,wCAAkB,IAAI,CAAC,qBAAqB;AAE/E,wCAAkB,IAAI,CAAC;AACvB,MAAM,kDAA4B,wCAAkB,IAAI,CAAC;AAElD,SAAS,0CAAY,OAAgB;IAC1C,OAAO,QAAQ,OAAO,CAAC;AACzB;AAEO,SAAS,0CAAW,OAAgB;IACzC,OAAO,QAAQ,OAAO,CAAC;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "file": "domHelpers.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/domHelpers.ts"], "sourcesContent": ["export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,4CAAmB,CAAC;QACxB;IAAP,OAAO,CAAA,oBAAA,OAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAI,aAAa,MAAA,QAAjB,sBAAA,KAAA,IAAA,oBAAqB;AAC9B;AAEO,MAAM,4CAAiB,CAC5B;IAEA,IAAI,MAAM,YAAY,MAAM,GAAG,MAAM,KAAK,IACxC,OAAO;IAGT,MAAM,MAAM,0CAAiB;IAC7B,OAAO,IAAI,WAAW,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,6BAAO,KAAc;IAC5B,OAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAQ,MAAe,QAAQ,KAAK;AACxC;AAKO,SAAS,0CAAa,IAAiB;IAC5C,OAAO,6BAAO,SACZ,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAC7C,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "file": "focusWithoutScrolling.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/focusWithoutScrolling.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAoBM,SAAS,0CAAsB,OAAyB;IAC7D,IAAI,+CACF,QAAQ,KAAK,CAAC;QAAC,eAAe;IAAI;SAC7B;QACL,IAAI,qBAAqB,4CAAsB;QAC/C,QAAQ,KAAK;QACb,4CAAsB;IACxB;AACF;AAEA,IAAI,oDAA8C;AAClD,SAAS;IACP,IAAI,qDAA+B,MAAM;QACvC,oDAA8B;QAC9B,IAAI;YACF,IAAI,YAAY,SAAS,aAAa,CAAC;YACvC,UAAU,KAAK,CAAC;gBACd,IAAI,iBAAgB;oBAClB,oDAA8B;oBAC9B,OAAO;gBACT;YACF;QACF,EAAE,OAAM;QACN,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAEA,SAAS,4CAAsB,OAAyB;IACtD,IAAI,SAAS,QAAQ,UAAU;IAC/B,IAAI,qBAA0C,EAAE;IAChD,IAAI,uBAAuB,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAEhF,MAAO,kBAAkB,eAAe,WAAW,qBAAsB;QACvE,IACE,OAAO,YAAY,GAAG,OAAO,YAAY,IACzC,OAAO,WAAW,GAAG,OAAO,WAAW,EAEvC,mBAAmB,IAAI,CAAC;YACtB,SAAS;YACT,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;QAC/B;QAEF,SAAS,OAAO,UAAU;IAC5B;IAEA,IAAI,gCAAgC,aAClC,mBAAmB,IAAI,CAAC;QACtB,SAAS;QACT,WAAW,qBAAqB,SAAS;QACzC,YAAY,qBAAqB,UAAU;IAC7C;IAGF,OAAO;AACT;AAEA,SAAS,4CAAsB,kBAAuC;IACpE,KAAK,IAAI,EAAA,SAAC,OAAO,EAAA,WAAE,SAAS,EAAA,YAAE,UAAU,EAAC,IAAI,mBAAoB;QAC/D,QAAQ,SAAS,GAAG;QACpB,QAAQ,UAAU,GAAG;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "file": "platform.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/platform.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  return (\n    window.navigator['userAgentData']?.brands.some((brand: {brand: string, version: string}) => re.test(brand.brand))\n  ) ||\n  re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n  \n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;;;;;AAED,SAAS,oCAAc,EAAU;QAK7B;IAJF,IAAI,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,MACvD,OAAO;IAET,OAAO,CAAA,CACL,kCAAA,OAAO,SAAS,CAAC,gBAAgB,MAAA,QAAjC,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAmC,MAAM,CAAC,IAAI,CAAC,CAAC,QAA4C,GAAG,IAAI,CAAC,MAAM,KAAK,EAAA,KAEjH,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AACpC;AAEA,SAAS,mCAAa,EAAU;QAElB;IADZ,OAAO,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,OACxD,GAAG,IAAI,CAAC,CAAA,CAAA,kCAAA,OAAO,SAAS,CAAC,gBAAgB,MAAA,QAAjC,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAmC,QAAQ,KAAI,OAAO,SAAS,CAAC,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,EAAiB;IAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAC3B,OAAO;;IAAA;IAGT,IAAI,MAAsB;IAC1B,OAAO;QACL,IAAI,OAAO,MACT,MAAM;QAER,OAAO;IACT;AACF;AAEO,MAAM,4CAAQ,6BAAO;IAC1B,OAAO,mCAAa;AACtB;AAEO,MAAM,2CAAW,6BAAO;IAC7B,OAAO,mCAAa;AACtB;AAEO,MAAM,4CAAS,6BAAO;IAC3B,OAAO,mCAAa,aAClB,yFAAyF;IACxF,+CAAW,UAAU,cAAc,GAAG;AAC3C;AAEO,MAAM,4CAAQ,6BAAO;IAC1B,OAAO,8CAAc;AACvB;AAEO,MAAM,4CAAgB,6BAAO;IAClC,OAAO,+CAAW;AACpB;AAEO,MAAM,4CAAW,6BAAO;IAC7B,OAAO,oCAAc,mBAAmB,CAAC;AAC3C;AAEO,MAAM,4CAAW,6BAAO;IAC7B,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAAY,6BAAO;IAC9B,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAAY,6BAAO;IAC9B,OAAO,oCAAc;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "file": "isVirtualEvent.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isVirtualEvent.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAeM,SAAS,0CAAe,KAAgC;IAC7D,0BAA0B;IAC1B,IAAK,MAAc,cAAc,KAAK,KAAK,MAAM,SAAS,EACxD,OAAO;IAGT,oIAAoI;IACpI,oIAAoI;IACpI,qCAAqC;IACrC,IAAI,CAAA,iKAAA,YAAQ,OAAQ,MAAuB,WAAW,EACpD,OAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,KAAK;IAGrD,OAAO,MAAM,MAAM,KAAK,KAAK,CAAE,MAAuB,WAAW;AACnE;AAEO,SAAS,0CAAsB,KAAmB;IACvD,yEAAyE;IACzE,uFAAuF;IACvF,kGAAkG;IAClG,mHAAmH;IACnH,oHAAoH;IACpH,8DAA8D;IAC9D,OACG,CAAC,CAAA,iKAAA,YAAQ,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KACtD,MAAM,KAAK,KAAK,KACf,MAAM,MAAM,KAAK,KACjB,MAAM,QAAQ,KAAK,KACnB,MAAM,MAAM,KAAK,KACjB,MAAM,WAAW,KAAK;AAG5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "file": "DOMFunctions.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/shadowdom/DOMFunctions.ts"], "sourcesContent": ["// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,kIAAkI;AAQ3H,SAAS,0CACd,IAA6B,EAC7B,SAAkC;IAElC,IAAI,CAAC,CAAA,kKAAA,YAAQ,KACX,OAAO,aAAa,OAAO,KAAK,QAAQ,CAAC,aAAa;IAGxD,IAAI,CAAC,QAAQ,CAAC,WACZ,OAAO;IAGT,IAAI,cAAqD;IAEzD,MAAO,gBAAgB,KAAM;QAC3B,IAAI,gBAAgB,MAClB,OAAO;QAGT,IAAK,YAAgC,OAAO,KAAK,UAC9C,YAAgC,YAAY,EAC7C,AACA,cAAe,OADM,KAC0B,YAAY,CAAE,UAAU;aAClE,IAAI,CAAA,mKAAA,eAAW,EAAE,cACtB,AACA,cAAc,YAAY,EADE,EACE;aAE9B,cAAc,YAAY,UAAU;IAExC;IAEA,OAAO;AACT;AAKO,MAAM,4CAAmB,CAAC,MAAgB,QAAQ;QAOvD;IANA,IAAI,CAAC,CAAA,kKAAA,YAAQ,KACX,OAAO,IAAI,aAAa;IAE1B,IAAI,gBAAgC,IAAI,aAAa;IAErD,MAAO,iBAAiB,gBAAgB,iBAAA,CAAA,CACxC,4BAAA,cAAc,UAAU,MAAA,QAAxB,8BAAA,KAAA,IAAA,KAAA,IAAA,0BAA0B,aAAa,EACrC,gBAAgB,cAAc,UAAU,CAAC,aAAa;IAGxD,OAAO;AACT;AAKO,SAAS,0CAAgC,KAAQ;IACtD,IAAI,CAAA,kKAAA,YAAQ,OAAQ,MAAM,MAAM,CAAiB,UAAU,EAAE;QAC3D,IAAI,MAAM,YAAY,EACpB,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAElC;IACA,OAAO,MAAM,MAAM;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "file": "useGlobalListeners.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useGlobalListeners.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS;IACd,IAAI,kBAAkB,CAAA,yMAAA,SAAK,EAAE,IAAI;IACjC,IAAI,oBAAoB,CAAA,yMAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;QAChE,8EAA8E;QAC9E,IAAI,KAAK,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,IAAI,IAAG,CAAC,GAAG;YAC3B,gBAAgB,OAAO,CAAC,MAAM,CAAC;YAC/B,YAAY;QACd,IAAI;QACJ,gBAAgB,OAAO,CAAC,GAAG,CAAC,UAAU;kBAAC;yBAAM;gBAAa;qBAAI;QAAO;QACrE,YAAY,gBAAgB,CAAC,MAAM,IAAI;IACzC,GAAG,EAAE;IACL,IAAI,uBAAuB,CAAA,yMAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;YAC1D;QAAT,IAAI,KAAK,CAAA,CAAA,+BAAA,gBAAgB,OAAO,CAAC,GAAG,CAAC,SAAA,MAAA,QAA5B,iCAAA,KAAA,IAAA,KAAA,IAAA,6BAAuC,EAAE,KAAI;QACtD,YAAY,mBAAmB,CAAC,MAAM,IAAI;QAC1C,gBAAgB,OAAO,CAAC,MAAM,CAAC;IACjC,GAAG,EAAE;IACL,IAAI,2BAA2B,CAAA,yMAAA,cAAU,EAAE;QACzC,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YACtC,qBAAqB,MAAM,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,MAAM,OAAO;QACxE;IACF,GAAG;QAAC;KAAqB;IAGzB,CAAA,yMAAA,YAAQ,EAAE;QACR,OAAO;IACT,GAAG;QAAC;KAAyB;IAE7B,OAAO;2BAAC;8BAAmB;kCAAsB;IAAwB;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAOM,SAAS,yCAA+C,WAAkB;IAC/E,IAAI,QAAQ;IACZ,MAAM,WAAW,GAAG;IACpB,MAAM,kBAAkB,GAAG,IAAM,MAAM,gBAAgB;IACvD,2FAA2F;IAC3F,MAAM,oBAAoB,GAAG,IAAO,MAAc,YAAY;IAC9D,MAAM,OAAO,GAAG,KAAO;IACvB,OAAO;AACT;AAEO,SAAS,0CAAe,KAAY,EAAE,MAAe;IAC1D,OAAO,cAAc,CAAC,OAAO,UAAU;QAAC,OAAO;IAAM;IACrD,OAAO,cAAc,CAAC,OAAO,iBAAiB;QAAC,OAAO;IAAM;AAC9D;AAEO,SAAS,0CAAwD,MAA4C;IAClH,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAE;QACpB,WAAW;QACX,UAAU;IACZ;IAEA,mDAAmD;IAEnD,CAAA,wKAAA,kBAAc,EAAE;QACd,MAAM,QAAQ,SAAS,OAAO;QAC9B,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,QAAQ,CAAC,UAAU;gBACzB,MAAM,QAAQ,GAAG;YACnB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,eAAe,CAAA,uKAAA,iBAAa,EAAE,CAAC;QACjC,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAA,OAAS;IACX;IAEA,wDAAwD;IACxD,OAAO,CAAA,yMAAA,cAAU,EAAE,CAAC;QAClB,wGAAwG;QACxG,sGAAsG;QACtG,6FAA6F;QAC7F,qGAAqG;QACrG,IACE,EAAE,MAAM,YAAY,qBACpB,EAAE,MAAM,YAAY,oBACpB,EAAE,MAAM,YAAY,uBACpB,EAAE,MAAM,YAAY,mBACpB;YACA,SAAS,OAAO,CAAC,SAAS,GAAG;YAE7B,IAAI,SAAS,EAAE,MAAM;YACrB,IAAI,gBAA2D,CAAC;gBAC9D,SAAS,OAAO,CAAC,SAAS,GAAG;gBAE7B,IAAI,OAAO,QAAQ,EAAE;oBACnB,uEAAuE;oBACvE,IAAI,QAAQ,yCAA8C;oBAC1D,aAAa;gBACf;gBAEA,qEAAqE;gBACrE,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;oBAC7B,SAAS,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpC,SAAS,OAAO,CAAC,QAAQ,GAAG;gBAC9B;YACF;YAEA,OAAO,gBAAgB,CAAC,YAAY,eAAe;gBAAC,MAAM;YAAI;YAE9D,SAAS,OAAO,CAAC,QAAQ,GAAG,IAAI,iBAAiB;gBAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,IAAI,OAAO,QAAQ,EAAE;wBACjD;qBAAA,6BAAA,SAAS,OAAO,CAAC,QAAQ,MAAA,QAAzB,+BAAA,KAAA,IAAA,KAAA,IAAA,2BAA2B,UAAU;oBACrC,IAAI,kBAAkB,WAAW,SAAS,aAAa,GAAG,OAAO,SAAS,aAAa;oBACvF,OAAO,aAAa,CAAC,IAAI,WAAW,QAAQ;wBAAC,eAAe;oBAAe;oBAC3E,OAAO,aAAa,CAAC,IAAI,WAAW,YAAY;wBAAC,SAAS;wBAAM,eAAe;oBAAe;gBAChG;YACF;YAEA,SAAS,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;gBAAC,YAAY;gBAAM,iBAAiB;oBAAC;iBAAW;YAAA;QAC5F;IACF,GAAG;QAAC;KAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,MAA+B;IAC1D,uEAAuE;IACvE,MAAO,UAAU,CAAC,CAAA,oKAAA,cAAU,EAAE,QAC5B,SAAS,OAAO,aAAa;IAG/B,IAAI,SAAS,CAAA,mKAAA,iBAAa,EAAE;IAC5B,IAAI,gBAAgB,OAAO,QAAQ,CAAC,aAAa;IACjD,IAAI,CAAC,iBAAiB,kBAAkB,QACtC;IAGF,4CAAmB;IACnB,IAAI,eAAe;IACnB,IAAI,SAAS,CAAC;QACZ,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAChC,EAAE,wBAAwB;IAE9B;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAAc;YAC9C,EAAE,wBAAwB;YAE1B,qEAAqE;YACrE,6CAA6C;YAC7C,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC5B,eAAe;gBACf,CAAA,8KAAA,wBAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,EAAE,MAAM,KAAK,UAAU,cACzB,EAAE,wBAAwB;IAE9B;IAEA,IAAI,YAAY,CAAC;QACf,IAAI,EAAE,MAAM,KAAK,UAAU,cAAc;YACvC,EAAE,wBAAwB;YAE1B,IAAI,CAAC,cAAc;gBACjB,eAAe;gBACf,CAAA,8KAAA,wBAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,OAAO,gBAAgB,CAAC,QAAQ,QAAQ;IACxC,OAAO,gBAAgB,CAAC,YAAY,YAAY;IAChD,OAAO,gBAAgB,CAAC,WAAW,WAAW;IAC9C,OAAO,gBAAgB,CAAC,SAAS,SAAS;IAE1C,IAAI,UAAU;QACZ,qBAAqB;QACrB,OAAO,mBAAmB,CAAC,QAAQ,QAAQ;QAC3C,OAAO,mBAAmB,CAAC,YAAY,YAAY;QACnD,OAAO,mBAAmB,CAAC,WAAW,WAAW;QACjD,OAAO,mBAAmB,CAAC,SAAS,SAAS;QAC7C,4CAAmB;QACnB,eAAe;IACjB;IAEA,IAAI,MAAM,sBAAsB;IAChC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "file": "useFocusVisible.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocusVisible.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAuBlH,IAAI,wCAAmC;AACvC,IAAI,uCAAiB,IAAI;AAIlB,IAAI,4CAA0B,IAAI,OAAmC,yFAAyF;AACrK,IAAI,4CAAsB;AAC1B,IAAI,iDAA2B;AAE/B,sEAAsE;AACtE,MAAM,iDAA2B;IAC/B,KAAK;IACL,QAAQ;AACV;AAEA,SAAS,4CAAsB,QAAkB,EAAE,CAAe;IAChE,KAAK,IAAI,WAAW,qCAClB,QAAQ,UAAU;AAEtB;AAEA;;CAEC,GACD,SAAS,iCAAW,CAAgB;IAClC,gFAAgF;IAChF,OAAO,CAAE,CAAA,EAAE,OAAO,IAAK,CAAC,CAAA,iKAAA,QAAI,OAAO,EAAE,MAAM,IAAK,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,MAAK;AAC1H;AAGA,SAAS,0CAAoB,CAAgB;IAC3C,4CAAsB;IACtB,IAAI,iCAAW,IAAI;QACjB,wCAAkB;QAClB,4CAAsB,YAAY;IACpC;AACF;AAEA,SAAS,yCAAmB,CAA4B;IACtD,wCAAkB;IAClB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,eAAe;QACtD,4CAAsB;QACtB,4CAAsB,WAAW;IACnC;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,IAAI,CAAA,uKAAA,iBAAa,EAAE,IAAI;QACrB,4CAAsB;QACtB,wCAAkB;IACpB;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,kFAAkF;IAClF,kFAAkF;IAClF,wCAAwC;IACxC,IAAI,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,YAAY,CAAA,qKAAA,mBAAe,KAAK,CAAC,EAAE,SAAS,EAClF;IAGF,qGAAqG;IACrG,0FAA0F;IAC1F,IAAI,CAAC,6CAAuB,CAAC,gDAA0B;QACrD,wCAAkB;QAClB,4CAAsB,WAAW;IACnC;IAEA,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA,SAAS;IACP,IAAI,qKAAA,mBAAA,EACF;IAGF,6FAA6F;IAC7F,8DAA8D;IAC9D,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA;;CAEC,GACD,SAAS,6CAAuB,OAA4B;IAC1D,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,0CAAwB,GAAG,CAAC,CAAA,mKAAA,iBAAa,EAAE,WACjH;IAGF,MAAM,eAAe,CAAA,mKAAA,iBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,mKAAA,mBAAe,EAAE;IAExC,0EAA0E;IAC1E,2EAA2E;IAC3E,0EAA0E;IAC1E,6DAA6D;IAC7D,IAAI,QAAQ,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK;IACpD,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG;QACzC,4CAAsB;QACtB,MAAM,KAAK,CAAC,IAAI,EAAE;IACpB;IAEA,eAAe,gBAAgB,CAAC,WAAW,2CAAqB;IAChE,eAAe,gBAAgB,CAAC,SAAS,2CAAqB;IAC9D,eAAe,gBAAgB,CAAC,SAAS,wCAAkB;IAE3D,iEAAiE;IACjE,+DAA+D;IAC/D,aAAa,gBAAgB,CAAC,SAAS,wCAAkB;IACzD,aAAa,gBAAgB,CAAC,QAAQ,wCAAkB;IAExD,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,aAAa,0CAAoB;IACnE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;IAI5C;IAEA,sBAAsB;IACtB,aAAa,gBAAgB,CAAC,gBAAgB;QAC5C,kDAA4B;IAC9B,GAAG;QAAC,MAAM;IAAI;IAEd,0CAAwB,GAAG,CAAC,cAAc;eAAC;IAAK;AAClD;AAEA,MAAM,oDAA8B,CAAC,SAAS;IAC5C,MAAM,eAAe,CAAA,mKAAA,iBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,mKAAA,mBAAe,EAAE;IACxC,IAAI,cACF,eAAe,mBAAmB,CAAC,oBAAoB;IAEzD,IAAI,CAAC,0CAAwB,GAAG,CAAC,eAC/B;IAEF,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,0CAAwB,GAAG,CAAC,cAAe,KAAK;IAE3F,eAAe,mBAAmB,CAAC,WAAW,2CAAqB;IACnE,eAAe,mBAAmB,CAAC,SAAS,2CAAqB;IACjE,eAAe,mBAAmB,CAAC,SAAS,wCAAkB;IAE9D,aAAa,mBAAmB,CAAC,SAAS,wCAAkB;IAC5D,aAAa,mBAAmB,CAAC,QAAQ,wCAAkB;IAE3D,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,aAAa,0CAAoB;IACtE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;IAI5C;IAEA,0CAAwB,MAAM,CAAC;AACjC;AAmBO,SAAS,0CAAuB,OAA4B;IACjE,MAAM,iBAAiB,CAAA,mKAAA,mBAAe,EAAE;IACxC,IAAI;IACJ,IAAI,eAAe,UAAU,KAAK,WAChC,6CAAuB;SAClB;QACL,eAAe;YACb,6CAAuB;QACzB;QACA,eAAe,gBAAgB,CAAC,oBAAoB;IACtD;IAEA,OAAO,IAAM,kDAA4B,SAAS;AACpD;AAEA,kEAAkE;AAClE,iDAAiD;AACjD,IAAI,OAAO,aAAa,aACtB;AAMK,SAAS;IACd,OAAO,0CAAoB;AAC7B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,0CAAuB,QAAkB;IACvD,wCAAkB;IAClB,4CAAsB,UAAU;AAClC;AAKO,SAAS;IACd;IAEA,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,CAAA,yMAAA,YAAQ,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;QACd;QAEA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,GAAG,EAAE;IAEL,OAAO,CAAA,kKAAA,WAAO,MAAM,OAAO;AAC7B;AAEA,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED;;;CAGC,GACD,SAAS,2CAAqB,WAAoB,EAAE,QAAkB,EAAE,CAAe;IACrF,IAAI,YAAW,CAAA,mKAAA,mBAAe,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM;IACzC,MAAM,oBAAoB,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,gBAAgB,GAAG;IAClH,MAAM,uBAAuB,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,mBAAmB,GAAG;IACxH,MAAM,eAAe,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,WAAW,GAAG;IACxG,MAAM,iBAAiB,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,aAAa,GAAG;IAE5G,qKAAqK;IACrK,kIAAkI;IAClI,cAAc,eACX,UAAS,aAAa,YAAY,qBAAqB,CAAC,wCAAkB,GAAG,CAAC,UAAS,aAAa,CAAC,IAAI,KAC1G,UAAS,aAAa,YAAY,wBACjC,UAAS,aAAa,YAAY,gBAAgB,UAAS,aAAa,CAAC,iBAAiB;IAC7F,OAAO,CAAE,CAAA,eAAe,aAAa,cAAc,aAAa,kBAAkB,CAAC,8CAAwB,CAAC,EAAE,GAAG,CAAA;AACnH;AAKO,SAAS,0CAAgB,QAA2B,CAAC,CAAC;IAC3D,IAAI,EAAA,aAAC,WAAW,EAAA,WAAE,SAAS,EAAC,GAAG;IAC/B,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,yMAAA,WAAO,EAAE,aAAa;IACnE,0CAAwB,CAAC;QACvB,gBAAgB;IAClB,GAAG;QAAC;KAAY,EAAE;qBAAC;IAAW;IAE9B,OAAO;QAAC,gBAAgB;IAAmB;AAC7C;AAKO,SAAS,0CAAwB,EAAuB,EAAE,IAAwB,EAAE,IAA8B;IACvH;IAEA,CAAA,yMAAA,YAAQ,EAAE;QACR,IAAI,UAAU,CAAC,UAAoB;YACjC,0GAA0G;YAC1G,IAAI,CAAC,2CAAqB,CAAC,CAAA,CAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,WAAW,GAAG,UAAU,IACzD;YAEF,GAAG;QACL;QACA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,uDAAuD;IACvD,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1503, "column": 0}, "map": {"version": 3, "file": "useFocus.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocus.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAqB3G,SAAS,0CAA6D,KAAyB;IACpG,IAAI,EAAA,YACF,UAAU,EACV,SAAS,WAAW,EACpB,QAAQ,UAAU,EAAA,eAClB,aAAa,EACd,GAAG;IAEJ,MAAM,SAAuC,CAAA,yMAAA,cAAU,EAAE,CAAC;QACxD,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC,IAAI,YACF,WAAW;YAGb,IAAI,eACF,cAAc;YAGhB,OAAO;QACT;IACF,GAAG;QAAC;QAAY;KAAc;IAG9B,MAAM,mBAAmB,CAAA,qKAAA,wBAAoB,EAAU;IAEvD,MAAM,UAAyC,CAAA,yMAAA,cAAU,EAAE,CAAC;QAC1D,kGAAkG;QAClG,oDAAoD;QAEpD,MAAM,gBAAgB,CAAA,mKAAA,mBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,gBAAgB,CAAA,qKAAA,mBAAe,EAAE,iBAAiB,CAAA,qKAAA,mBAAe;QACvF,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,kBAAkB,CAAA,qKAAA,iBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,aACF,YAAY;YAGd,IAAI,eACF,cAAc;YAGhB,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAe;QAAa;KAAiB;IAEjD,OAAO;QACL,YAAY;YACV,SAAU,CAAC,cAAe,CAAA,eAAe,iBAAiB,UAAS,IAAM,UAAU;YACnF,QAAS,CAAC,cAAe,CAAA,cAAc,aAAY,IAAM,SAAS;QACpE;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "file": "useFocusWithin.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocusWithin.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {createSyntheticEvent, setEventTarget, useSyntheticBlurEvent} from './utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {FocusEvent, useCallback, useRef} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\n\nexport interface FocusWithinProps {\n  /** Whether the focus within events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when the target element or a descendant receives focus. */\n  onFocusWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the target element and all descendants lose focus. */\n  onBlurWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the the focus within state changes. */\n  onFocusWithinChange?: (isFocusWithin: boolean) => void\n}\n\nexport interface FocusWithinResult {\n  /** Props to spread onto the target element. */\n  focusWithinProps: DOMAttributes\n}\n\n/**\n * Handles focus events for the target and its descendants.\n */\nexport function useFocusWithin(props: FocusWithinProps): FocusWithinResult {\n  let {\n    isDisabled,\n    onBlurWithin,\n    onFocusWithin,\n    onFocusWithinChange\n  } = props;\n  let state = useRef({\n    isFocusWithin: false\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let onBlur = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !(e.currentTarget as Element).contains(e.relatedTarget as Element)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n\n      if (onBlurWithin) {\n        onBlurWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(false);\n      }\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n\n  let onSyntheticFocus = useSyntheticBlurEvent(onBlur);\n  let onFocus = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = getActiveElement(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusWithin) {\n        onFocusWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(true);\n      }\n\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !nodeContains(currentTarget, e.target as Element)) {\n          let nativeEvent = new ownerDocument.defaultView!.FocusEvent('blur', {relatedTarget: e.target});\n          setEventTarget(nativeEvent, currentTarget);\n          let event = createSyntheticEvent<FocusEvent>(nativeEvent);\n          onBlur(event);\n        }\n      }, {capture: true});\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n\n  if (isDisabled) {\n    return {\n      focusWithinProps: {\n        // These cannot be null, that would conflict in mergeProps\n        onFocus: undefined,\n        onBlur: undefined\n      }\n    };\n  }\n\n  return {\n    focusWithinProps: {\n      onFocus,\n      onBlur\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AA0B3G,SAAS,0CAAe,KAAuB;IACpD,IAAI,EAAA,YACF,UAAU,EAAA,cACV,YAAY,EAAA,eACZ,aAAa,EAAA,qBACb,mBAAmB,EACpB,GAAG;IACJ,IAAI,QAAQ,CAAA,yMAAA,SAAK,EAAE;QACjB,eAAe;IACjB;IAEA,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,2KAAA,qBAAiB;IAErE,IAAI,SAAS,CAAA,yMAAA,cAAU,EAAE,CAAC;QACxB,0CAA0C;QAC1C,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,iFAAiF;QACjF,kFAAkF;QAClF,qDAAqD;QACrD,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAE,EAAE,aAAa,CAAa,QAAQ,CAAC,EAAE,aAAa,GAAc;YACrG,MAAM,OAAO,CAAC,aAAa,GAAG;YAC9B;YAEA,IAAI,cACF,aAAa;YAGf,IAAI,qBACF,oBAAoB;QAExB;IACF,GAAG;QAAC;QAAc;QAAqB;QAAO;KAAyB;IAEvE,IAAI,mBAAmB,CAAA,qKAAA,wBAAoB,EAAE;IAC7C,IAAI,UAAU,CAAA,yMAAA,cAAU,EAAE,CAAC;QACzB,0CAA0C;QAC1C,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,kGAAkG;QAClG,oDAAoD;QACpD,MAAM,gBAAgB,CAAA,mKAAA,mBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,CAAA,qKAAA,mBAAe,EAAE;QACvC,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,IAAI,kBAAkB,CAAA,qKAAA,iBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,eACF,cAAc;YAGhB,IAAI,qBACF,oBAAoB;YAGtB,MAAM,OAAO,CAAC,aAAa,GAAG;YAC9B,iBAAiB;YAEjB,0EAA0E;YAC1E,0EAA0E;YAC1E,4BAA4B;YAC5B,IAAI,gBAAgB,EAAE,aAAa;YACnC,kBAAkB,eAAe,SAAS,CAAA;gBACxC,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAC,CAAA,qKAAA,eAAW,EAAE,eAAe,EAAE,MAAM,GAAc;oBACpF,IAAI,cAAc,IAAI,cAAc,WAAW,CAAE,UAAU,CAAC,QAAQ;wBAAC,eAAe,EAAE,MAAM;oBAAA;oBAC5F,CAAA,qKAAA,iBAAa,EAAE,aAAa;oBAC5B,IAAI,QAAQ,CAAA,qKAAA,uBAAmB,EAAc;oBAC7C,OAAO;gBACT;YACF,GAAG;gBAAC,SAAS;YAAI;QACnB;IACF,GAAG;QAAC;QAAe;QAAqB;QAAkB;QAAmB;KAAO;IAEpF,IAAI,YACF,OAAO;QACL,kBAAkB;YAChB,0DAA0D;YAC1D,SAAS;YACT,QAAQ;QACV;IACF;IAGF,OAAO;QACL,kBAAkB;qBAChB;oBACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "file": "useHover.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useHover.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (typeof PointerEvent !== 'undefined') {\n    document.addEventListener('pointerup', handleGlobalPointerEvent);\n  } else if (process.env.NODE_ENV === 'test') {\n    document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAiBlH,oGAAoG;AACpG,iFAAiF;AACjF,sDAAsD;AACtD,IAAI,wDAAkC;AACtC,IAAI,mCAAa;AAEjB,SAAS;IACP,wDAAkC;IAElC,wFAAwF;IACxF,sFAAsF;IACtF,wFAAwF;IACxF,oEAAoE;IACpE,WAAW;QACT,wDAAkC;IACpC,GAAG;AACL;AAEA,SAAS,+CAAyB,CAAC;IACjC,IAAI,EAAE,WAAW,KAAK,SACpB;AAEJ;AAEA,SAAS;IACP,IAAI,OAAO,aAAa,aACtB;IAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,gBAAgB,CAAC,aAAa;SAClC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAClC,SAAS,gBAAgB,CAAC,YAAY;;IAAA;IAGxC;IACA,OAAO;QACL;QACA,IAAI,mCAAa,GACf;QAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,mBAAmB,CAAC,aAAa;aACrC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAClC,SAAS,mBAAmB,CAAC,YAAY;;QAAA;IAE7C;AACF;AAMO,SAAS,0CAAS,KAAiB;IACxC,IAAI,EAAA,cACF,YAAY,EAAA,eACZ,aAAa,EAAA,YACb,UAAU,EAAA,YACV,UAAU,EACX,GAAG;IAEJ,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,IAAI,QAAQ,CAAA,yMAAA,SAAK,EAAE;QACjB,WAAW;QACX,2BAA2B;QAC3B,aAAa;QACb,QAAQ;IACV,GAAG,OAAO;IAEV,CAAA,yMAAA,YAAQ,EAAE,8CAAwB,EAAE;IACpC,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,2KAAA,qBAAiB;IAErE,IAAI,EAAA,YAAC,UAAU,EAAA,iBAAE,eAAe,EAAC,GAAG,CAAA,yMAAA,UAAM,EAAE;QAC1C,IAAI,oBAAoB,CAAC,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,IAAI,cAAc,gBAAgB,WAAW,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxG;YAGF,MAAM,SAAS,GAAG;YAClB,IAAI,SAAS,MAAM,aAAa;YAChC,MAAM,MAAM,GAAG;YAEf,kGAAkG;YAClG,gGAAgG;YAChG,kFAAkF;YAClF,yGAAyG;YACzG,kBAAkB,CAAA,mKAAA,mBAAe,EAAE,MAAM,MAAM,GAAG,eAAe,CAAA;gBAC/D,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI,CAAC,CAAA,qKAAA,eAAW,EAAE,MAAM,MAAM,EAAE,EAAE,MAAM,GACzE,gBAAgB,GAAG,EAAE,WAAW;YAEpC,GAAG;gBAAC,SAAS;YAAI;YAEjB,IAAI,cACF,aAAa;gBACX,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,kBAAkB,CAAC,OAAO;YAC5B,IAAI,SAAS,MAAM,MAAM;YACzB,MAAM,WAAW,GAAG;YACpB,MAAM,MAAM,GAAG;YAEf,IAAI,gBAAgB,WAAW,CAAC,MAAM,SAAS,IAAI,CAAC,QAClD;YAGF,MAAM,SAAS,GAAG;YAClB;YAEA,IAAI,YACF,WAAW;gBACT,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,aAA4B,CAAC;QAEjC,IAAI,OAAO,iBAAiB,aAAa;YACvC,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,yDAAmC,EAAE,WAAW,KAAK,SACvD;gBAGF,kBAAkB,GAAG,EAAE,WAAW;YACpC;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAClD,gBAAgB,GAAG,EAAE,WAAW;YAEpC;QACF,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;QAkB5C;QACA,OAAO;wBAAC;6BAAY;QAAe;IACrC,GAAG;QAAC;QAAc;QAAe;QAAY;QAAY;QAAO;QAAmB;KAAyB;IAE5G,CAAA,yMAAA,YAAQ,EAAE;QACR,iEAAiE;QACjE,qFAAqF;QACrF,IAAI,YACF,gBAAgB;YAAC,eAAe,MAAM,MAAM;QAAA,GAAG,MAAM,WAAW;IAEpE,uDAAuD;IACvD,GAAG;QAAC;KAAW;IAEf,OAAO;oBACL;mBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1833, "column": 0}, "map": {"version": 3, "file": "SSRProvider.module.js.map", "sourceRoot": "../../../../", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/ssr/dist/packages/%40react-aria/ssr/src/SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,0FAA0F;AAC1F,2DAA2D;AAC3D,wDAAwD;AAcxD,iFAAiF;AACjF,kFAAkF;AAClF,+EAA+E;AAC/E,+EAA+E;AAC/E,2DAA2D;AAC3D,MAAM,uCAAkC;IACtC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAC1C,SAAS;AACX;AAEA,MAAM,mCAAA,WAAA,GAAa,CAAA,yMAAA,UAAI,EAAE,aAAa,CAAkB;AACxD,MAAM,qCAAA,WAAA,GAAe,CAAA,yMAAA,UAAI,EAAE,aAAa,CAAC;AAOzC,mCAAmC;AACnC,SAAS,wCAAkB,KAAuB;IAChD,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IACrB,IAAI,UAAU,iCAAW,QAAQ;IACjC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,yMAAA,WAAO,EAAE;IACjC,IAAI,QAAyB,CAAA,yMAAA,UAAM,EAAE,IAAO,CAAA;YAC1C,iFAAiF;YACjF,oCAAoC;YACpC,QAAQ,QAAQ,uCAAiB,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS;YAChE,SAAS;QACX,CAAA,GAAI;QAAC;QAAK;KAAQ;IAElB,qEAAqE;IACrE,yEAAyE;IACzE,IAAI,OAAO,aAAa,aAEtB,AADA,sDACsD,iBADiB;IAEvE,sDAAsD;IACtD,CAAA,yMAAA,kBAAc,EAAE;QACd,SAAS;IACX,GAAG,EAAE;IAGP,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,iCAAW,QAAQ,EAAA;QAAC,OAAO;qBAC1B,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,mCAAa,QAAQ,EAAA;QAAC,OAAO;OAC3B,MAAM,QAAQ;AAIvB;AAEA,IAAI,+CAAyB;AAMtB,SAAS,0CAAY,KAAuB;IACjD,IAAI,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,QAAQ,KAAK,YAAY;QACxC,IAAI,QAAQ,GAAG,CAAC,QAAQ,gCAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,gBAAgB,CAAC,8CAAwB;YACvG,QAAQ,IAAI,CAAC;YACb,+CAAyB;QAC3B;QACA,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAA,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,QAAA,EAAA,MAAG,MAAM,QAAQ;IAC1B;IACA,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,yCAAsB;AAChC;AAEA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAG/B,IAAI,qCAAe,IAAI;AAEvB,SAAS,iCAAW,aAAa,KAAK;IACpC,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IACrB,IAAI,MAAM,CAAA,yMAAA,SAAK,EAAiB;IAChC,gDAAgD;IAChD,IAAI,IAAI,OAAO,KAAK,QAAQ,CAAC,YAAY;YAWpB,6EAAA;QAVnB,0GAA0G;QAC1G,wGAAwG;QACxG,uGAAuG;QACvG,+GAA+G;QAC/G,gHAAgH;QAChH,uHAAuH;QACvH,2GAA2G;QAC3G,yGAAyG;QACzG,gFAAgF;QAChF,aAAa;QACb,IAAI,eAAA,CAAe,4DAAA,CAAA,yMAAA,UAAI,EAAE,kDAAkD,MAAA,QAAxD,8DAAA,KAAA,IAAA,KAAA,IAAA,CAAA,8EAAA,0DAA0D,iBAAiB,MAAA,QAA3E,gFAAA,KAAA,IAAA,KAAA,IAAA,4EAA6E,OAAO;QACvG,IAAI,cAAc;YAChB,IAAI,qBAAqB,mCAAa,GAAG,CAAC;YAC1C,IAAI,sBAAsB,MACxB,AACA,mCAAa,GAAG,CAAC,cAAc,mCADyD;gBAEtF,IAAI,IAAI,OAAO;gBACf,OAAO,aAAa,aAAa;YACnC;iBACK,IAAI,aAAa,aAAa,KAAK,mBAAmB,KAAK,EAAE;gBAClE,+DAA+D;gBAC/D,8DAA8D;gBAC9D,sCAAsC;gBACtC,IAAI,OAAO,GAAG,mBAAmB,EAAE;gBACnC,mCAAa,MAAM,CAAC;YACtB;QACF;QAEA,gDAAgD;QAChD,IAAI,OAAO,GAAG,EAAE,IAAI,OAAO;IAC7B;IAEA,gDAAgD;IAChD,OAAO,IAAI,OAAO;AACpB;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IAErB,4EAA4E;IAC5E,yDAAyD;IACzD,IAAI,QAAQ,wCAAkB,CAAC,mCAAa,QAAQ,GAAG,CAAC,QAAQ,gCAAK,cACnE,QAAQ,IAAI,CAAC;IAGf,IAAI,UAAU,iCAAW,CAAC,CAAC;IAC3B,IAAI,SAAS,QAAQ,wCAAkB,QAAQ,GAAG,CAAC,QAAQ,KAAK,IAAwB,CAAC,IAAhB,MAA0B,EAAE,IAAI,MAAM,EAAE;IACjH,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,SAAS;AAC5C;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,KAAK,CAAA,yMAAA,UAAI,EAAE,KAAK;IACpB,IAAI,CAAC,OAAO,GAAG,CAAA,yMAAA,WAAO,EAAE;IACxB,IAAI,SAAS,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,SAAS,eAAe,CAAC,UAAU,EAAE,qCAAe,MAAM,EAAE;IAC5G,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,IAAI;AACvC;AAIO,MAAM,4CAAe,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,QAAQ,KAAK,aAAa,2CAAqB;AAExF,SAAS;IACP,OAAO;AACT;AAEA,SAAS;IACP,OAAO;AACT;AAEA,6DAA6D;AAC7D,SAAS,gCAAU,aAAyB;IAC1C,OAAO;IACP,OAAO,KAAO;AAChB;AAOO,SAAS;IACd,iGAAiG;IACjG,IAAI,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,uBAAuB,KAAK,YAC3C,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,uBAAuB,CAAC,iCAAW,mCAAa;IAG/D,sDAAsD;IACtD,OAAO,CAAA,yMAAA,aAAS,EAAE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "file": "module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-stately/flags/dist/packages/%40react-stately/flags/src/index.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;AAED,IAAI,yCAAmB;AACvB,IAAI,mCAAa;AAEV,SAAS;IACd,yCAAmB;AACrB;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,mCAAa;AACf;AAEO,SAAS;IACd,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2020, "column": 0}, "map": {"version": 3, "file": "useFocusRing.module.js.map", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/useFocusRing.ts"], "sourcesContent": ["import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAoCO,SAAS,0CAAa,QAA4B,CAAC,CAAC;IACzD,IAAI,EAAA,WACF,YAAY,KAAA,EAAA,aACZ,WAAW,EAAA,QACX,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ,CAAA,yMAAA,SAAK,EAAE;QACjB,WAAW;QACX,gBAAgB,aAAa,CAAA,+KAAA,iBAAa;IAC5C;IACA,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,yMAAA,WAAO,EAAE,IAAM,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc;IAEnH,IAAI,cAAc,CAAA,yMAAA,cAAU,EAAE,IAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc,GAAG,EAAE;IAEhH,IAAI,gBAAgB,CAAA,yMAAA,cAAU,EAAE,CAAA;QAC9B,MAAM,OAAO,CAAC,SAAS,GAAG;QAC1B,WAAW;QACX;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,+KAAA,0BAAsB,EAAE,CAAC;QACvB,MAAM,OAAO,CAAC,cAAc,GAAG;QAC/B;IACF,GAAG,EAAE,EAAE;qBAAC;IAAW;IAEnB,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;QAC1B,YAAY;uBACZ;IACF;IAEA,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,8KAAA,iBAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB;IACvB;IAEA,OAAO;mBACL;QACA,gBAAgB;QAChB,YAAY,SAAS,mBAAmB;IAC1C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40floating-ui/utils/dist/floating-ui.utils.mjs"], "sourcesContent": ["/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;AAED,MAAM,QAAQ;IAAC;IAAO;IAAS;IAAU;CAAO;AAChD,MAAM,aAAa;IAAC;IAAS;CAAM;AACnC,MAAM,aAAa,WAAW,GAAE,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,IAAI,MAAM,CAAC,MAAM,OAAO,MAAM,UAAU,CAAC,EAAE,EAAE,OAAO,MAAM,UAAU,CAAC,EAAE,GAAG,EAAE;AACxI,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,eAAe,CAAA,IAAK,CAAC;QACzB,GAAG;QACH,GAAG;IACL,CAAC;AACD,MAAM,kBAAkB;IACtB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;AACP;AACA,MAAM,uBAAuB;IAC3B,OAAO;IACP,KAAK;AACP;AACA,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG;IAC9B,OAAO,IAAI,OAAO,IAAI,OAAO;AAC/B;AACA,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,OAAO,OAAO,UAAU,aAAa,MAAM,SAAS;AACtD;AACA,SAAS,QAAQ,SAAS;IACxB,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC;AACA,SAAS,aAAa,SAAS;IAC7B,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC;AACA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,IAAI;IACzB,OAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,SAAS;IAC5B,OAAO;QAAC;QAAO;KAAS,CAAC,QAAQ,CAAC,QAAQ,cAAc,MAAM;AAChE;AACA,SAAS,iBAAiB,SAAS;IACjC,OAAO,gBAAgB,YAAY;AACrC;AACA,SAAS,kBAAkB,SAAS,EAAE,KAAK,EAAE,GAAG;IAC9C,IAAI,QAAQ,KAAK,GAAG;QAClB,MAAM;IACR;IACA,MAAM,YAAY,aAAa;IAC/B,MAAM,gBAAgB,iBAAiB;IACvC,MAAM,SAAS,cAAc;IAC7B,IAAI,oBAAoB,kBAAkB,MAAM,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,UAAU,SAAS,cAAc,UAAU,WAAW;IAC9I,IAAI,MAAM,SAAS,CAAC,OAAO,GAAG,MAAM,QAAQ,CAAC,OAAO,EAAE;QACpD,oBAAoB,qBAAqB;IAC3C;IACA,OAAO;QAAC;QAAmB,qBAAqB;KAAmB;AACrE;AACA,SAAS,sBAAsB,SAAS;IACtC,MAAM,oBAAoB,qBAAqB;IAC/C,OAAO;QAAC,8BAA8B;QAAY;QAAmB,8BAA8B;KAAmB;AACxH;AACA,SAAS,8BAA8B,SAAS;IAC9C,OAAO,UAAU,OAAO,CAAC,cAAc,CAAA,YAAa,oBAAoB,CAAC,UAAU;AACrF;AACA,SAAS,YAAY,IAAI,EAAE,OAAO,EAAE,GAAG;IACrC,MAAM,KAAK;QAAC;QAAQ;KAAQ;IAC5B,MAAM,KAAK;QAAC;QAAS;KAAO;IAC5B,MAAM,KAAK;QAAC;QAAO;KAAS;IAC5B,MAAM,KAAK;QAAC;QAAU;KAAM;IAC5B,OAAQ;QACN,KAAK;QACL,KAAK;YACH,IAAI,KAAK,OAAO,UAAU,KAAK;YAC/B,OAAO,UAAU,KAAK;QACxB,KAAK;QACL,KAAK;YACH,OAAO,UAAU,KAAK;QACxB;YACE,OAAO,EAAE;IACb;AACF;AACA,SAAS,0BAA0B,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG;IACzE,MAAM,YAAY,aAAa;IAC/B,IAAI,OAAO,YAAY,QAAQ,YAAY,cAAc,SAAS;IAClE,IAAI,WAAW;QACb,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,OAAO,MAAM;QACrC,IAAI,eAAe;YACjB,OAAO,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,SAAS;IACrC,OAAO,UAAU,OAAO,CAAC,0BAA0B,CAAA,OAAQ,eAAe,CAAC,KAAK;AAClF;AACA,SAAS,oBAAoB,OAAO;IAClC,OAAO;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;QACN,GAAG,OAAO;IACZ;AACF;AACA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,OAAO,YAAY,WAAW,oBAAoB,WAAW;QAClE,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF;AACA,SAAS,iBAAiB,IAAI;IAC5B,MAAM,EACJ,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACP,GAAG;IACJ,OAAO;QACL;QACA;QACA,KAAK;QACL,MAAM;QACN,OAAO,IAAI;QACX,QAAQ,IAAI;QACZ;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40floating-ui/utils/dist/floating-ui.utils.dom.mjs"], "sourcesContent": ["function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS;IACP,OAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,OAAO;QAChB,OAAO,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,WAAW;IAC1C;IACA,wEAAwE;IACxE,sDAAsD;IACtD,yDAAyD;IACzD,OAAO;AACT;AACA,SAAS,UAAU,IAAI;IACrB,IAAI;IACJ,OAAO,CAAC,QAAQ,QAAQ,CAAC,sBAAsB,KAAK,aAAa,KAAK,OAAO,KAAK,IAAI,oBAAoB,WAAW,KAAK;AAC5H;AACA,SAAS,mBAAmB,IAAI;IAC9B,IAAI;IACJ,OAAO,CAAC,OAAO,CAAC,OAAO,QAAQ,KAAK,aAAa,GAAG,KAAK,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO,KAAK,IAAI,KAAK,eAAe;AAChI;AACA,SAAS,OAAO,KAAK;IACnB,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,iBAAiB,QAAQ,iBAAiB,UAAU,OAAO,IAAI;AACxE;AACA,SAAS,UAAU,KAAK;IACtB,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,iBAAiB,WAAW,iBAAiB,UAAU,OAAO,OAAO;AAC9E;AACA,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,iBAAiB,eAAe,iBAAiB,UAAU,OAAO,WAAW;AACtF;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,CAAC,eAAe,OAAO,eAAe,aAAa;QACrD,OAAO;IACT;IACA,OAAO,iBAAiB,cAAc,iBAAiB,UAAU,OAAO,UAAU;AACpF;AACA,SAAS,kBAAkB,OAAO;IAChC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,SAAS,EACT,OAAO,EACR,GAAG,iBAAiB;IACrB,OAAO,kCAAkC,IAAI,CAAC,WAAW,YAAY,cAAc,CAAC;QAAC;QAAU;KAAW,CAAC,QAAQ,CAAC;AACtH;AACA,SAAS,eAAe,OAAO;IAC7B,OAAO;QAAC;QAAS;QAAM;KAAK,CAAC,QAAQ,CAAC,YAAY;AACpD;AACA,SAAS,WAAW,OAAO;IACzB,OAAO;QAAC;QAAiB;KAAS,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI;YACF,OAAO,QAAQ,OAAO,CAAC;QACzB,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;AACF;AACA,SAAS,kBAAkB,YAAY;IACrC,MAAM,SAAS;IACf,MAAM,MAAM,UAAU,gBAAgB,iBAAiB,gBAAgB;IAEvE,qGAAqG;IACrG,mEAAmE;IACnE,OAAO;QAAC;QAAa;QAAa;QAAS;QAAU;KAAc,CAAC,IAAI,CAAC,CAAA,QAAS,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,SAAS,UAAU,CAAC,IAAI,aAAa,GAAG,IAAI,aAAa,KAAK,WAAW,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,cAAc,GAAG,IAAI,cAAc,KAAK,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,MAAM,GAAG,IAAI,MAAM,KAAK,SAAS,KAAK,KAAK;QAAC;QAAa;QAAa;QAAS;QAAU;QAAe;KAAS,CAAC,IAAI,CAAC,CAAA,QAAS,CAAC,IAAI,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,WAAW;QAAC;QAAS;QAAU;QAAU;KAAU,CAAC,IAAI,CAAC,CAAA,QAAS,CAAC,IAAI,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AAC7hB;AACA,SAAS,mBAAmB,OAAO;IACjC,IAAI,cAAc,cAAc;IAChC,MAAO,cAAc,gBAAgB,CAAC,sBAAsB,aAAc;QACxE,IAAI,kBAAkB,cAAc;YAClC,OAAO;QACT,OAAO,IAAI,WAAW,cAAc;YAClC,OAAO;QACT;QACA,cAAc,cAAc;IAC9B;IACA,OAAO;AACT;AACA,SAAS;IACP,IAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,QAAQ,EAAE,OAAO;IACxD,OAAO,IAAI,QAAQ,CAAC,2BAA2B;AACjD;AACA,SAAS,sBAAsB,IAAI;IACjC,OAAO;QAAC;QAAQ;QAAQ;KAAY,CAAC,QAAQ,CAAC,YAAY;AAC5D;AACA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,UAAU,SAAS,gBAAgB,CAAC;AAC7C;AACA,SAAS,cAAc,OAAO;IAC5B,IAAI,UAAU,UAAU;QACtB,OAAO;YACL,YAAY,QAAQ,UAAU;YAC9B,WAAW,QAAQ,SAAS;QAC9B;IACF;IACA,OAAO;QACL,YAAY,QAAQ,OAAO;QAC3B,WAAW,QAAQ,OAAO;IAC5B;AACF;AACA,SAAS,cAAc,IAAI;IACzB,IAAI,YAAY,UAAU,QAAQ;QAChC,OAAO;IACT;IACA,MAAM,SACN,4DAA4D;IAC5D,KAAK,YAAY,IACjB,wBAAwB;IACxB,KAAK,UAAU,IACf,uBAAuB;IACvB,aAAa,SAAS,KAAK,IAAI,IAC/B,YAAY;IACZ,mBAAmB;IACnB,OAAO,aAAa,UAAU,OAAO,IAAI,GAAG;AAC9C;AACA,SAAS,2BAA2B,IAAI;IACtC,MAAM,aAAa,cAAc;IACjC,IAAI,sBAAsB,aAAa;QACrC,OAAO,KAAK,aAAa,GAAG,KAAK,aAAa,CAAC,IAAI,GAAG,KAAK,IAAI;IACjE;IACA,IAAI,cAAc,eAAe,kBAAkB,aAAa;QAC9D,OAAO;IACT;IACA,OAAO,2BAA2B;AACpC;AACA,SAAS,qBAAqB,IAAI,EAAE,IAAI,EAAE,eAAe;IACvD,IAAI;IACJ,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,EAAE;IACX;IACA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,MAAM,qBAAqB,2BAA2B;IACtD,MAAM,SAAS,uBAAuB,CAAC,CAAC,uBAAuB,KAAK,aAAa,KAAK,OAAO,KAAK,IAAI,qBAAqB,IAAI;IAC/H,MAAM,MAAM,UAAU;IACtB,IAAI,QAAQ;QACV,MAAM,eAAe,gBAAgB;QACrC,OAAO,KAAK,MAAM,CAAC,KAAK,IAAI,cAAc,IAAI,EAAE,EAAE,kBAAkB,sBAAsB,qBAAqB,EAAE,EAAE,gBAAgB,kBAAkB,qBAAqB,gBAAgB,EAAE;IAC9L;IACA,OAAO,KAAK,MAAM,CAAC,oBAAoB,qBAAqB,oBAAoB,EAAE,EAAE;AACtF;AACA,SAAS,gBAAgB,GAAG;IAC1B,OAAO,IAAI,MAAM,IAAI,OAAO,cAAc,CAAC,IAAI,MAAM,IAAI,IAAI,YAAY,GAAG;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40floating-ui/core/dist/floating-ui.core.mjs"], "sourcesContent": ["import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGA,SAAS,2BAA2B,IAAI,EAAE,SAAS,EAAE,GAAG;IACtD,IAAI,EACF,SAAS,EACT,QAAQ,EACT,GAAG;IACJ,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,MAAM,cAAc,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;IAClC,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE;IACrB,MAAM,aAAa,aAAa;IAChC,MAAM,UAAU,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG;IACrE,MAAM,UAAU,UAAU,CAAC,GAAG,UAAU,MAAM,GAAG,IAAI,SAAS,MAAM,GAAG;IACvE,MAAM,cAAc,SAAS,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC,YAAY,GAAG;IACzE,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,SAAS;gBACP,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,SAAS,MAAM;YAClC;YACA;QACF,KAAK;YACH,SAAS;gBACP,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,UAAU,MAAM;YACnC;YACA;QACF,KAAK;YACH,SAAS;gBACP,GAAG,UAAU,CAAC,GAAG,UAAU,KAAK;gBAChC,GAAG;YACL;YACA;QACF,KAAK;YACH,SAAS;gBACP,GAAG,UAAU,CAAC,GAAG,SAAS,KAAK;gBAC/B,GAAG;YACL;YACA;QACF;YACE,SAAS;gBACP,GAAG,UAAU,CAAC;gBACd,GAAG,UAAU,CAAC;YAChB;IACJ;IACA,OAAQ,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;QACnB,KAAK;YACH,MAAM,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,aAAa,CAAC,IAAI,CAAC;YAClE;QACF,KAAK;YACH,MAAM,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,aAAa,CAAC,IAAI,CAAC;YAClE;IACJ;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,MAAM,kBAAkB,OAAO,WAAW,UAAU;IAClD,MAAM,EACJ,YAAY,QAAQ,EACpB,WAAW,UAAU,EACrB,aAAa,EAAE,EACf,QAAQ,EACT,GAAG;IACJ,MAAM,kBAAkB,WAAW,MAAM,CAAC;IAC1C,MAAM,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS;IAC7E,IAAI,QAAQ,MAAM,SAAS,eAAe,CAAC;QACzC;QACA;QACA;IACF;IACA,IAAI,EACF,CAAC,EACD,CAAC,EACF,GAAG,2BAA2B,OAAO,WAAW;IACjD,IAAI,oBAAoB;IACxB,IAAI,iBAAiB,CAAC;IACtB,IAAI,aAAa;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;QAC/C,MAAM,EACJ,IAAI,EACJ,EAAE,EACH,GAAG,eAAe,CAAC,EAAE;QACtB,MAAM,EACJ,GAAG,KAAK,EACR,GAAG,KAAK,EACR,IAAI,EACJ,KAAK,EACN,GAAG,MAAM,GAAG;YACX;YACA;YACA,kBAAkB;YAClB,WAAW;YACX;YACA;YACA;YACA;YACA,UAAU;gBACR;gBACA;YACF;QACF;QACA,IAAI,SAAS,OAAO,QAAQ;QAC5B,IAAI,SAAS,OAAO,QAAQ;QAC5B,iBAAiB;YACf,GAAG,cAAc;YACjB,CAAC,KAAK,EAAE;gBACN,GAAG,cAAc,CAAC,KAAK;gBACvB,GAAG,IAAI;YACT;QACF;QACA,IAAI,SAAS,cAAc,IAAI;YAC7B;YACA,IAAI,OAAO,UAAU,UAAU;gBAC7B,IAAI,MAAM,SAAS,EAAE;oBACnB,oBAAoB,MAAM,SAAS;gBACrC;gBACA,IAAI,MAAM,KAAK,EAAE;oBACf,QAAQ,MAAM,KAAK,KAAK,OAAO,MAAM,SAAS,eAAe,CAAC;wBAC5D;wBACA;wBACA;oBACF,KAAK,MAAM,KAAK;gBAClB;gBACA,CAAC,EACC,CAAC,EACD,CAAC,EACF,GAAG,2BAA2B,OAAO,mBAAmB,IAAI;YAC/D;YACA,IAAI,CAAC;QACP;IACF;IACA,OAAO;QACL;QACA;QACA,WAAW;QACX;QACA;IACF;AACF;AAEA;;;;;;;CAOC,GACD,eAAe,eAAe,KAAK,EAAE,OAAO;IAC1C,IAAI;IACJ,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,CAAC,EACD,CAAC,EACD,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,WAAW,mBAAmB,EAC9B,eAAe,UAAU,EACzB,iBAAiB,UAAU,EAC3B,cAAc,KAAK,EACnB,UAAU,CAAC,EACZ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IACtB,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,MAAM,aAAa,mBAAmB,aAAa,cAAc;IACjE,MAAM,UAAU,QAAQ,CAAC,cAAc,aAAa,eAAe;IACnE,MAAM,qBAAqB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS,eAAe,CAAC;QACzE,SAAS,CAAC,CAAC,wBAAwB,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,KAAK,IAAI,SAAS,SAAS,CAAC,QAAQ,CAAC,KAAK,OAAO,wBAAwB,IAAI,IAAI,UAAU,QAAQ,cAAc,IAAK,MAAM,CAAC,SAAS,kBAAkB,IAAI,OAAO,KAAK,IAAI,SAAS,kBAAkB,CAAC,SAAS,QAAQ,CAAC;QACjS;QACA;QACA;IACF;IACA,MAAM,OAAO,mBAAmB,aAAa;QAC3C;QACA;QACA,OAAO,MAAM,QAAQ,CAAC,KAAK;QAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;IAC/B,IAAI,MAAM,SAAS;IACnB,MAAM,eAAe,MAAM,CAAC,SAAS,eAAe,IAAI,OAAO,KAAK,IAAI,SAAS,eAAe,CAAC,SAAS,QAAQ,CAAC;IACnH,MAAM,cAAc,AAAC,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,KAAK,IAAI,SAAS,SAAS,CAAC,aAAa,IAAK,AAAC,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC,aAAa,KAAM;QACvL,GAAG;QACH,GAAG;IACL,IAAI;QACF,GAAG;QACH,GAAG;IACL;IACA,MAAM,oBAAoB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,qDAAqD,GAAG,MAAM,SAAS,qDAAqD,CAAC;QAC/K;QACA;QACA;QACA;IACF,KAAK;IACL,OAAO;QACL,KAAK,CAAC,mBAAmB,GAAG,GAAG,kBAAkB,GAAG,GAAG,cAAc,GAAG,IAAI,YAAY,CAAC;QACzF,QAAQ,CAAC,kBAAkB,MAAM,GAAG,mBAAmB,MAAM,GAAG,cAAc,MAAM,IAAI,YAAY,CAAC;QACrG,MAAM,CAAC,mBAAmB,IAAI,GAAG,kBAAkB,IAAI,GAAG,cAAc,IAAI,IAAI,YAAY,CAAC;QAC7F,OAAO,CAAC,kBAAkB,KAAK,GAAG,mBAAmB,KAAK,GAAG,cAAc,KAAK,IAAI,YAAY,CAAC;IACnG;AACF;AAEA;;;;CAIC,GACD,MAAM,QAAQ,CAAA,UAAW,CAAC;QACxB,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACT,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,cAAc,EACf,GAAG;YACJ,4DAA4D;YAC5D,MAAM,EACJ,OAAO,EACP,UAAU,CAAC,EACZ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,UAAU,CAAC;YACjC,IAAI,WAAW,MAAM;gBACnB,OAAO,CAAC;YACV;YACA,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;YACvC,MAAM,SAAS;gBACb;gBACA;YACF;YACA,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;YAC9B,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;YAC7B,MAAM,kBAAkB,MAAM,SAAS,aAAa,CAAC;YACrD,MAAM,UAAU,SAAS;YACzB,MAAM,UAAU,UAAU,QAAQ;YAClC,MAAM,UAAU,UAAU,WAAW;YACrC,MAAM,aAAa,UAAU,iBAAiB;YAC9C,MAAM,UAAU,MAAM,SAAS,CAAC,OAAO,GAAG,MAAM,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,QAAQ,CAAC,OAAO;YACvG,MAAM,YAAY,MAAM,CAAC,KAAK,GAAG,MAAM,SAAS,CAAC,KAAK;YACtD,MAAM,oBAAoB,MAAM,CAAC,SAAS,eAAe,IAAI,OAAO,KAAK,IAAI,SAAS,eAAe,CAAC,QAAQ;YAC9G,IAAI,aAAa,oBAAoB,iBAAiB,CAAC,WAAW,GAAG;YAErE,0DAA0D;YAC1D,IAAI,CAAC,cAAc,CAAE,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,KAAK,IAAI,SAAS,SAAS,CAAC,kBAAkB,GAAI;gBACzG,aAAa,SAAS,QAAQ,CAAC,WAAW,IAAI,MAAM,QAAQ,CAAC,OAAO;YACtE;YACA,MAAM,oBAAoB,UAAU,IAAI,YAAY;YAEpD,0EAA0E;YAC1E,uDAAuD;YACvD,MAAM,yBAAyB,aAAa,IAAI,eAAe,CAAC,OAAO,GAAG,IAAI;YAC9E,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,aAAa,CAAC,QAAQ,EAAE;YAC/C,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,aAAa,CAAC,QAAQ,EAAE;YAE/C,0EAA0E;YAC1E,kDAAkD;YAClD,MAAM,QAAQ;YACd,MAAM,MAAM,aAAa,eAAe,CAAC,OAAO,GAAG;YACnD,MAAM,SAAS,aAAa,IAAI,eAAe,CAAC,OAAO,GAAG,IAAI;YAC9D,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,OAAO,QAAQ;YAEpC,yEAAyE;YACzE,yEAAyE;YACzE,yEAAyE;YACzE,iDAAiD;YACjD,MAAM,kBAAkB,CAAC,eAAe,KAAK,IAAI,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,cAAc,QAAQ,WAAW,UAAU,MAAM,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,QAAQ,aAAa,UAAU,IAAI,eAAe,CAAC,OAAO,GAAG,IAAI;YAClN,MAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAAS,MAAM;YAC3F,OAAO;gBACL,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG;gBACvB,MAAM;oBACJ,CAAC,KAAK,EAAE;oBACR,cAAc,SAAS,SAAS;oBAChC,GAAI,mBAAmB;wBACrB;oBACF,CAAC;gBACH;gBACA,OAAO;YACT;QACF;IACF,CAAC;AAED,SAAS,iBAAiB,SAAS,EAAE,aAAa,EAAE,iBAAiB;IACnE,MAAM,qCAAqC,YAAY;WAAI,kBAAkB,MAAM,CAAC,CAAA,YAAa,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,eAAe;WAAe,kBAAkB,MAAM,CAAC,CAAA,YAAa,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,eAAe;KAAW,GAAG,kBAAkB,MAAM,CAAC,CAAA,YAAa,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IACzR,OAAO,mCAAmC,MAAM,CAAC,CAAA;QAC/C,IAAI,WAAW;YACb,OAAO,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,eAAe,aAAa,CAAC,gBAAgB,CAAA,GAAA,6KAAA,CAAA,gCAA6B,AAAD,EAAE,eAAe,YAAY,KAAK;QACjI;QACA,OAAO;IACT;AACF;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,SAAU,OAAO;IACrC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB,wBAAwB;YACnD,MAAM,EACJ,KAAK,EACL,cAAc,EACd,SAAS,EACT,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,EACJ,YAAY,KAAK,EACjB,SAAS,EACT,oBAAoB,6KAAA,CAAA,aAAU,EAC9B,gBAAgB,IAAI,EACpB,GAAG,uBACJ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,eAAe,cAAc,aAAa,sBAAsB,6KAAA,CAAA,aAAU,GAAG,iBAAiB,aAAa,MAAM,eAAe,qBAAqB;YAC3J,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,eAAe,CAAC,CAAC,wBAAwB,eAAe,aAAa,KAAK,OAAO,KAAK,IAAI,sBAAsB,KAAK,KAAK;YAChI,MAAM,mBAAmB,YAAY,CAAC,aAAa;YACnD,IAAI,oBAAoB,MAAM;gBAC5B,OAAO,CAAC;YACV;YACA,MAAM,iBAAiB,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB,OAAO,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC;YAE5I,mDAAmD;YACnD,IAAI,cAAc,kBAAkB;gBAClC,OAAO;oBACL,OAAO;wBACL,WAAW,YAAY,CAAC,EAAE;oBAC5B;gBACF;YACF;YACA,MAAM,mBAAmB;gBAAC,QAAQ,CAAC,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;gBAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;aAAC;YACxH,MAAM,eAAe;mBAAK,CAAC,CAAC,yBAAyB,eAAe,aAAa,KAAK,OAAO,KAAK,IAAI,uBAAuB,SAAS,KAAK,EAAE;gBAAG;oBAC9I,WAAW;oBACX,WAAW;gBACb;aAAE;YACF,MAAM,gBAAgB,YAAY,CAAC,eAAe,EAAE;YAEpD,sCAAsC;YACtC,IAAI,eAAe;gBACjB,OAAO;oBACL,MAAM;wBACJ,OAAO,eAAe;wBACtB,WAAW;oBACb;oBACA,OAAO;wBACL,WAAW;oBACb;gBACF;YACF;YACA,MAAM,8BAA8B,aAAa,GAAG,CAAC,CAAA;gBACnD,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,EAAE,SAAS;gBAC1C,OAAO;oBAAC,EAAE,SAAS;oBAAE,aAAa,YAClC,oDAAoD;oBACpD,EAAE,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KACpD,2BAA2B;oBAC3B,EAAE,SAAS,CAAC,EAAE;oBAAE,EAAE,SAAS;iBAAC;YAC9B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC7B,MAAM,8BAA8B,4BAA4B,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GACvF,+DAA+D;gBAC/D,QAAQ;gBACR,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,CAAC,CAAC,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAC5C,MAAM,iBAAiB,CAAC,CAAC,wBAAwB,2BAA2B,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,EAAE,KAAK,2BAA2B,CAAC,EAAE,CAAC,EAAE;YAClK,IAAI,mBAAmB,WAAW;gBAChC,OAAO;oBACL,MAAM;wBACJ,OAAO,eAAe;wBACtB,WAAW;oBACb;oBACA,OAAO;wBACL,WAAW;oBACb;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA;;;;;CAKC,GACD,MAAM,OAAO,SAAU,OAAO;IAC5B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB;YAC3B,MAAM,EACJ,SAAS,EACT,cAAc,EACd,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,EACJ,UAAU,gBAAgB,IAAI,EAC9B,WAAW,iBAAiB,IAAI,EAChC,oBAAoB,2BAA2B,EAC/C,mBAAmB,SAAS,EAC5B,4BAA4B,MAAM,EAClC,gBAAgB,IAAI,EACpB,GAAG,uBACJ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YAEtB,sEAAsE;YACtE,0EAA0E;YAC1E,QAAQ;YACR,iFAAiF;YACjF,IAAI,CAAC,wBAAwB,eAAe,KAAK,KAAK,QAAQ,sBAAsB,eAAe,EAAE;gBACnG,OAAO,CAAC;YACV;YACA,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE;YACrB,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE;YACpC,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;YACtD,MAAM,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC;YACtF,MAAM,qBAAqB,+BAA+B,CAAC,mBAAmB,CAAC,gBAAgB;gBAAC,CAAA,GAAA,6KAAA,CAAA,uBAAoB,AAAD,EAAE;aAAkB,GAAG,CAAA,GAAA,6KAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB;YACjL,MAAM,+BAA+B,8BAA8B;YACnE,IAAI,CAAC,+BAA+B,8BAA8B;gBAChE,mBAAmB,IAAI,IAAI,CAAA,GAAA,6KAAA,CAAA,4BAAyB,AAAD,EAAE,kBAAkB,eAAe,2BAA2B;YACnH;YACA,MAAM,aAAa;gBAAC;mBAAqB;aAAmB;YAC5D,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,YAAY,EAAE;YACpB,IAAI,gBAAgB,CAAC,CAAC,uBAAuB,eAAe,IAAI,KAAK,OAAO,KAAK,IAAI,qBAAqB,SAAS,KAAK,EAAE;YAC1H,IAAI,eAAe;gBACjB,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC/B;YACA,IAAI,gBAAgB;gBAClB,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,OAAO;gBAClD,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD;YACA,gBAAgB;mBAAI;gBAAe;oBACjC;oBACA;gBACF;aAAE;YAEF,oCAAoC;YACpC,IAAI,CAAC,UAAU,KAAK,CAAC,CAAA,OAAQ,QAAQ,IAAI;gBACvC,IAAI,uBAAuB;gBAC3B,MAAM,YAAY,CAAC,CAAC,CAAC,wBAAwB,eAAe,IAAI,KAAK,OAAO,KAAK,IAAI,sBAAsB,KAAK,KAAK,CAAC,IAAI;gBAC1H,MAAM,gBAAgB,UAAU,CAAC,UAAU;gBAC3C,IAAI,eAAe;oBACjB,IAAI;oBACJ,MAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB;oBAClH,MAAM,6BAA6B,CAAC,CAAC,kBAAkB,aAAa,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,gBAAgB,SAAS,CAAC,EAAE,IAAI;oBAC5H,IAAI,CAAC,2BAA2B,4BAA4B;wBAC1D,+CAA+C;wBAC/C,OAAO;4BACL,MAAM;gCACJ,OAAO;gCACP,WAAW;4BACb;4BACA,OAAO;gCACL,WAAW;4BACb;wBACF;oBACF;gBACF;gBAEA,wEAAwE;gBACxE,yEAAyE;gBACzE,IAAI,iBAAiB,CAAC,wBAAwB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,SAAS;gBAEnM,sBAAsB;gBACtB,IAAI,CAAC,gBAAgB;oBACnB,OAAQ;wBACN,KAAK;4BACH;gCACE,IAAI;gCACJ,MAAM,YAAY,CAAC,yBAAyB,cAAc,MAAM,CAAC,CAAA;oCAC/D,IAAI,8BAA8B;wCAChC,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,EAAE,SAAS;wCAC/C,OAAO,oBAAoB,mBAC3B,uDAAuD;wCACvD,6CAA6C;wCAC7C,oBAAoB;oCACtB;oCACA,OAAO;gCACT,GAAG,GAAG,CAAC,CAAA,IAAK;wCAAC,EAAE,SAAS;wCAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA,WAAY,WAAW,GAAG,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,UAAU;qCAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,CAAC,EAAE;gCAClM,IAAI,WAAW;oCACb,iBAAiB;gCACnB;gCACA;4BACF;wBACF,KAAK;4BACH,iBAAiB;4BACjB;oBACJ;gBACF;gBACA,IAAI,cAAc,gBAAgB;oBAChC,OAAO;wBACL,OAAO;4BACL,WAAW;wBACb;oBACF;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,IAAI;IACpC,OAAO;QACL,KAAK,SAAS,GAAG,GAAG,KAAK,MAAM;QAC/B,OAAO,SAAS,KAAK,GAAG,KAAK,KAAK;QAClC,QAAQ,SAAS,MAAM,GAAG,KAAK,MAAM;QACrC,MAAM,SAAS,IAAI,GAAG,KAAK,KAAK;IAClC;AACF;AACA,SAAS,sBAAsB,QAAQ;IACrC,OAAO,6KAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,QAAQ,CAAC,KAAK,IAAI;AAC9C;AACA;;;;CAIC,GACD,MAAM,OAAO,SAAU,OAAO;IAC5B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,KAAK,EACN,GAAG;YACJ,MAAM,EACJ,WAAW,iBAAiB,EAC5B,GAAG,uBACJ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,OAAQ;gBACN,KAAK;oBACH;wBACE,MAAM,WAAW,MAAM,eAAe,OAAO;4BAC3C,GAAG,qBAAqB;4BACxB,gBAAgB;wBAClB;wBACA,MAAM,UAAU,eAAe,UAAU,MAAM,SAAS;wBACxD,OAAO;4BACL,MAAM;gCACJ,wBAAwB;gCACxB,iBAAiB,sBAAsB;4BACzC;wBACF;oBACF;gBACF,KAAK;oBACH;wBACE,MAAM,WAAW,MAAM,eAAe,OAAO;4BAC3C,GAAG,qBAAqB;4BACxB,aAAa;wBACf;wBACA,MAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;wBACvD,OAAO;4BACL,MAAM;gCACJ,gBAAgB;gCAChB,SAAS,sBAAsB;4BACjC;wBACF;oBACF;gBACF;oBACE;wBACE,OAAO,CAAC;oBACV;YACJ;QACF;IACF;AACF;AAEA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC/C,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;IAC9C,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAChD,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IACjD,OAAO;QACL,GAAG;QACH,GAAG;QACH,OAAO,OAAO;QACd,QAAQ,OAAO;IACjB;AACF;AACA,SAAS,eAAe,KAAK;IAC3B,MAAM,cAAc,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;IAC1D,MAAM,SAAS,EAAE;IACjB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,MAAM,OAAO,WAAW,CAAC,EAAE;QAC3B,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,GAAG;YAC1D,OAAO,IAAI,CAAC;gBAAC;aAAK;QACpB,OAAO;YACL,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC;QACjC;QACA,WAAW;IACb;IACA,OAAO,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;AAC7D;AACA;;;;CAIC,GACD,MAAM,SAAS,SAAU,OAAO;IAC9B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,gEAAgE;YAChE,qEAAqE;YACrE,2CAA2C;YAC3C,MAAM,EACJ,UAAU,CAAC,EACX,CAAC,EACD,CAAC,EACF,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,oBAAoB,MAAM,IAAI,CAAC,AAAC,MAAM,CAAC,SAAS,cAAc,IAAI,OAAO,KAAK,IAAI,SAAS,cAAc,CAAC,SAAS,SAAS,CAAC,KAAM,EAAE;YAC3I,MAAM,cAAc,eAAe;YACnC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;YAClD,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;YACvC,SAAS;gBACP,8CAA8C;gBAC9C,IAAI,YAAY,MAAM,KAAK,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;oBACpG,0DAA0D;oBAC1D,OAAO,YAAY,IAAI,CAAC,CAAA,OAAQ,IAAI,KAAK,IAAI,GAAG,cAAc,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,cAAc,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,cAAc,GAAG,IAAI,IAAI,KAAK,MAAM,GAAG,cAAc,MAAM,KAAK;gBAC/L;gBAEA,uCAAuC;gBACvC,IAAI,YAAY,MAAM,IAAI,GAAG;oBAC3B,IAAI,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,eAAe,KAAK;wBAClC,MAAM,YAAY,WAAW,CAAC,EAAE;wBAChC,MAAM,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;wBACpD,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,eAAe;wBACrC,MAAM,MAAM,UAAU,GAAG;wBACzB,MAAM,SAAS,SAAS,MAAM;wBAC9B,MAAM,OAAO,QAAQ,UAAU,IAAI,GAAG,SAAS,IAAI;wBACnD,MAAM,QAAQ,QAAQ,UAAU,KAAK,GAAG,SAAS,KAAK;wBACtD,MAAM,QAAQ,QAAQ;wBACtB,MAAM,SAAS,SAAS;wBACxB,OAAO;4BACL;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,GAAG;4BACH,GAAG;wBACL;oBACF;oBACA,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,eAAe;oBAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,KAAK,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBAC1D,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,KAAK,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;oBACxD,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,OAAQ,aAAa,KAAK,IAAI,KAAK,UAAU,KAAK,KAAK,KAAK;oBACpG,MAAM,MAAM,YAAY,CAAC,EAAE,CAAC,GAAG;oBAC/B,MAAM,SAAS,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,MAAM;oBAC3D,MAAM,OAAO;oBACb,MAAM,QAAQ;oBACd,MAAM,QAAQ,QAAQ;oBACtB,MAAM,SAAS,SAAS;oBACxB,OAAO;wBACL;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA,GAAG;wBACH,GAAG;oBACL;gBACF;gBACA,OAAO;YACT;YACA,MAAM,aAAa,MAAM,SAAS,eAAe,CAAC;gBAChD,WAAW;oBACT;gBACF;gBACA,UAAU,SAAS,QAAQ;gBAC3B;YACF;YACA,IAAI,MAAM,SAAS,CAAC,CAAC,KAAK,WAAW,SAAS,CAAC,CAAC,IAAI,MAAM,SAAS,CAAC,CAAC,KAAK,WAAW,SAAS,CAAC,CAAC,IAAI,MAAM,SAAS,CAAC,KAAK,KAAK,WAAW,SAAS,CAAC,KAAK,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,WAAW,SAAS,CAAC,MAAM,EAAE;gBAClN,OAAO;oBACL,OAAO;wBACL,OAAO;oBACT;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA,sEAAsE;AACtE,aAAa;AAEb,eAAe,qBAAqB,KAAK,EAAE,OAAO;IAChD,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC;IACtF,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE;IACrB,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IAC/B,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,eAAe;IAC9C,MAAM,gBAAgB;QAAC;QAAQ;KAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI;IAC5D,MAAM,iBAAiB,OAAO,aAAa,CAAC,IAAI;IAChD,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IAEnC,wCAAwC;IACxC,IAAI,EACF,QAAQ,EACR,SAAS,EACT,aAAa,EACd,GAAG,OAAO,aAAa,WAAW;QACjC,UAAU;QACV,WAAW;QACX,eAAe;IACjB,IAAI;QACF,UAAU,SAAS,QAAQ,IAAI;QAC/B,WAAW,SAAS,SAAS,IAAI;QACjC,eAAe,SAAS,aAAa;IACvC;IACA,IAAI,aAAa,OAAO,kBAAkB,UAAU;QAClD,YAAY,cAAc,QAAQ,gBAAgB,CAAC,IAAI;IACzD;IACA,OAAO,aAAa;QAClB,GAAG,YAAY;QACf,GAAG,WAAW;IAChB,IAAI;QACF,GAAG,WAAW;QACd,GAAG,YAAY;IACjB;AACF;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,SAAU,OAAO;IAC9B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB;YAC3B,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACT,cAAc,EACf,GAAG;YACJ,MAAM,aAAa,MAAM,qBAAqB,OAAO;YAErD,wEAAwE;YACxE,4DAA4D;YAC5D,IAAI,cAAc,CAAC,CAAC,wBAAwB,eAAe,MAAM,KAAK,OAAO,KAAK,IAAI,sBAAsB,SAAS,KAAK,CAAC,wBAAwB,eAAe,KAAK,KAAK,QAAQ,sBAAsB,eAAe,EAAE;gBACzN,OAAO,CAAC;YACV;YACA,OAAO;gBACL,GAAG,IAAI,WAAW,CAAC;gBACnB,GAAG,IAAI,WAAW,CAAC;gBACnB,MAAM;oBACJ,GAAG,UAAU;oBACb;gBACF;YACF;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,MAAM,QAAQ,SAAU,OAAO;IAC7B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACV,GAAG;YACJ,MAAM,EACJ,UAAU,gBAAgB,IAAI,EAC9B,WAAW,iBAAiB,KAAK,EACjC,UAAU;gBACR,IAAI,CAAA;oBACF,IAAI,EACF,CAAC,EACD,CAAC,EACF,GAAG;oBACJ,OAAO;wBACL;wBACA;oBACF;gBACF;YACF,CAAC,EACD,GAAG,uBACJ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,SAAS;gBACb;gBACA;YACF;YACA,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE;YACtC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE;YACjC,IAAI,gBAAgB,MAAM,CAAC,SAAS;YACpC,IAAI,iBAAiB,MAAM,CAAC,UAAU;YACtC,IAAI,eAAe;gBACjB,MAAM,UAAU,aAAa,MAAM,QAAQ;gBAC3C,MAAM,UAAU,aAAa,MAAM,WAAW;gBAC9C,MAAM,MAAM,gBAAgB,QAAQ,CAAC,QAAQ;gBAC7C,MAAM,MAAM,gBAAgB,QAAQ,CAAC,QAAQ;gBAC7C,gBAAgB,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,eAAe;YAC5C;YACA,IAAI,gBAAgB;gBAClB,MAAM,UAAU,cAAc,MAAM,QAAQ;gBAC5C,MAAM,UAAU,cAAc,MAAM,WAAW;gBAC/C,MAAM,MAAM,iBAAiB,QAAQ,CAAC,QAAQ;gBAC9C,MAAM,MAAM,iBAAiB,QAAQ,CAAC,QAAQ;gBAC9C,iBAAiB,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,gBAAgB;YAC9C;YACA,MAAM,gBAAgB,QAAQ,EAAE,CAAC;gBAC/B,GAAG,KAAK;gBACR,CAAC,SAAS,EAAE;gBACZ,CAAC,UAAU,EAAE;YACf;YACA,OAAO;gBACL,GAAG,aAAa;gBAChB,MAAM;oBACJ,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;oBACrB,SAAS;wBACP,CAAC,SAAS,EAAE;wBACZ,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACF;IACF;AACF;AACA;;CAEC,GACD,MAAM,aAAa,SAAU,OAAO;IAClC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL;QACA,IAAG,KAAK;YACN,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACT,KAAK,EACL,cAAc,EACf,GAAG;YACJ,MAAM,EACJ,SAAS,CAAC,EACV,UAAU,gBAAgB,IAAI,EAC9B,WAAW,iBAAiB,IAAI,EACjC,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,SAAS;gBACb;gBACA;YACF;YACA,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE;YAC9B,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE;YACjC,IAAI,gBAAgB,MAAM,CAAC,SAAS;YACpC,IAAI,iBAAiB,MAAM,CAAC,UAAU;YACtC,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACnC,MAAM,iBAAiB,OAAO,cAAc,WAAW;gBACrD,UAAU;gBACV,WAAW;YACb,IAAI;gBACF,UAAU;gBACV,WAAW;gBACX,GAAG,SAAS;YACd;YACA,IAAI,eAAe;gBACjB,MAAM,MAAM,aAAa,MAAM,WAAW;gBAC1C,MAAM,WAAW,MAAM,SAAS,CAAC,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,GAAG,eAAe,QAAQ;gBAC1F,MAAM,WAAW,MAAM,SAAS,CAAC,SAAS,GAAG,MAAM,SAAS,CAAC,IAAI,GAAG,eAAe,QAAQ;gBAC3F,IAAI,gBAAgB,UAAU;oBAC5B,gBAAgB;gBAClB,OAAO,IAAI,gBAAgB,UAAU;oBACnC,gBAAgB;gBAClB;YACF;YACA,IAAI,gBAAgB;gBAClB,IAAI,uBAAuB;gBAC3B,MAAM,MAAM,aAAa,MAAM,UAAU;gBACzC,MAAM,eAAe;oBAAC;oBAAO;iBAAO,CAAC,QAAQ,CAAC,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE;gBACtD,MAAM,WAAW,MAAM,SAAS,CAAC,UAAU,GAAG,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC,wBAAwB,eAAe,MAAM,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,eAAe,SAAS;gBAClP,MAAM,WAAW,MAAM,SAAS,CAAC,UAAU,GAAG,MAAM,SAAS,CAAC,IAAI,GAAG,CAAC,eAAe,IAAI,CAAC,CAAC,yBAAyB,eAAe,MAAM,KAAK,OAAO,KAAK,IAAI,sBAAsB,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,eAAe,eAAe,SAAS,GAAG,CAAC;gBACrP,IAAI,iBAAiB,UAAU;oBAC7B,iBAAiB;gBACnB,OAAO,IAAI,iBAAiB,UAAU;oBACpC,iBAAiB;gBACnB;YACF;YACA,OAAO;gBACL,CAAC,SAAS,EAAE;gBACZ,CAAC,UAAU,EAAE;YACf;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,MAAM,OAAO,SAAU,OAAO;IAC5B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB;YAC3B,MAAM,EACJ,SAAS,EACT,KAAK,EACL,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,EACJ,QAAQ,KAAO,CAAC,EAChB,GAAG,uBACJ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE;YACrB,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;YAC/B,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,eAAe;YAC3C,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,IAAI;YACJ,IAAI;YACJ,IAAI,SAAS,SAAS,SAAS,UAAU;gBACvC,aAAa;gBACb,YAAY,cAAc,CAAC,AAAC,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAK,UAAU,KAAK,IAAI,SAAS;YACzI,OAAO;gBACL,YAAY;gBACZ,aAAa,cAAc,QAAQ,QAAQ;YAC7C;YACA,MAAM,wBAAwB,SAAS,SAAS,GAAG,GAAG,SAAS,MAAM;YACrE,MAAM,uBAAuB,QAAQ,SAAS,IAAI,GAAG,SAAS,KAAK;YACnE,MAAM,0BAA0B,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,QAAQ,CAAC,WAAW,EAAE;YACnE,MAAM,yBAAyB,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,QAAQ,CAAC,UAAU,EAAE;YAChE,MAAM,UAAU,CAAC,MAAM,cAAc,CAAC,KAAK;YAC3C,IAAI,kBAAkB;YACtB,IAAI,iBAAiB;YACrB,IAAI,CAAC,wBAAwB,MAAM,cAAc,CAAC,KAAK,KAAK,QAAQ,sBAAsB,OAAO,CAAC,CAAC,EAAE;gBACnG,iBAAiB;YACnB;YACA,IAAI,CAAC,yBAAyB,MAAM,cAAc,CAAC,KAAK,KAAK,QAAQ,uBAAuB,OAAO,CAAC,CAAC,EAAE;gBACrG,kBAAkB;YACpB;YACA,IAAI,WAAW,CAAC,WAAW;gBACzB,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,IAAI,EAAE;gBAChC,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,KAAK,EAAE;gBACjC,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,GAAG,EAAE;gBAC/B,MAAM,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,MAAM,EAAE;gBAClC,IAAI,SAAS;oBACX,iBAAiB,QAAQ,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,IAAI,EAAE,SAAS,KAAK,CAAC;gBAC3G,OAAO;oBACL,kBAAkB,SAAS,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,SAAS,GAAG,EAAE,SAAS,MAAM,CAAC;gBAC7G;YACF;YACA,MAAM,MAAM;gBACV,GAAG,KAAK;gBACR;gBACA;YACF;YACA,MAAM,iBAAiB,MAAM,SAAS,aAAa,CAAC,SAAS,QAAQ;YACrE,IAAI,UAAU,eAAe,KAAK,IAAI,WAAW,eAAe,MAAM,EAAE;gBACtE,OAAO;oBACL,OAAO;wBACL,OAAO;oBACT;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAEA;;;;;AAGA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7B,yEAAyE;IACzE,yEAAyE;IACzE,IAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;IACrC,IAAI,SAAS,WAAW,IAAI,MAAM,KAAK;IACvC,MAAM,YAAY,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;IAChC,MAAM,cAAc,YAAY,QAAQ,WAAW,GAAG;IACtD,MAAM,eAAe,YAAY,QAAQ,YAAY,GAAG;IACxD,MAAM,iBAAiB,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,WAAW,eAAe,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,YAAY;IACzE,IAAI,gBAAgB;QAClB,QAAQ;QACR,SAAS;IACX;IACA,OAAO;QACL;QACA;QACA,GAAG;IACL;AACF;AAEA,SAAS,cAAc,OAAO;IAC5B,OAAO,CAAC,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,QAAQ,cAAc,GAAG;AACxD;AAEA,SAAS,SAAS,OAAO;IACvB,MAAM,aAAa,cAAc;IACjC,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,aAAa;QAC9B,OAAO,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACtB;IACA,MAAM,OAAO,WAAW,qBAAqB;IAC7C,MAAM,EACJ,KAAK,EACL,MAAM,EACN,CAAC,EACF,GAAG,iBAAiB;IACrB,IAAI,IAAI,CAAC,IAAI,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI;IAC/C,IAAI,IAAI,CAAC,IAAI,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI;IAEjD,mDAAmD;IAEnD,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ,CAAC,IAAI;QAC7B,IAAI;IACN;IACA,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ,CAAC,IAAI;QAC7B,IAAI;IACN;IACA,OAAO;QACL;QACA;IACF;AACF;AAEA,MAAM,YAAY,WAAW,GAAE,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;AAC5C,SAAS,iBAAiB,OAAO;IAC/B,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE;IACtB,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,WAAQ,AAAD,OAAO,CAAC,IAAI,cAAc,EAAE;QACtC,OAAO;IACT;IACA,OAAO;QACL,GAAG,IAAI,cAAc,CAAC,UAAU;QAChC,GAAG,IAAI,cAAc,CAAC,SAAS;IACjC;AACF;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB;IACpE,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IACA,IAAI,CAAC,wBAAwB,WAAW,yBAAyB,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QACnF,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,YAAY;IACjF,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,MAAM,aAAa,QAAQ,qBAAqB;IAChD,MAAM,aAAa,cAAc;IACjC,IAAI,QAAQ,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACzB,IAAI,cAAc;QAChB,IAAI,cAAc;YAChB,IAAI,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;gBAC3B,QAAQ,SAAS;YACnB;QACF,OAAO;YACL,QAAQ,SAAS;QACnB;IACF;IACA,MAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,gBAAgB,iBAAiB,cAAc,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACtI,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC;IACrD,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC;IACpD,IAAI,QAAQ,WAAW,KAAK,GAAG,MAAM,CAAC;IACtC,IAAI,SAAS,WAAW,MAAM,GAAG,MAAM,CAAC;IACxC,IAAI,YAAY;QACd,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE;QACtB,MAAM,YAAY,gBAAgB,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QACtF,IAAI,aAAa;QACjB,IAAI,gBAAgB,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE;QACpC,MAAO,iBAAiB,gBAAgB,cAAc,WAAY;YAChE,MAAM,cAAc,SAAS;YAC7B,MAAM,aAAa,cAAc,qBAAqB;YACtD,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE;YAC7B,MAAM,OAAO,WAAW,IAAI,GAAG,CAAC,cAAc,UAAU,GAAG,WAAW,IAAI,WAAW,CAAC,IAAI,YAAY,CAAC;YACvG,MAAM,MAAM,WAAW,GAAG,GAAG,CAAC,cAAc,SAAS,GAAG,WAAW,IAAI,UAAU,CAAC,IAAI,YAAY,CAAC;YACnG,KAAK,YAAY,CAAC;YAClB,KAAK,YAAY,CAAC;YAClB,SAAS,YAAY,CAAC;YACtB,UAAU,YAAY,CAAC;YACvB,KAAK;YACL,KAAK;YACL,aAAa,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE;YACvB,gBAAgB,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE;QAClC;IACF;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB;QACA;QACA;QACA;IACF;AACF;AAEA,yEAAyE;AACzE,qBAAqB;AACrB,SAAS,oBAAoB,OAAO,EAAE,IAAI;IACxC,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,UAAU;IACpD,IAAI,CAAC,MAAM;QACT,OAAO,sBAAsB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI,GAAG;IACnE;IACA,OAAO,KAAK,IAAI,GAAG;AACrB;AAEA,SAAS,cAAc,eAAe,EAAE,MAAM,EAAE,gBAAgB;IAC9D,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;IACrB;IACA,MAAM,WAAW,gBAAgB,qBAAqB;IACtD,MAAM,IAAI,SAAS,IAAI,GAAG,OAAO,UAAU,GAAG,CAAC,mBAAmB,IAClE,wBAAwB;IACxB,oBAAoB,iBAAiB,SAAS;IAC9C,MAAM,IAAI,SAAS,GAAG,GAAG,OAAO,SAAS;IACzC,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,sDAAsD,IAAI;IACjE,IAAI,EACF,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,QAAQ,EACT,GAAG;IACJ,MAAM,UAAU,aAAa;IAC7B,MAAM,kBAAkB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC3C,MAAM,WAAW,WAAW,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE,SAAS,QAAQ,IAAI;IAC5D,IAAI,iBAAiB,mBAAmB,YAAY,SAAS;QAC3D,OAAO;IACT;IACA,IAAI,SAAS;QACX,YAAY;QACZ,WAAW;IACb;IACA,IAAI,QAAQ,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACzB,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IAC7B,MAAM,0BAA0B,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;IAC9C,IAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;QACnE,IAAI,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,UAAU,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB;YAC9E,SAAS,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;YAC/B,MAAM,aAAa,sBAAsB;YACzC,QAAQ,SAAS;YACjB,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,UAAU;YAClD,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,SAAS;QACnD;IACF;IACA,MAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,QAAQ,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACzI,OAAO;QACL,OAAO,KAAK,KAAK,GAAG,MAAM,CAAC;QAC3B,QAAQ,KAAK,MAAM,GAAG,MAAM,CAAC;QAC7B,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,UAAU,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;QAC5E,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,SAAS,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;IAC7E;AACF;AAEA,SAAS,eAAe,OAAO;IAC7B,OAAO,MAAM,IAAI,CAAC,QAAQ,cAAc;AAC1C;AAEA,+EAA+E;AAC/E,uEAAuE;AACvE,SAAS,gBAAgB,OAAO;IAC9B,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE;IAChC,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,MAAM,OAAO,QAAQ,aAAa,CAAC,IAAI;IACvC,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW;IACxF,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY;IAC7F,IAAI,IAAI,CAAC,OAAO,UAAU,GAAG,oBAAoB;IACjD,MAAM,IAAI,CAAC,OAAO,SAAS;IAC3B,IAAI,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS,KAAK,OAAO;QAC9C,KAAK,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,IAAI;IACjD;IACA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE;IACtB,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE;IAChC,MAAM,iBAAiB,IAAI,cAAc;IACzC,IAAI,QAAQ,KAAK,WAAW;IAC5B,IAAI,SAAS,KAAK,YAAY;IAC9B,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,gBAAgB;QAClB,QAAQ,eAAe,KAAK;QAC5B,SAAS,eAAe,MAAM;QAC9B,MAAM,sBAAsB,CAAA,GAAA,oLAAA,CAAA,WAAQ,AAAD;QACnC,IAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;YACvE,IAAI,eAAe,UAAU;YAC7B,IAAI,eAAe,SAAS;QAC9B;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,oEAAoE;AACpE,SAAS,2BAA2B,OAAO,EAAE,QAAQ;IACnD,MAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa;IACrE,MAAM,MAAM,WAAW,GAAG,GAAG,QAAQ,SAAS;IAC9C,MAAM,OAAO,WAAW,IAAI,GAAG,QAAQ,UAAU;IACjD,MAAM,QAAQ,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,SAAS,WAAW,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACxE,MAAM,QAAQ,QAAQ,WAAW,GAAG,MAAM,CAAC;IAC3C,MAAM,SAAS,QAAQ,YAAY,GAAG,MAAM,CAAC;IAC7C,MAAM,IAAI,OAAO,MAAM,CAAC;IACxB,MAAM,IAAI,MAAM,MAAM,CAAC;IACvB,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AACA,SAAS,kCAAkC,OAAO,EAAE,gBAAgB,EAAE,QAAQ;IAC5E,IAAI;IACJ,IAAI,qBAAqB,YAAY;QACnC,OAAO,gBAAgB,SAAS;IAClC,OAAO,IAAI,qBAAqB,YAAY;QAC1C,OAAO,gBAAgB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5C,OAAO,IAAI,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;QACtC,OAAO,2BAA2B,kBAAkB;IACtD,OAAO;QACL,MAAM,gBAAgB,iBAAiB;QACvC,OAAO;YACL,GAAG,iBAAiB,CAAC,GAAG,cAAc,CAAC;YACvC,GAAG,iBAAiB,CAAC,GAAG,cAAc,CAAC;YACvC,OAAO,iBAAiB,KAAK;YAC7B,QAAQ,iBAAiB,MAAM;QACjC;IACF;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;AAC1B;AACA,SAAS,yBAAyB,OAAO,EAAE,QAAQ;IACjD,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;IACjC,IAAI,eAAe,YAAY,CAAC,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAA,GAAA,oLAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa;QAC1F,OAAO;IACT;IACA,OAAO,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,QAAQ,KAAK,WAAW,yBAAyB,YAAY;AACnG;AAEA,4EAA4E;AAC5E,2EAA2E;AAC3E,oCAAoC;AACpC,SAAS,4BAA4B,OAAO,EAAE,KAAK;IACjD,MAAM,eAAe,MAAM,GAAG,CAAC;IAC/B,IAAI,cAAc;QAChB,OAAO;IACT;IACA,IAAI,SAAS,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,EAAE,EAAE,OAAO,MAAM,CAAC,CAAA,KAAM,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;IACxG,IAAI,sCAAsC;IAC1C,MAAM,iBAAiB,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK;IAC9D,IAAI,cAAc,iBAAiB,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;IAE5D,qGAAqG;IACrG,MAAO,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,CAAC,CAAA,GAAA,oLAAA,CAAA,wBAAqB,AAAD,EAAE,aAAc;QACpE,MAAM,gBAAgB,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE;QACvC,MAAM,0BAA0B,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE;QAClD,IAAI,CAAC,2BAA2B,cAAc,QAAQ,KAAK,SAAS;YAClE,sCAAsC;QACxC;QACA,MAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,QAAQ,KAAK,YAAY,CAAC,CAAC,uCAAuC;YAAC;YAAY;SAAQ,CAAC,QAAQ,CAAC,oCAAoC,QAAQ,KAAK,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,CAAC,2BAA2B,yBAAyB,SAAS;QAC9Y,IAAI,uBAAuB;YACzB,8BAA8B;YAC9B,SAAS,OAAO,MAAM,CAAC,CAAA,WAAY,aAAa;QAClD,OAAO;YACL,mDAAmD;YACnD,sCAAsC;QACxC;QACA,cAAc,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;IAC9B;IACA,MAAM,GAAG,CAAC,SAAS;IACnB,OAAO;AACT;AAEA,4EAA4E;AAC5E,sBAAsB;AACtB,SAAS,gBAAgB,IAAI;IAC3B,IAAI,EACF,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,QAAQ,EACT,GAAG;IACJ,MAAM,2BAA2B,aAAa,sBAAsB,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE,WAAW,EAAE,GAAG,4BAA4B,SAAS,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC;IACzJ,MAAM,oBAAoB;WAAI;QAA0B;KAAa;IACrE,MAAM,wBAAwB,iBAAiB,CAAC,EAAE;IAClD,MAAM,eAAe,kBAAkB,MAAM,CAAC,CAAC,SAAS;QACtD,MAAM,OAAO,kCAAkC,SAAS,kBAAkB;QAC1E,QAAQ,GAAG,GAAG,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG;QACvC,QAAQ,KAAK,GAAG,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,EAAE,QAAQ,KAAK;QAC7C,QAAQ,MAAM,GAAG,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,MAAM,EAAE,QAAQ,MAAM;QAChD,QAAQ,IAAI,GAAG,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,KAAK,IAAI,EAAE,QAAQ,IAAI;QAC1C,OAAO;IACT,GAAG,kCAAkC,SAAS,uBAAuB;IACrE,OAAO;QACL,OAAO,aAAa,KAAK,GAAG,aAAa,IAAI;QAC7C,QAAQ,aAAa,MAAM,GAAG,aAAa,GAAG;QAC9C,GAAG,aAAa,IAAI;QACpB,GAAG,aAAa,GAAG;IACrB;AACF;AAEA,SAAS,cAAc,OAAO;IAC5B,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG,iBAAiB;IACrB,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,8BAA8B,OAAO,EAAE,YAAY,EAAE,QAAQ;IACpE,MAAM,0BAA0B,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;IAC9C,MAAM,kBAAkB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC3C,MAAM,UAAU,aAAa;IAC7B,MAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS;IAC3D,IAAI,SAAS;QACX,YAAY;QACZ,WAAW;IACb;IACA,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IAE7B,sEAAsE;IACtE,uEAAuE;IACvE,SAAS;QACP,QAAQ,CAAC,GAAG,oBAAoB;IAClC;IACA,IAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;QACnE,IAAI,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,UAAU,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB;YAC9E,SAAS,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QACA,IAAI,yBAAyB;YAC3B,MAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS;YACtE,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,UAAU;YAClD,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,SAAS;QACnD,OAAO,IAAI,iBAAiB;YAC1B;QACF;IACF;IACA,IAAI,WAAW,CAAC,2BAA2B,iBAAiB;QAC1D;IACF;IACA,MAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,UAAU,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IACnI,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,UAAU,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;IAClE,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO,SAAS,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;IAChE,OAAO;QACL;QACA;QACA,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;IACrB;AACF;AAEA,SAAS,mBAAmB,OAAO;IACjC,OAAO,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK;AAChD;AAEA,SAAS,oBAAoB,OAAO,EAAE,QAAQ;IAC5C,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK,SAAS;QAC7E,OAAO;IACT;IACA,IAAI,UAAU;QACZ,OAAO,SAAS;IAClB;IACA,IAAI,kBAAkB,QAAQ,YAAY;IAE1C,6EAA6E;IAC7E,6EAA6E;IAC7E,4EAA4E;IAC5E,cAAc;IACd,IAAI,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACnD,kBAAkB,gBAAgB,aAAa,CAAC,IAAI;IACtD;IACA,OAAO;AACT;AAEA,yEAAyE;AACzE,kDAAkD;AAClD,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE;IACtB,IAAI,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QACvB,OAAO;IACT;IACA,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;QAC3B,IAAI,kBAAkB,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;QACpC,MAAO,mBAAmB,CAAC,CAAA,GAAA,oLAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAkB;YACjE,IAAI,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,CAAC,mBAAmB,kBAAkB;gBACtE,OAAO;YACT;YACA,kBAAkB,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;QAClC;QACA,OAAO;IACT;IACA,IAAI,eAAe,oBAAoB,SAAS;IAChD,MAAO,gBAAgB,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,mBAAmB,cAAe;QACvF,eAAe,oBAAoB,cAAc;IACnD;IACA,IAAI,gBAAgB,CAAA,GAAA,oLAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,mBAAmB,iBAAiB,CAAC,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;QAC/H,OAAO;IACT;IACA,OAAO,gBAAgB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY;AACxD;AAEA,MAAM,kBAAkB,eAAgB,IAAI;IAC1C,MAAM,oBAAoB,IAAI,CAAC,eAAe,IAAI;IAClD,MAAM,kBAAkB,IAAI,CAAC,aAAa;IAC1C,MAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;IAC9D,OAAO;QACL,WAAW,8BAA8B,KAAK,SAAS,EAAE,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;QAC9G,UAAU;YACR,GAAG;YACH,GAAG;YACH,OAAO,mBAAmB,KAAK;YAC/B,QAAQ,mBAAmB,MAAM;QACnC;IACF;AACF;AAEA,SAAS,MAAM,OAAO;IACpB,OAAO,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,SAAS,KAAK;AACjD;AAEA,MAAM,WAAW;IACf;IACA,oBAAA,oLAAA,CAAA,qBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA,WAAA,oLAAA,CAAA,YAAS;IACT;AACF;AAEA,SAAS,cAAc,CAAC,EAAE,CAAC;IACzB,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM;AACnF;AAEA,yCAAyC;AACzC,SAAS,YAAY,OAAO,EAAE,MAAM;IAClC,IAAI,KAAK;IACT,IAAI;IACJ,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE;IAChC,SAAS;QACP,IAAI;QACJ,aAAa;QACb,CAAC,MAAM,EAAE,KAAK,QAAQ,IAAI,UAAU;QACpC,KAAK;IACP;IACA,SAAS,QAAQ,IAAI,EAAE,SAAS;QAC9B,IAAI,SAAS,KAAK,GAAG;YACnB,OAAO;QACT;QACA,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY;QACd;QACA;QACA,MAAM,2BAA2B,QAAQ,qBAAqB;QAC9D,MAAM,EACJ,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACP,GAAG;QACJ,IAAI,CAAC,MAAM;YACT;QACF;QACA,IAAI,CAAC,SAAS,CAAC,QAAQ;YACrB;QACF;QACA,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE;QACvB,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,WAAW,GAAG,CAAC,OAAO,KAAK;QACzD,MAAM,cAAc,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE,KAAK,YAAY,GAAG,CAAC,MAAM,MAAM;QAC3D,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,QAAK,AAAD,EAAE;QACxB,MAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;QACjG,MAAM,UAAU;YACd;YACA,WAAW,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,MAAG,AAAD,EAAE,GAAG,eAAe;QAC1C;QACA,IAAI,gBAAgB;QACpB,SAAS,cAAc,OAAO;YAC5B,MAAM,QAAQ,OAAO,CAAC,EAAE,CAAC,iBAAiB;YAC1C,IAAI,UAAU,WAAW;gBACvB,IAAI,CAAC,eAAe;oBAClB,OAAO;gBACT;gBACA,IAAI,CAAC,OAAO;oBACV,oEAAoE;oBACpE,0CAA0C;oBAC1C,YAAY,WAAW;wBACrB,QAAQ,OAAO;oBACjB,GAAG;gBACL,OAAO;oBACL,QAAQ,OAAO;gBACjB;YACF;YACA,IAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,qBAAqB,KAAK;gBAC5F,iEAAiE;gBACjE,uEAAuE;gBACvE,wEAAwE;gBACxE,oEAAoE;gBACpE,oEAAoE;gBACpE,uEAAuE;gBACvE,uDAAuD;gBACvD;YACF;YACA,gBAAgB;QAClB;QAEA,0EAA0E;QAC1E,SAAS;QACT,IAAI;YACF,KAAK,IAAI,qBAAqB,eAAe;gBAC3C,GAAG,OAAO;gBACV,mBAAmB;gBACnB,MAAM,KAAK,aAAa;YAC1B;QACF,EAAE,OAAO,IAAI;YACX,KAAK,IAAI,qBAAqB,eAAe;QAC/C;QACA,GAAG,OAAO,CAAC;IACb;IACA,QAAQ;IACR,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IACtD,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,gBAAgB,OAAO,mBAAmB,UAAU,EACpD,cAAc,OAAO,yBAAyB,UAAU,EACxD,iBAAiB,KAAK,EACvB,GAAG;IACJ,MAAM,cAAc,cAAc;IAClC,MAAM,YAAY,kBAAkB,iBAAiB;WAAK,cAAc,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,EAAE;WAAM,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE;KAAU,GAAG,EAAE;IACxJ,UAAU,OAAO,CAAC,CAAA;QAChB,kBAAkB,SAAS,gBAAgB,CAAC,UAAU,QAAQ;YAC5D,SAAS;QACX;QACA,kBAAkB,SAAS,gBAAgB,CAAC,UAAU;IACxD;IACA,MAAM,YAAY,eAAe,cAAc,YAAY,aAAa,UAAU;IAClF,IAAI,iBAAiB,CAAC;IACtB,IAAI,iBAAiB;IACrB,IAAI,eAAe;QACjB,iBAAiB,IAAI,eAAe,CAAA;YAClC,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,cAAc,WAAW,MAAM,KAAK,eAAe,gBAAgB;gBACrE,yDAAyD;gBACzD,yDAAyD;gBACzD,eAAe,SAAS,CAAC;gBACzB,qBAAqB;gBACrB,iBAAiB,sBAAsB;oBACrC,IAAI;oBACJ,CAAC,kBAAkB,cAAc,KAAK,QAAQ,gBAAgB,OAAO,CAAC;gBACxE;YACF;YACA;QACF;QACA,IAAI,eAAe,CAAC,gBAAgB;YAClC,eAAe,OAAO,CAAC;QACzB;QACA,eAAe,OAAO,CAAC;IACzB;IACA,IAAI;IACJ,IAAI,cAAc,iBAAiB,sBAAsB,aAAa;IACtE,IAAI,gBAAgB;QAClB;IACF;IACA,SAAS;QACP,MAAM,cAAc,sBAAsB;QAC1C,IAAI,eAAe,CAAC,cAAc,aAAa,cAAc;YAC3D;QACF;QACA,cAAc;QACd,UAAU,sBAAsB;IAClC;IACA;IACA,OAAO;QACL,IAAI;QACJ,UAAU,OAAO,CAAC,CAAA;YAChB,kBAAkB,SAAS,mBAAmB,CAAC,UAAU;YACzD,kBAAkB,SAAS,mBAAmB,CAAC,UAAU;QAC3D;QACA,aAAa,QAAQ;QACrB,CAAC,mBAAmB,cAAc,KAAK,QAAQ,iBAAiB,UAAU;QAC1E,iBAAiB;QACjB,IAAI,gBAAgB;YAClB,qBAAqB;QACvB;IACF;AACF;AAEA;;;;;;;CAOC,GACD,MAAM,iBAAiB,2LAAA,CAAA,iBAAgB;AAEvC;;;;;;CAMC,GACD,MAAM,SAAS,2LAAA,CAAA,SAAQ;AAEvB;;;;;CAKC,GACD,MAAM,gBAAgB,2LAAA,CAAA,gBAAe;AAErC;;;;CAIC,GACD,MAAM,QAAQ,2LAAA,CAAA,QAAO;AAErB;;;;;CAKC,GACD,MAAM,OAAO,2LAAA,CAAA,OAAM;AAEnB;;;;;CAKC,GACD,MAAM,OAAO,2LAAA,CAAA,OAAM;AAEnB;;;;CAIC,GACD,MAAM,OAAO,2LAAA,CAAA,OAAM;AAEnB;;;;CAIC,GACD,MAAM,QAAQ,2LAAA,CAAA,QAAO;AAErB;;;;CAIC,GACD,MAAM,SAAS,2LAAA,CAAA,SAAQ;AAEvB;;CAEC,GACD,MAAM,aAAa,2LAAA,CAAA,aAAY;AAE/B;;;CAGC,GACD,MAAM,kBAAkB,CAAC,WAAW,UAAU;IAC5C,2EAA2E;IAC3E,wEAAwE;IACxE,6EAA6E;IAC7E,MAAM,QAAQ,IAAI;IAClB,MAAM,gBAAgB;QACpB;QACA,GAAG,OAAO;IACZ;IACA,MAAM,oBAAoB;QACxB,GAAG,cAAc,QAAQ;QACzB,IAAI;IACN;IACA,OAAO,CAAA,GAAA,2LAAA,CAAA,kBAAiB,AAAD,EAAE,WAAW,UAAU;QAC5C,GAAG,aAAa;QAChB,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/%40floating-ui/react-dom/dist/floating-ui.react-dom.mjs"], "sourcesContent": ["import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAEA;AAEA;;;;;;AAEA,IAAI,QAAQ,OAAO,aAAa,cAAc,qMAAA,CAAA,kBAAe,GAAG,qMAAA,CAAA,YAAS;AAEzE,gFAAgF;AAChF,YAAY;AACZ,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IACA,IAAI,OAAO,MAAM,OAAO,GAAG;QACzB,OAAO;IACT;IACA,IAAI,OAAO,MAAM,cAAc,EAAE,QAAQ,OAAO,EAAE,QAAQ,IAAI;QAC5D,OAAO;IACT;IACA,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK,KAAK,OAAO,MAAM,UAAU;QACnC,IAAI,MAAM,OAAO,CAAC,IAAI;YACpB,SAAS,EAAE,MAAM;YACjB,IAAI,WAAW,EAAE,MAAM,EAAE,OAAO;YAChC,IAAK,IAAI,QAAQ,QAAQ,GAAI;gBAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG;oBAC1B,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,OAAO,OAAO,IAAI,CAAC;QACnB,SAAS,KAAK,MAAM;QACpB,IAAI,WAAW,OAAO,IAAI,CAAC,GAAG,MAAM,EAAE;YACpC,OAAO;QACT;QACA,IAAK,IAAI,QAAQ,QAAQ,GAAI;YAC3B,IAAI,CAAC,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG;gBACvC,OAAO;YACT;QACF;QACA,IAAK,IAAI,QAAQ,QAAQ,GAAI;YAC3B,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,QAAQ,YAAY,EAAE,QAAQ,EAAE;gBAClC;YACF;YACA,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,GAAG;gBAC9B,OAAO;YACT;QACF;QACA,OAAO;IACT;IACA,OAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,OAAO;IACrB,IAAI,OAAO,WAAW,aAAa;QACjC,OAAO;IACT;IACA,MAAM,MAAM,QAAQ,aAAa,CAAC,WAAW,IAAI;IACjD,OAAO,IAAI,gBAAgB,IAAI;AACjC;AAEA,SAAS,WAAW,OAAO,EAAE,KAAK;IAChC,MAAM,MAAM,OAAO;IACnB,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO;AACnC;AAEA,SAAS,aAAa,KAAK;IACzB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACzB,MAAM;QACJ,IAAI,OAAO,GAAG;IAChB;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,YAAY,OAAO;IAC1B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,YAAY,QAAQ,EACpB,WAAW,UAAU,EACrB,aAAa,EAAE,EACf,QAAQ,EACR,UAAU,EACR,WAAW,iBAAiB,EAC5B,UAAU,gBAAgB,EAC3B,GAAG,CAAC,CAAC,EACN,YAAY,IAAI,EAChB,oBAAoB,EACpB,IAAI,EACL,GAAG;IACJ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;QACrC,GAAG;QACH,GAAG;QACH;QACA;QACA,gBAAgB,CAAC;QACjB,cAAc;IAChB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC/D,IAAI,CAAC,UAAU,kBAAkB,aAAa;QAC5C,oBAAoB;IACtB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAA;QACrC,IAAI,SAAS,aAAa,OAAO,EAAE;YACjC,aAAa,OAAO,GAAG;YACvB,cAAc;QAChB;IACF,GAAG,EAAE;IACL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAA;QACpC,IAAI,SAAS,YAAY,OAAO,EAAE;YAChC,YAAY,OAAO,GAAG;YACtB,aAAa;QACf;IACF,GAAG,EAAE;IACL,MAAM,cAAc,qBAAqB;IACzC,MAAM,aAAa,oBAAoB;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAClC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACjC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,0BAA0B,wBAAwB;IACxD,MAAM,0BAA0B,aAAa;IAC7C,MAAM,cAAc,aAAa;IACjC,MAAM,UAAU,aAAa;IAC7B,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QAC/B,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,YAAY,OAAO,EAAE;YACjD;QACF;QACA,MAAM,SAAS;YACb;YACA;YACA,YAAY;QACd;QACA,IAAI,YAAY,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,YAAY,OAAO;QACvC;QACA,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,OAAO,EAAE,YAAY,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAA;YACtE,MAAM,WAAW;gBACf,GAAG,IAAI;gBACP,sEAAsE;gBACtE,gEAAgE;gBAChE,mEAAmE;gBACnE,kEAAkE;gBAClE,cAAc,QAAQ,OAAO,KAAK;YACpC;YACA,IAAI,aAAa,OAAO,IAAI,CAAC,UAAU,QAAQ,OAAO,EAAE,WAAW;gBACjE,QAAQ,OAAO,GAAG;gBAClB,CAAA,GAAA,4MAAA,CAAA,YAAkB,AAAD,EAAE;oBACjB,QAAQ;gBACV;YACF;QACF;IACF,GAAG;QAAC;QAAkB;QAAW;QAAU;QAAa;KAAQ;IAChE,MAAM;QACJ,IAAI,SAAS,SAAS,QAAQ,OAAO,CAAC,YAAY,EAAE;YAClD,QAAQ,OAAO,CAAC,YAAY,GAAG;YAC/B,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,cAAc;gBAChB,CAAC;QACH;IACF,GAAG;QAAC;KAAK;IACT,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAClC,MAAM;QACJ,aAAa,OAAO,GAAG;QACvB,OAAO;YACL,aAAa,OAAO,GAAG;QACzB;IACF,GAAG,EAAE;IACL,MAAM;QACJ,IAAI,aAAa,aAAa,OAAO,GAAG;QACxC,IAAI,YAAY,YAAY,OAAO,GAAG;QACtC,IAAI,eAAe,YAAY;YAC7B,IAAI,wBAAwB,OAAO,EAAE;gBACnC,OAAO,wBAAwB,OAAO,CAAC,aAAa,YAAY;YAClE;YACA;QACF;IACF,GAAG;QAAC;QAAa;QAAY;QAAQ;QAAyB;KAAwB;IACtF,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YAChC,WAAW;YACX,UAAU;YACV;YACA;QACF,CAAC,GAAG;QAAC;QAAc;KAAY;IAC/B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YACpC,WAAW;YACX,UAAU;QACZ,CAAC,GAAG;QAAC;QAAa;KAAW;IAC7B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnC,MAAM,gBAAgB;YACpB,UAAU;YACV,MAAM;YACN,KAAK;QACP;QACA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO;QACT;QACA,MAAM,IAAI,WAAW,SAAS,QAAQ,EAAE,KAAK,CAAC;QAC9C,MAAM,IAAI,WAAW,SAAS,QAAQ,EAAE,KAAK,CAAC;QAC9C,IAAI,WAAW;YACb,OAAO;gBACL,GAAG,aAAa;gBAChB,WAAW,eAAe,IAAI,SAAS,IAAI;gBAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;oBACtC,YAAY;gBACd,CAAC;YACH;QACF;QACA,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;QACP;IACF,GAAG;QAAC;QAAU;QAAW,SAAS,QAAQ;QAAE,KAAK,CAAC;QAAE,KAAK,CAAC;KAAC;IAC3D,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YAC1B,GAAG,IAAI;YACP;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAM;QAAQ;QAAM;QAAU;KAAe;AACpD;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAA;IACd,SAAS,MAAM,KAAK;QAClB,OAAO,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,OAAO;IACvC;IACA,OAAO;QACL,MAAM;QACN;QACA,IAAG,KAAK;YACN,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG,OAAO,YAAY,aAAa,QAAQ,SAAS;YACrD,IAAI,WAAW,MAAM,UAAU;gBAC7B,IAAI,QAAQ,OAAO,IAAI,MAAM;oBAC3B,OAAO,CAAA,GAAA,yLAAA,CAAA,QAAO,AAAD,EAAE;wBACb,SAAS,QAAQ,OAAO;wBACxB;oBACF,GAAG,EAAE,CAAC;gBACR;gBACA,OAAO,CAAC;YACV;YACA,IAAI,SAAS;gBACX,OAAO,CAAA,GAAA,yLAAA,CAAA,QAAO,AAAD,EAAE;oBACb;oBACA;gBACF,GAAG,EAAE,CAAC;YACR;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,CAAC,SAAS,OAAS,CAAC;QACjC,GAAG,CAAA,GAAA,yLAAA,CAAA,SAAQ,AAAD,EAAE,QAAQ;QACpB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;CAIC,GACD,MAAM,QAAQ,CAAC,SAAS,OAAS,CAAC;QAChC,GAAG,CAAA,GAAA,yLAAA,CAAA,QAAO,AAAD,EAAE,QAAQ;QACnB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;CAEC,GACD,MAAM,aAAa,CAAC,SAAS,OAAS,CAAC;QACrC,GAAG,CAAA,GAAA,yLAAA,CAAA,aAAY,AAAD,EAAE,QAAQ;QACxB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,OAAO,CAAC,SAAS,OAAS,CAAC;QAC/B,GAAG,CAAA,GAAA,yLAAA,CAAA,OAAM,AAAD,EAAE,QAAQ;QAClB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,OAAO,CAAC,SAAS,OAAS,CAAC;QAC/B,GAAG,CAAA,GAAA,yLAAA,CAAA,OAAM,AAAD,EAAE,QAAQ;QAClB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC,SAAS,OAAS,CAAC;QACxC,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAe,AAAD,EAAE,QAAQ;QAC3B,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;CAIC,GACD,MAAM,OAAO,CAAC,SAAS,OAAS,CAAC;QAC/B,GAAG,CAAA,GAAA,yLAAA,CAAA,OAAM,AAAD,EAAE,QAAQ;QAClB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;CAIC,GACD,MAAM,SAAS,CAAC,SAAS,OAAS,CAAC;QACjC,GAAG,CAAA,GAAA,yLAAA,CAAA,SAAQ,AAAD,EAAE,QAAQ;QACpB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,QAAQ,CAAC,SAAS,OAAS,CAAC;QAChC,GAAG,QAAQ,QAAQ;QACnB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4504, "column": 0}, "map": {"version": 3, "file": "index.esm.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/node_modules/tabbable/src/index.js"], "sourcesContent": ["// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n"], "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable"], "mappings": ";;;GAAA,+EAAA;AACA,yCAAA;AACA,mFAAA;AACA,iFAAA;AACA,qFAAA;;;;;;;;AACA,IAAMA,kBAAkB,GAAG;IACzB,oBAAoB;IACpB,qBAAqB;IACrB,uBAAuB;IACvB,sBAAsB;IACtB,qBAAqB;IACrB,mCAAmC;IACnC,8BAA8B;IAC9B,8BAA8B;IAC9B,+DAA+D;IAC/D,4CAA4C;IAC5C,sBAAsB;CACvB,CAAA;AACD,IAAMC,iBAAiB,GAAA,aAAA,GAAmBD,kBAAkB,CAACE,IAAI,CAAC,GAAG,CAAC,CAAA;AAEtE,IAAMC,SAAS,GAAG,OAAOC,OAAO,KAAK,WAAW,CAAA;AAEhD,IAAMC,OAAO,GAAGF,SAAS,GACrB,YAAY,CAAE,GACdC,OAAO,CAACE,SAAS,CAACD,OAAO,IACzBD,OAAO,CAACE,SAAS,CAACC,iBAAiB,IACnCH,OAAO,CAACE,SAAS,CAACE,qBAAqB,CAAA;AAE3C,IAAMC,WAAW,GACf,CAACN,SAAS,IAAIC,OAAO,CAACE,SAAS,CAACG,WAAW,GACvC,SAACC,OAAO,EAAA;IAAA,IAAAC,oBAAA,CAAA;IAAA,OAAKD,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAAC,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,oBAAA,GAAPD,OAAO,CAAED,WAAW,MAAA,IAAA,IAAAE,oBAAA,KAApBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAAC,IAAA,CAAAF,OAAuB,CAAC,CAAA;AAAA,CAAA,GACrC,SAACA,OAAO,EAAA;IAAA,OAAKA,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAPA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEG,aAAa,CAAA;AAAA,CAAA,CAAA;AAEzC;;;;;;;CAOA,GACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAaC,IAAI,EAAEC,MAAM,EAAS;IAAA,IAAAC,kBAAA,CAAA;IAAA,IAAfD,MAAM,KAAA,KAAA,CAAA,EAAA;QAANA,MAAM,GAAG,IAAI,CAAA;IAAA,CAAA;IAC3C,wFAAA;IACA,yFAAA;IACA,qEAAA;IACA,IAAME,QAAQ,GAAGH,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAAE,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,kBAAA,GAAJF,IAAI,CAAEI,YAAY,MAAAF,IAAAA,IAAAA,kBAAA,KAAA,KAAA,IAAA,KAAA,IAAlBA,kBAAA,CAAAL,IAAA,CAAAG,IAAI,EAAiB,OAAO,CAAC,CAAA;IAC9C,IAAMK,KAAK,GAAGF,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,MAAM,CAAA;IAEpD,kFAAA;IACA,qFAAA;IACA,mCAAA;IACA,qFAAA;IACA,6DAAA;IACA,IAAMG,MAAM,GAAGD,KAAK,IAAKJ,MAAM,IAAID,IAAI,IAAID,OAAO,CAACC,IAAI,CAACO,UAAU,CAAE,CAAC,CAAA,YAAA;IAErE,OAAOD,MAAM,CAAA;AACf,CAAC,CAAA;AAED;;;;CAIA,GACA,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaR,IAAI,EAAE;IAAA,IAAAS,mBAAA,CAAA;IACxC,qFAAA;IACA,yFAAA;IACA,0EAAA;IACA,IAAMC,QAAQ,GAAGV,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAAS,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,mBAAA,GAAJT,IAAI,CAAEI,YAAY,MAAAK,IAAAA,IAAAA,mBAAA,KAAA,KAAA,IAAA,KAAA,IAAlBA,mBAAA,CAAAZ,IAAA,CAAAG,IAAI,EAAiB,iBAAiB,CAAC,CAAA;IACxD,OAAOU,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,MAAM,CAAA;AAC/C,CAAC,CAAA;AAED;;;;;CAKA,GACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,EAAE,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;IAC5D,oFAAA;IACA,6CAAA;IACA,IAAIf,OAAO,CAACa,EAAE,CAAC,EAAE;QACf,OAAO,EAAE,CAAA;IACX,CAAA;IAEA,IAAIG,UAAU,GAAGC,KAAK,CAACzB,SAAS,CAAC0B,KAAK,CAACC,KAAK,CAC1CN,EAAE,CAACO,gBAAgB,CAACjC,iBAAiB,CACvC,CAAC,CAAA;IACD,IAAI2B,gBAAgB,IAAIvB,OAAO,CAACO,IAAI,CAACe,EAAE,EAAE1B,iBAAiB,CAAC,EAAE;QAC3D6B,UAAU,CAACK,OAAO,CAACR,EAAE,CAAC,CAAA;IACxB,CAAA;IACAG,UAAU,GAAGA,UAAU,CAACD,MAAM,CAACA,MAAM,CAAC,CAAA;IACtC,OAAOC,UAAU,CAAA;AACnB,CAAC,CAAA;AAED;;;;CAIA,GAEA;;;;CAIA,GAEA;;;;CAIA,GAEA;;;;;;;;CAQA,GAEA;;;;;CAKA,GACA,IAAMM,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAC5BC,QAAQ,EACRT,gBAAgB,EAChBU,OAAO,EACP;IACA,IAAMR,UAAU,GAAG,EAAE,CAAA;IACrB,IAAMS,eAAe,GAAGR,KAAK,CAACS,IAAI,CAACH,QAAQ,CAAC,CAAA;IAC5C,MAAOE,eAAe,CAACE,MAAM,CAAE;QAC7B,IAAM/B,OAAO,GAAG6B,eAAe,CAACG,KAAK,EAAE,CAAA;QACvC,IAAI5B,OAAO,CAACJ,OAAO,EAAE,KAAK,CAAC,EAAE;YAG3B,SAAA;QACF,CAAA;QAEA,IAAIA,OAAO,CAACiC,OAAO,KAAK,MAAM,EAAE;YAC9B,8DAAA;YACA,IAAMC,QAAQ,GAAGlC,OAAO,CAACmC,gBAAgB,EAAE,CAAA;YAC3C,IAAMC,OAAO,GAAGF,QAAQ,CAACH,MAAM,GAAGG,QAAQ,GAAGlC,OAAO,CAACqC,QAAQ,CAAA;YAC7D,IAAMC,gBAAgB,GAAGZ,wBAAwB,CAACU,OAAO,EAAE,IAAI,EAAER,OAAO,CAAC,CAAA;YACzE,IAAIA,OAAO,CAACW,OAAO,EAAE;gBACnBnB,UAAU,CAACoB,IAAI,CAAAjB,KAAA,CAAfH,UAAU,EAASkB,gBAAgB,CAAC,CAAA;YACtC,CAAC,MAAM;gBACLlB,UAAU,CAACoB,IAAI,CAAC;oBACdC,WAAW,EAAEzC,OAAO;oBACpBoB,UAAU,EAAEkB,gBAAAA;gBACd,CAAC,CAAC,CAAA;YACJ,CAAA;QACF,CAAC,MAAM;YACL,0BAAA;YACA,IAAMI,cAAc,GAAG/C,OAAO,CAACO,IAAI,CAACF,OAAO,EAAET,iBAAiB,CAAC,CAAA;YAC/D,IACEmD,cAAc,IACdd,OAAO,CAACT,MAAM,CAACnB,OAAO,CAAC,IAAA,CACtBkB,gBAAgB,IAAI,CAACS,QAAQ,CAACgB,QAAQ,CAAC3C,OAAO,CAAC,CAAC,EACjD;gBACAoB,UAAU,CAACoB,IAAI,CAACxC,OAAO,CAAC,CAAA;YAC1B,CAAA;YAEA,0CAAA;YACA,IAAM4C,UAAU,GACd5C,OAAO,CAAC4C,UAAU,IAClB,kCAAA;YACC,OAAOhB,OAAO,CAACiB,aAAa,KAAK,UAAU,IAC1CjB,OAAO,CAACiB,aAAa,CAAC7C,OAAO,CAAE,CAAA;YAEnC,kFAAA;YACA,iFAAA;YACA,uBAAA;YACA,IAAM8C,eAAe,GACnB,CAAC1C,OAAO,CAACwC,UAAU,EAAE,KAAK,CAAC,IAAA,CAC1B,CAAChB,OAAO,CAACmB,gBAAgB,IAAInB,OAAO,CAACmB,gBAAgB,CAAC/C,OAAO,CAAC,CAAC,CAAA;YAElE,IAAI4C,UAAU,IAAIE,eAAe,EAAE;gBACjC,mFAAA;gBACA,uFAAA;gBACA,qFAAA;gBACA,uFAAA;gBACA,qFAAA;gBACA,8CAAA;gBACA,IAAMR,iBAAgB,GAAGZ,wBAAwB,CAC/CkB,UAAU,KAAK,IAAI,GAAG5C,OAAO,CAACqC,QAAQ,GAAGO,UAAU,CAACP,QAAQ,EAC5D,IAAI,EACJT,OACF,CAAC,CAAA;gBAED,IAAIA,OAAO,CAACW,OAAO,EAAE;oBACnBnB,UAAU,CAACoB,IAAI,CAAAjB,KAAA,CAAfH,UAAU,EAASkB,iBAAgB,CAAC,CAAA;gBACtC,CAAC,MAAM;oBACLlB,UAAU,CAACoB,IAAI,CAAC;wBACdC,WAAW,EAAEzC,OAAO;wBACpBoB,UAAU,EAAEkB,iBAAAA;oBACd,CAAC,CAAC,CAAA;gBACJ,CAAA;YACF,CAAC,MAAM;gBACL,yEAAA;gBACA,0DAAA;gBACAT,eAAe,CAACJ,OAAO,CAAAF,KAAA,CAAvBM,eAAe,EAAY7B,OAAO,CAACqC,QAAQ,CAAC,CAAA;YAC9C,CAAA;QACF,CAAA;IACF,CAAA;IACA,OAAOjB,UAAU,CAAA;AACnB,CAAC,CAAA;AAED;;;;;CAKA,GACA,IAAM4B,WAAW,GAAG,SAAdA,WAAWA,CAAa3C,IAAI,EAAE;IAClC,OAAO,CAAC4C,KAAK,CAACC,QAAQ,CAAC7C,IAAI,CAACI,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAC5D,CAAC,CAAA;AAED;;;;;CAKA,GACA,IAAM0C,WAAW,GAAG,SAAdA,WAAWA,CAAa9C,IAAI,EAAE;IAClC,IAAI,CAACA,IAAI,EAAE;QACT,MAAM,IAAI+C,KAAK,CAAC,kBAAkB,CAAC,CAAA;IACrC,CAAA;IAEA,IAAI/C,IAAI,CAACgD,QAAQ,GAAG,CAAC,EAAE;QACrB,wFAAA;QACA,6EAAA;QACA,8EAAA;QACA,6EAAA;QACA,2CAAA;QACA,8EAAA;QACA,8EAAA;QACA,IACE,CAAC,yBAAyB,CAACC,IAAI,CAACjD,IAAI,CAAC4B,OAAO,CAAC,IAC3CpB,iBAAiB,CAACR,IAAI,CAAC,KACzB,CAAC2C,WAAW,CAAC3C,IAAI,CAAC,EAClB;YACA,OAAO,CAAC,CAAA;QACV,CAAA;IACF,CAAA;IAEA,OAAOA,IAAI,CAACgD,QAAQ,CAAA;AACtB,EAAC;AAED;;;;;;;CAOA,GACA,IAAME,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAalD,IAAI,EAAEmD,OAAO,EAAE;IACpD,IAAMH,QAAQ,GAAGF,WAAW,CAAC9C,IAAI,CAAC,CAAA;IAElC,IAAIgD,QAAQ,GAAG,CAAC,IAAIG,OAAO,IAAI,CAACR,WAAW,CAAC3C,IAAI,CAAC,EAAE;QACjD,OAAO,CAAC,CAAA;IACV,CAAA;IAEA,OAAOgD,QAAQ,CAAA;AACjB,CAAC,CAAA;AAED,IAAMI,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAaC,CAAC,EAAEC,CAAC,EAAE;IAC3C,OAAOD,CAAC,CAACL,QAAQ,KAAKM,CAAC,CAACN,QAAQ,GAC5BK,CAAC,CAACE,aAAa,GAAGD,CAAC,CAACC,aAAa,GACjCF,CAAC,CAACL,QAAQ,GAAGM,CAAC,CAACN,QAAQ,CAAA;AAC7B,CAAC,CAAA;AAED,IAAMQ,OAAO,GAAG,SAAVA,OAAOA,CAAaxD,IAAI,EAAE;IAC9B,OAAOA,IAAI,CAAC4B,OAAO,KAAK,OAAO,CAAA;AACjC,CAAC,CAAA;AAED,IAAM6B,aAAa,GAAG,SAAhBA,aAAaA,CAAazD,IAAI,EAAE;IACpC,OAAOwD,OAAO,CAACxD,IAAI,CAAC,IAAIA,IAAI,CAAC0D,IAAI,KAAK,QAAQ,CAAA;AAChD,CAAC,CAAA;AAED,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa3D,IAAI,EAAE;IAC3C,IAAM4D,CAAC,GACL5D,IAAI,CAAC4B,OAAO,KAAK,SAAS,IAC1BZ,KAAK,CAACzB,SAAS,CAAC0B,KAAK,CAClBC,KAAK,CAAClB,IAAI,CAACgC,QAAQ,CAAC,CACpB6B,IAAI,CAAC,SAACC,KAAK,EAAA;QAAA,OAAKA,KAAK,CAAClC,OAAO,KAAK,SAAS,CAAA;KAAC,CAAA,CAAA;IACjD,OAAOgC,CAAC,CAAA;AACV,CAAC,CAAA;AAED,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,KAAK,EAAEC,IAAI,EAAE;IAC7C,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACtC,MAAM,EAAEwC,CAAC,EAAE,CAAE;QACrC,IAAIF,KAAK,CAACE,CAAC,CAAC,CAACC,OAAO,IAAIH,KAAK,CAACE,CAAC,CAAC,CAACD,IAAI,KAAKA,IAAI,EAAE;YAC9C,OAAOD,KAAK,CAACE,CAAC,CAAC,CAAA;QACjB,CAAA;IACF,CAAA;AACF,CAAC,CAAA;AAED,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAapE,IAAI,EAAE;IACtC,IAAI,CAACA,IAAI,CAACqE,IAAI,EAAE;QACd,OAAO,IAAI,CAAA;IACb,CAAA;IACA,IAAMC,UAAU,GAAGtE,IAAI,CAACiE,IAAI,IAAIvE,WAAW,CAACM,IAAI,CAAC,CAAA;IACjD,IAAMuE,WAAW,GAAG,SAAdA,WAAWA,CAAaF,IAAI,EAAE;QAClC,OAAOC,UAAU,CAACnD,gBAAgB,CAChC,4BAA4B,GAAGkD,IAAI,GAAG,IACxC,CAAC,CAAA;KACF,CAAA;IAED,IAAIG,QAAQ,CAAA;IACZ,IACE,OAAOC,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACC,GAAG,KAAK,WAAW,IACjC,OAAOD,MAAM,CAACC,GAAG,CAACC,MAAM,KAAK,UAAU,EACvC;QACAH,QAAQ,GAAGD,WAAW,CAACE,MAAM,CAACC,GAAG,CAACC,MAAM,CAAC3E,IAAI,CAACqE,IAAI,CAAC,CAAC,CAAA;IACtD,CAAC,MAAM;QACL,IAAI;YACFG,QAAQ,GAAGD,WAAW,CAACvE,IAAI,CAACqE,IAAI,CAAC,CAAA;SAClC,CAAC,OAAOO,GAAG,EAAE;YACZ,sCAAA;YACAC,OAAO,CAACC,KAAK,CACX,0IAA0I,EAC1IF,GAAG,CAACG,OACN,CAAC,CAAA;YACD,OAAO,KAAK,CAAA;QACd,CAAA;IACF,CAAA;IAEA,IAAMZ,OAAO,GAAGJ,eAAe,CAACS,QAAQ,EAAExE,IAAI,CAACiE,IAAI,CAAC,CAAA;IACpD,OAAO,CAACE,OAAO,IAAIA,OAAO,KAAKnE,IAAI,CAAA;AACrC,CAAC,CAAA;AAED,IAAMgF,OAAO,GAAG,SAAVA,OAAOA,CAAahF,IAAI,EAAE;IAC9B,OAAOwD,OAAO,CAACxD,IAAI,CAAC,IAAIA,IAAI,CAAC0D,IAAI,KAAK,OAAO,CAAA;AAC/C,CAAC,CAAA;AAED,IAAMuB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAajF,IAAI,EAAE;IACzC,OAAOgF,OAAO,CAAChF,IAAI,CAAC,IAAI,CAACoE,eAAe,CAACpE,IAAI,CAAC,CAAA;AAChD,CAAC,CAAA;AAED,uEAAA;AACA,IAAMkF,cAAc,GAAG,SAAjBA,cAAcA,CAAalF,IAAI,EAAE;IAAA,IAAAmF,SAAA,CAAA;IACrC,2FAAA;IACA,qEAAA;IACA,0FAAA;IACA,yFAAA;IACA,wFAAA;IACA,6FAAA;IACA,4FAAA;IACA,wFAAA;IACA,2FAAA;IACA,2FAAA;IACA,YAAA;IACA,2FAAA;IACA,kFAAA;IACA,wFAAA;IACA,yFAAA;IACA,sFAAA;IACA,kFAAA;IACA,wFAAA;IACA,8BAAA;IACA,2FAAA;IACA,wFAAA;IACA,uFAAA;IACA,sEAAA;IACA,IAAIC,QAAQ,GAAGpF,IAAI,IAAIN,WAAW,CAACM,IAAI,CAAC,CAAA;IACxC,IAAIqF,YAAY,GAAAF,CAAAA,SAAA,GAAGC,QAAQ,MAAA,QAAAD,SAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAARA,SAAA,CAAUG,IAAI,CAAA;IAEjC,yFAAA;IACA,yFAAA;IACA,IAAIC,QAAQ,GAAG,KAAK,CAAA;IACpB,IAAIH,QAAQ,IAAIA,QAAQ,KAAKpF,IAAI,EAAE;QAAA,IAAAwF,aAAA,EAAAC,qBAAA,EAAAC,mBAAA,CAAA;QACjCH,QAAQ,GAAG,CAAC,CAAA,CACV,CAAAC,aAAA,GAAAH,YAAY,MAAAG,IAAAA,IAAAA,aAAA,KAAA,KAAA,KAAA,CAAAC,qBAAA,GAAZD,aAAA,CAAc1F,aAAa,MAAA,QAAA2F,qBAAA,KAAA,KAAA,CAAA,IAA3BA,qBAAA,CAA6BE,QAAQ,CAACN,YAAY,CAAC,IACnDrF,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAA0F,KAAAA,CAAAA,IAAAA,CAAAA,mBAAA,GAAJ1F,IAAI,CAAEF,aAAa,MAAA4F,IAAAA,IAAAA,mBAAA,KAAA,KAAA,KAAnBA,mBAAA,CAAqBC,QAAQ,CAAC3F,IAAI,CAAC,CACpC,CAAA;QAED,MAAO,CAACuF,QAAQ,IAAIF,YAAY,CAAE;YAAA,IAAAO,UAAA,EAAAC,cAAA,EAAAC,qBAAA,CAAA;YAChC,4FAAA;YACA,yFAAA;YACA,sCAAA;YACAV,QAAQ,GAAG1F,WAAW,CAAC2F,YAAY,CAAC,CAAA;YACpCA,YAAY,GAAA,CAAAO,UAAA,GAAGR,QAAQ,MAAA,QAAAQ,UAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAARA,UAAA,CAAUN,IAAI,CAAA;YAC7BC,QAAQ,GAAG,CAAC,CAAA,CAAAM,CAAAA,cAAA,GAACR,YAAY,MAAA,IAAA,IAAAQ,cAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,qBAAA,GAAZD,cAAA,CAAc/F,aAAa,MAAA,QAAAgG,qBAAA,KAAA,KAAA,CAAA,IAA3BA,qBAAA,CAA6BH,QAAQ,CAACN,YAAY,CAAC,CAAA,CAAA;QAClE,CAAA;IACF,CAAA;IAEA,OAAOE,QAAQ,CAAA;AACjB,CAAC,CAAA;AAED,IAAMQ,UAAU,GAAG,SAAbA,UAAUA,CAAa/F,IAAI,EAAE;IACjC,IAAAgG,qBAAA,GAA0BhG,IAAI,CAACiG,qBAAqB,EAAE,EAA9CC,KAAK,GAAAF,qBAAA,CAALE,KAAK,EAAEC,MAAM,GAAAH,qBAAA,CAANG,MAAM,CAAA;IACrB,OAAOD,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,CAAA;AACpC,CAAC,CAAA;AACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAapG,IAAI,EAAAqG,IAAA,EAAmC;IAAA,IAA/BC,YAAY,GAAAD,IAAA,CAAZC,YAAY,EAAE9D,aAAa,GAAA6D,IAAA,CAAb7D,aAAa,CAAA;IAC5D,6EAAA;IACA,gFAAA;IACA,uDAAA;IACA,4EAAA;IACA,6CAAA;IACA,IAAI+D,gBAAgB,CAACvG,IAAI,CAAC,CAACwG,UAAU,KAAK,QAAQ,EAAE;QAClD,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,IAAMC,eAAe,GAAGnH,OAAO,CAACO,IAAI,CAACG,IAAI,EAAE,+BAA+B,CAAC,CAAA;IAC3E,IAAM0G,gBAAgB,GAAGD,eAAe,GAAGzG,IAAI,CAAC2G,aAAa,GAAG3G,IAAI,CAAA;IACpE,IAAIV,OAAO,CAACO,IAAI,CAAC6G,gBAAgB,EAAE,uBAAuB,CAAC,EAAE;QAC3D,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,IACE,CAACJ,YAAY,IACbA,YAAY,KAAK,MAAM,IACvBA,YAAY,KAAK,aAAa,EAC9B;QACA,IAAI,OAAO9D,aAAa,KAAK,UAAU,EAAE;YACvC,uFAAA;YACA,4BAAA;YACA,IAAMoE,YAAY,GAAG5G,IAAI,CAAA;YACzB,MAAOA,IAAI,CAAE;gBACX,IAAM2G,aAAa,GAAG3G,IAAI,CAAC2G,aAAa,CAAA;gBACxC,IAAME,QAAQ,GAAGnH,WAAW,CAACM,IAAI,CAAC,CAAA;gBAClC,IACE2G,aAAa,IACb,CAACA,aAAa,CAACpE,UAAU,IACzBC,aAAa,CAACmE,aAAa,CAAC,KAAK,IAAI,CAAA,yCAAA;kBACrC;oBACA,wFAAA;oBACA,qCAAA;oBACA,OAAOZ,UAAU,CAAC/F,IAAI,CAAC,CAAA;gBACzB,CAAC,MAAM,IAAIA,IAAI,CAAC8G,YAAY,EAAE;oBAC5B,kBAAA;oBACA9G,IAAI,GAAGA,IAAI,CAAC8G,YAAY,CAAA;iBACzB,MAAM,IAAI,CAACH,aAAa,IAAIE,QAAQ,KAAK7G,IAAI,CAACF,aAAa,EAAE;oBAC5D,wBAAA;oBACAE,IAAI,GAAG6G,QAAQ,CAACvB,IAAI,CAAA;gBACtB,CAAC,MAAM;oBACL,wBAAA;oBACAtF,IAAI,GAAG2G,aAAa,CAAA;gBACtB,CAAA;YACF,CAAA;YAEA3G,IAAI,GAAG4G,YAAY,CAAA;QACrB,CAAA;QACA,sFAAA;QACA,qFAAA;QACA,yEAAA;QAEA,qFAAA;QACA,qFAAA;QACA,kCAAA;QACA,sFAAA;QACA,sFAAA;QAEA,IAAI1B,cAAc,CAAClF,IAAI,CAAC,EAAE;YACxB,6EAAA;YACA,gFAAA;YACA,iFAAA;YACA,2BAAA;YACA,OAAO,CAACA,IAAI,CAAC+G,cAAc,EAAE,CAACrF,MAAM,CAAA;QACtC,CAAA;QAEA,oFAAA;QACA,iFAAA;QACA,8EAAA;QACA,yEAAA;QACA,EAAA;QACA,wFAAA;QACA,wEAAA;QACA,EAAA;QACA,sFAAA;QACA,qFAAA;QACA,sFAAA;QACA,4FAAA;QACA,2FAAA;QACA,EAAA;QACA,0FAAA;QACA,gDAAA;QACA,IAAI4E,YAAY,KAAK,aAAa,EAAE;YAClC,OAAO,IAAI,CAAC,CAAA,SAAA;QACd,CAAA;IACA,8DAAA;IACF,CAAC,MAAM,IAAIA,YAAY,KAAK,eAAe,EAAE;QAC3C,oFAAA;QACA,qFAAA;QACA,kFAAA;QACA,qFAAA;QACA,6CAAA;QACA,OAAOP,UAAU,CAAC/F,IAAI,CAAC,CAAA;IACzB,CAAA;IAEA,qFAAA;IACA,gBAAA;IACA,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED,6EAAA;AACA,4EAAA;AACA,YAAA;AACA,IAAMgH,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAahH,IAAI,EAAE;IAC7C,IAAI,kCAAkC,CAACiD,IAAI,CAACjD,IAAI,CAAC4B,OAAO,CAAC,EAAE;QACzD,IAAIrB,UAAU,GAAGP,IAAI,CAAC2G,aAAa,CAAA;QACnC,wDAAA;QACA,MAAOpG,UAAU,CAAE;YACjB,IAAIA,UAAU,CAACqB,OAAO,KAAK,UAAU,IAAIrB,UAAU,CAAC0G,QAAQ,EAAE;gBAC5D,4EAAA;gBACA,IAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,UAAU,CAACyB,QAAQ,CAACN,MAAM,EAAEwC,CAAC,EAAE,CAAE;oBACnD,IAAMJ,KAAK,GAAGvD,UAAU,CAACyB,QAAQ,CAACkF,IAAI,CAAChD,CAAC,CAAC,CAAA;oBACzC,uDAAA;oBACA,IAAIJ,KAAK,CAAClC,OAAO,KAAK,QAAQ,EAAE;wBAC9B,yEAAA;wBACA,8DAAA;wBACA,OAAOtC,OAAO,CAACO,IAAI,CAACU,UAAU,EAAE,sBAAsB,CAAC,GACnD,IAAI,GACJ,CAACuD,KAAK,CAAC6B,QAAQ,CAAC3F,IAAI,CAAC,CAAA;oBAC3B,CAAA;gBACF,CAAA;gBACA,4DAAA;gBACA,OAAO,IAAI,CAAA;YACb,CAAA;YACAO,UAAU,GAAGA,UAAU,CAACoG,aAAa,CAAA;QACvC,CAAA;IACF,CAAA;IAEA,+EAAA;IACA,0BAAA;IACA,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED,IAAMQ,+BAA+B,GAAG,SAAlCA,+BAA+BA,CAAa5F,OAAO,EAAEvB,IAAI,EAAE;IAC/D,IACEA,IAAI,CAACiH,QAAQ,IACb,kFAAA;IACA,mFAAA;IACA,yCAAA;IACAlH,OAAO,CAACC,IAAI,CAAC,IACbyD,aAAa,CAACzD,IAAI,CAAC,IACnBoG,QAAQ,CAACpG,IAAI,EAAEuB,OAAO,CAAC,IACvB,2EAAA;IACAoC,oBAAoB,CAAC3D,IAAI,CAAC,IAC1BgH,sBAAsB,CAAChH,IAAI,CAAC,EAC5B;QACA,OAAO,KAAK,CAAA;IACd,CAAA;IACA,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,IAAMoH,8BAA8B,GAAG,SAAjCA,8BAA8BA,CAAa7F,OAAO,EAAEvB,IAAI,EAAE;IAC9D,IACEiF,kBAAkB,CAACjF,IAAI,CAAC,IACxB8C,WAAW,CAAC9C,IAAI,CAAC,GAAG,CAAC,IACrB,CAACmH,+BAA+B,CAAC5F,OAAO,EAAEvB,IAAI,CAAC,EAC/C;QACA,OAAO,KAAK,CAAA;IACd,CAAA;IACA,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,IAAMqH,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,cAAc,EAAE;IAC1D,IAAMtE,QAAQ,GAAGH,QAAQ,CAACyE,cAAc,CAAClH,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAA;IACtE,IAAIwC,KAAK,CAACI,QAAQ,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;QACpC,OAAO,IAAI,CAAA;IACb,CAAA;IACA,yDAAA;IACA,iEAAA;IACA,OAAO,KAAK,CAAA;AACd,CAAC,CAAA;AAED;;;CAGA,GACA,IAAMuE,WAAW,GAAG,SAAdA,WAAWA,CAAaxG,UAAU,EAAE;IACxC,IAAMyG,gBAAgB,GAAG,EAAE,CAAA;IAC3B,IAAMC,gBAAgB,GAAG,EAAE,CAAA;IAC3B1G,UAAU,CAAC2G,OAAO,CAAC,SAAUR,IAAI,EAAEhD,CAAC,EAAE;QACpC,IAAMf,OAAO,GAAG,CAAC,CAAC+D,IAAI,CAAC9E,WAAW,CAAA;QAClC,IAAMzC,OAAO,GAAGwD,OAAO,GAAG+D,IAAI,CAAC9E,WAAW,GAAG8E,IAAI,CAAA;QACjD,IAAMS,iBAAiB,GAAGzE,oBAAoB,CAACvD,OAAO,EAAEwD,OAAO,CAAC,CAAA;QAChE,IAAM7B,QAAQ,GAAG6B,OAAO,GAAGoE,WAAW,CAACL,IAAI,CAACnG,UAAU,CAAC,GAAGpB,OAAO,CAAA;QACjE,IAAIgI,iBAAiB,KAAK,CAAC,EAAE;YAC3BxE,OAAO,GACHqE,gBAAgB,CAACrF,IAAI,CAAAjB,KAAA,CAArBsG,gBAAgB,EAASlG,QAAQ,CAAC,GAClCkG,gBAAgB,CAACrF,IAAI,CAACxC,OAAO,CAAC,CAAA;QACpC,CAAC,MAAM;YACL8H,gBAAgB,CAACtF,IAAI,CAAC;gBACpBoB,aAAa,EAAEW,CAAC;gBAChBlB,QAAQ,EAAE2E,iBAAiB;gBAC3BT,IAAI,EAAEA,IAAI;gBACV/D,OAAO,EAAEA,OAAO;gBAChBpB,OAAO,EAAET,QAAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAA;IACF,CAAC,CAAC,CAAA;IAEF,OAAOmG,gBAAgB,CACpBG,IAAI,CAACxE,oBAAoB,CAAC,CAC1ByE,MAAM,CAAC,SAACC,GAAG,EAAEC,QAAQ,EAAK;QACzBA,QAAQ,CAAC5E,OAAO,GACZ2E,GAAG,CAAC3F,IAAI,CAAAjB,KAAA,CAAR4G,GAAG,EAASC,QAAQ,CAAChG,OAAO,CAAC,GAC7B+F,GAAG,CAAC3F,IAAI,CAAC4F,QAAQ,CAAChG,OAAO,CAAC,CAAA;QAC9B,OAAO+F,GAAG,CAAA;IACZ,CAAC,EAAE,EAAE,CAAC,CACLE,MAAM,CAACR,gBAAgB,CAAC,CAAA;AAC7B,CAAC,CAAA;AAEKS,IAAAA,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,SAAS,EAAE3G,OAAO,EAAE;IAC7CA,OAAO,GAAGA,OAAO,IAAI,CAAA,CAAE,CAAA;IAEvB,IAAIR,UAAU,CAAA;IACd,IAAIQ,OAAO,CAACiB,aAAa,EAAE;QACzBzB,UAAU,GAAGM,wBAAwB,CACnC;YAAC6G,SAAS;SAAC,EACX3G,OAAO,CAACV,gBAAgB,EACxB;YACEC,MAAM,EAAEsG,8BAA8B,CAACe,IAAI,CAAC,IAAI,EAAE5G,OAAO,CAAC;YAC1DW,OAAO,EAAE,KAAK;YACdM,aAAa,EAAEjB,OAAO,CAACiB,aAAa;YACpCE,gBAAgB,EAAE2E,yBAAAA;QACpB,CACF,CAAC,CAAA;IACH,CAAC,MAAM;QACLtG,UAAU,GAAGJ,aAAa,CACxBuH,SAAS,EACT3G,OAAO,CAACV,gBAAgB,EACxBuG,8BAA8B,CAACe,IAAI,CAAC,IAAI,EAAE5G,OAAO,CACnD,CAAC,CAAA;IACH,CAAA;IACA,OAAOgG,WAAW,CAACxG,UAAU,CAAC,CAAA;AAChC,EAAC;AAEKqH,IAAAA,SAAS,GAAG,SAAZA,SAASA,CAAaF,SAAS,EAAE3G,OAAO,EAAE;IAC9CA,OAAO,GAAGA,OAAO,IAAI,CAAA,CAAE,CAAA;IAEvB,IAAIR,UAAU,CAAA;IACd,IAAIQ,OAAO,CAACiB,aAAa,EAAE;QACzBzB,UAAU,GAAGM,wBAAwB,CACnC;YAAC6G,SAAS;SAAC,EACX3G,OAAO,CAACV,gBAAgB,EACxB;YACEC,MAAM,EAAEqG,+BAA+B,CAACgB,IAAI,CAAC,IAAI,EAAE5G,OAAO,CAAC;YAC3DW,OAAO,EAAE,IAAI;YACbM,aAAa,EAAEjB,OAAO,CAACiB,aAAAA;QACzB,CACF,CAAC,CAAA;IACH,CAAC,MAAM;QACLzB,UAAU,GAAGJ,aAAa,CACxBuH,SAAS,EACT3G,OAAO,CAACV,gBAAgB,EACxBsG,+BAA+B,CAACgB,IAAI,CAAC,IAAI,EAAE5G,OAAO,CACpD,CAAC,CAAA;IACH,CAAA;IAEA,OAAOR,UAAU,CAAA;AACnB,EAAC;AAEKsH,IAAAA,UAAU,GAAG,SAAbA,UAAUA,CAAarI,IAAI,EAAEuB,OAAO,EAAE;IAC1CA,OAAO,GAAGA,OAAO,IAAI,CAAA,CAAE,CAAA;IACvB,IAAI,CAACvB,IAAI,EAAE;QACT,MAAM,IAAI+C,KAAK,CAAC,kBAAkB,CAAC,CAAA;IACrC,CAAA;IACA,IAAIzD,OAAO,CAACO,IAAI,CAACG,IAAI,EAAEd,iBAAiB,CAAC,KAAK,KAAK,EAAE;QACnD,OAAO,KAAK,CAAA;IACd,CAAA;IACA,OAAOkI,8BAA8B,CAAC7F,OAAO,EAAEvB,IAAI,CAAC,CAAA;AACtD,EAAC;AAED,IAAMsI,0BAA0B,GAAA,aAAA,GAAmBrJ,kBAAkB,CAClE+I,MAAM,CAAC,QAAQ,CAAC,CAChB7I,IAAI,CAAC,GAAG,CAAC,CAAA;AAENoJ,IAAAA,WAAW,GAAG,SAAdA,WAAWA,CAAavI,IAAI,EAAEuB,OAAO,EAAE;IAC3CA,OAAO,GAAGA,OAAO,IAAI,CAAA,CAAE,CAAA;IACvB,IAAI,CAACvB,IAAI,EAAE;QACT,MAAM,IAAI+C,KAAK,CAAC,kBAAkB,CAAC,CAAA;IACrC,CAAA;IACA,IAAIzD,OAAO,CAACO,IAAI,CAACG,IAAI,EAAEsI,0BAA0B,CAAC,KAAK,KAAK,EAAE;QAC5D,OAAO,KAAK,CAAA;IACd,CAAA;IACA,OAAOnB,+BAA+B,CAAC5F,OAAO,EAAEvB,IAAI,CAAC,CAAA;AACvD", "ignoreList": [0], "debugId": null}}]}