module.exports = {

"[project]/.next-internal/server/app/api/claims/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/db.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Claim.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const ClaimSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    item: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'Item',
        required: true
    },
    claimant: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    itemOwner: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    status: {
        type: String,
        enum: [
            'pending',
            'approved',
            'rejected',
            'completed',
            'cancelled'
        ],
        default: 'pending'
    },
    message: {
        type: String,
        required: [
            true,
            'Claim message is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Message cannot be more than 500 characters'
        ]
    },
    proofImages: [
        {
            url: {
                type: String,
                required: true
            },
            publicId: {
                type: String,
                required: true
            }
        }
    ],
    verificationQuestions: [
        {
            question: {
                type: String,
                required: true
            },
            answer: {
                type: String,
                required: true
            },
            isCorrect: {
                type: Boolean,
                default: null
            }
        }
    ],
    meetingDetails: {
        location: {
            type: String,
            trim: true
        },
        dateTime: {
            type: Date
        },
        notes: {
            type: String,
            trim: true
        }
    },
    adminNotes: {
        type: String,
        trim: true
    },
    reviewedBy: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User'
    },
    reviewedAt: {
        type: Date
    },
    completedAt: {
        type: Date
    },
    rating: {
        claimantRating: {
            score: {
                type: Number,
                min: 1,
                max: 5
            },
            comment: {
                type: String,
                trim: true
            }
        },
        ownerRating: {
            score: {
                type: Number,
                min: 1,
                max: 5
            },
            comment: {
                type: String,
                trim: true
            }
        }
    }
}, {
    timestamps: true
});
// Indexes
ClaimSchema.index({
    item: 1,
    claimant: 1
}, {
    unique: true
});
ClaimSchema.index({
    status: 1,
    createdAt: -1
});
ClaimSchema.index({
    claimant: 1
});
ClaimSchema.index({
    itemOwner: 1
});
// Virtual for claim age
ClaimSchema.virtual('age').get(function() {
    return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days
});
// Method to approve claim
ClaimSchema.methods.approve = function(reviewerId, notes = '') {
    this.status = 'approved';
    this.reviewedBy = reviewerId;
    this.reviewedAt = new Date();
    this.adminNotes = notes;
    return this.save();
};
// Method to reject claim
ClaimSchema.methods.reject = function(reviewerId, notes = '') {
    this.status = 'rejected';
    this.reviewedBy = reviewerId;
    this.reviewedAt = new Date();
    this.adminNotes = notes;
    return this.save();
};
// Method to complete claim
ClaimSchema.methods.complete = function() {
    this.status = 'completed';
    this.completedAt = new Date();
    return this.save();
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Claim || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Claim', ClaimSchema);
}}),
"[project]/src/models/Item.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const ItemSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    title: {
        type: String,
        required: [
            true,
            'Item title is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Title cannot be more than 100 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot be more than 500 characters'
        ]
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: [
            'Electronics',
            'Books & Stationery',
            'Clothing & Accessories',
            'ID Cards & Documents',
            'Keys',
            'Water Bottles',
            'Bags & Backpacks',
            'Sports Equipment',
            'Jewelry',
            'Other'
        ]
    },
    type: {
        type: String,
        required: [
            true,
            'Item type is required'
        ],
        enum: [
            'lost',
            'found'
        ]
    },
    status: {
        type: String,
        enum: [
            'active',
            'claimed',
            'resolved',
            'archived'
        ],
        default: 'active'
    },
    images: [
        {
            url: {
                type: String,
                required: true
            },
            publicId: {
                type: String,
                required: true
            }
        }
    ],
    location: {
        type: String,
        required: [
            true,
            'Location is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Location cannot be more than 100 characters'
        ]
    },
    dateOccurred: {
        type: Date,
        required: [
            true,
            'Date is required'
        ],
        validate: {
            validator: function(date) {
                return date <= new Date();
            },
            message: 'Date cannot be in the future'
        }
    },
    contactInfo: {
        email: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            trim: true
        },
        preferredContact: {
            type: String,
            enum: [
                'email',
                'phone',
                'chat'
            ],
            default: 'email'
        }
    },
    postedBy: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    isUrgent: {
        type: Boolean,
        default: false
    },
    reward: {
        offered: {
            type: Boolean,
            default: false
        },
        amount: {
            type: Number,
            min: 0
        },
        description: {
            type: String,
            trim: true
        }
    },
    views: {
        type: Number,
        default: 0
    },
    claimsCount: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});
// Indexes for better query performance
ItemSchema.index({
    type: 1,
    status: 1,
    createdAt: -1
});
ItemSchema.index({
    category: 1,
    type: 1
});
ItemSchema.index({
    location: 1,
    type: 1
});
ItemSchema.index({
    postedBy: 1
});
ItemSchema.index({
    tags: 1
});
ItemSchema.index({
    'contactInfo.email': 1
});
// Virtual for age of the post
ItemSchema.virtual('age').get(function() {
    return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days
});
// Method to increment views
ItemSchema.methods.incrementViews = function() {
    this.views += 1;
    return this.save();
};
// Method to increment claims count
ItemSchema.methods.incrementClaims = function() {
    this.claimsCount += 1;
    return this.save();
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Item || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Item', ItemSchema);
}}),
"[project]/src/models/User.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    name: {
        type: String,
        required: [
            true,
            'Name is required'
        ],
        trim: true,
        maxlength: [
            50,
            'Name cannot be more than 50 characters'
        ]
    },
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        lowercase: true,
        validate: {
            validator: function(email) {
                // Validate UMT email domain
                return email.endsWith('@umt.edu.pk') || email.endsWith('@student.umt.edu.pk');
            },
            message: 'Please use a valid UMT email address'
        }
    },
    password: {
        type: String,
        required: [
            true,
            'Password is required'
        ],
        minlength: [
            6,
            'Password must be at least 6 characters'
        ]
    },
    role: {
        type: String,
        enum: [
            'student',
            'admin'
        ],
        default: 'student'
    },
    studentId: {
        type: String,
        sparse: true,
        unique: true
    },
    phone: {
        type: String,
        trim: true
    },
    avatar: {
        type: String,
        default: null
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    verificationToken: {
        type: String,
        default: null
    },
    resetPasswordToken: {
        type: String,
        default: null
    },
    resetPasswordExpires: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});
// Hash password before saving
UserSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    try {
        const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(12);
        this.password = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});
// Compare password method
UserSchema.methods.comparePassword = async function(candidatePassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(candidatePassword, this.password);
};
// Remove password from JSON output
UserSchema.methods.toJSON = function() {
    const userObject = this.toObject();
    delete userObject.password;
    delete userObject.verificationToken;
    delete userObject.resetPasswordToken;
    delete userObject.resetPasswordExpires;
    return userObject;
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions),
    "canAccess": (()=>canAccess),
    "generateResetToken": (()=>generateResetToken),
    "generateVerificationToken": (()=>generateVerificationToken),
    "isAdmin": (()=>isAdmin),
    "isOwner": (()=>isOwner),
    "requireAuth": (()=>requireAuth),
    "validateUMTEmail": (()=>validateUMTEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.js [app-route] (ecmascript)");
;
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    throw new Error('Email and password are required');
                }
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                    email: credentials.email
                });
                if (!user) {
                    throw new Error('No user found with this email');
                }
                const isPasswordValid = await user.comparePassword(credentials.password);
                if (!isPasswordValid) {
                    throw new Error('Invalid password');
                }
                if (!user.isVerified) {
                    throw new Error('Please verify your email before logging in');
                }
                return {
                    id: user._id.toString(),
                    email: user.email,
                    name: user.name,
                    role: user.role,
                    studentId: user.studentId,
                    avatar: user.avatar
                };
            }
        })
    ],
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    jwt: {
        maxAge: 30 * 24 * 60 * 60
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.studentId = user.studentId;
                token.avatar = user.avatar;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.studentId = token.studentId;
                session.user.avatar = token.avatar;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signUp: '/auth/signup',
        error: '/auth/error'
    },
    secret: process.env.NEXTAUTH_SECRET
};
const validateUMTEmail = (email)=>{
    const umtDomains = [
        '@umt.edu.pk',
        '@student.umt.edu.pk'
    ];
    return umtDomains.some((domain)=>email.endsWith(domain));
};
const generateVerificationToken = ()=>{
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};
const generateResetToken = ()=>{
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};
const requireAuth = (handler, requiredRole = null)=>{
    return async (req, res)=>{
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(req, res, authOptions);
        if (!session) {
            return res.status(401).json({
                error: 'Authentication required'
            });
        }
        if (requiredRole && session.user.role !== requiredRole) {
            return res.status(403).json({
                error: 'Insufficient permissions'
            });
        }
        req.user = session.user;
        return handler(req, res);
    };
};
const isAdmin = (user)=>{
    return user?.role === 'admin';
};
const isOwner = (user, resourceUserId)=>{
    return user?.id === resourceUserId.toString();
};
const canAccess = (user, resourceUserId, requiredRole = null)=>{
    if (requiredRole && user?.role !== requiredRole) {
        return false;
    }
    return isAdmin(user) || isOwner(user, resourceUserId);
};
}}),
"[project]/src/models/Notification.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const NotificationSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema({
    recipient: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    sender: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'User'
    },
    type: {
        type: String,
        required: true,
        enum: [
            'new_claim',
            'claim_approved',
            'claim_rejected',
            'claim_completed',
            'item_matched',
            'message_received',
            'item_expired',
            'system_announcement'
        ]
    },
    title: {
        type: String,
        required: true,
        trim: true,
        maxlength: [
            100,
            'Title cannot be more than 100 characters'
        ]
    },
    message: {
        type: String,
        required: true,
        trim: true,
        maxlength: [
            300,
            'Message cannot be more than 300 characters'
        ]
    },
    relatedItem: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'Item'
    },
    relatedClaim: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
        ref: 'Claim'
    },
    actionUrl: {
        type: String,
        trim: true
    },
    isRead: {
        type: Boolean,
        default: false
    },
    readAt: {
        type: Date
    },
    priority: {
        type: String,
        enum: [
            'low',
            'medium',
            'high',
            'urgent'
        ],
        default: 'medium'
    },
    expiresAt: {
        type: Date
    }
}, {
    timestamps: true
});
// Indexes
NotificationSchema.index({
    recipient: 1,
    isRead: 1,
    createdAt: -1
});
NotificationSchema.index({
    type: 1
});
NotificationSchema.index({
    expiresAt: 1
}, {
    expireAfterSeconds: 0
});
// Virtual for notification age
NotificationSchema.virtual('age').get(function() {
    return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days
});
// Method to mark as read
NotificationSchema.methods.markAsRead = function() {
    this.isRead = true;
    this.readAt = new Date();
    return this.save();
};
// Static method to create notification
NotificationSchema.statics.createNotification = async function(data) {
    const notification = new this(data);
    await notification.save();
    // Here you could emit socket event for real-time notifications
    // socketIO.to(data.recipient.toString()).emit('new_notification', notification);
    return notification;
};
// Static method to mark all as read for a user
NotificationSchema.statics.markAllAsRead = async function(userId) {
    return this.updateMany({
        recipient: userId,
        isRead: false
    }, {
        isRead: true,
        readAt: new Date()
    });
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Notification || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Notification', NotificationSchema);
}}),
"[project]/src/lib/notifier.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanupExpiredNotifications": (()=>cleanupExpiredNotifications),
    "createBulkNotifications": (()=>createBulkNotifications),
    "createNotification": (()=>createNotification),
    "getUnreadNotificationCount": (()=>getUnreadNotificationCount),
    "getUserNotifications": (()=>getUserNotifications),
    "markAllNotificationsAsRead": (()=>markAllNotificationsAsRead),
    "markNotificationAsRead": (()=>markNotificationAsRead)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Notification.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.js [app-route] (ecmascript)");
;
;
// Notification types and their templates
const NOTIFICATION_TEMPLATES = {
    new_claim: {
        title: 'New Claim Request',
        getMessage: (data)=>`${data.claimantName} has submitted a claim for your ${data.itemType} item "${data.itemTitle}"`
    },
    claim_approved: {
        title: 'Claim Approved',
        getMessage: (data)=>`Your claim for "${data.itemTitle}" has been approved. You can now contact the owner.`
    },
    claim_rejected: {
        title: 'Claim Rejected',
        getMessage: (data)=>`Your claim for "${data.itemTitle}" has been rejected. ${data.reason || ''}`
    },
    claim_completed: {
        title: 'Claim Completed',
        getMessage: (data)=>`The claim for "${data.itemTitle}" has been marked as completed.`
    },
    item_matched: {
        title: 'Potential Match Found',
        getMessage: (data)=>`We found a potential match for your ${data.itemType} item. Check it out!`
    },
    message_received: {
        title: 'New Message',
        getMessage: (data)=>`You have a new message from ${data.senderName} about "${data.itemTitle}"`
    },
    item_expired: {
        title: 'Item Listing Expired',
        getMessage: (data)=>`Your ${data.itemType} item "${data.itemTitle}" listing has expired after 30 days.`
    },
    system_announcement: {
        title: 'System Announcement',
        getMessage: (data)=>data.message
    }
};
const createNotification = async (type, recipientId, data, options = {})=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const template = NOTIFICATION_TEMPLATES[type];
        if (!template) {
            throw new Error(`Unknown notification type: ${type}`);
        }
        const notification = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].createNotification({
            recipient: recipientId,
            sender: data.senderId || null,
            type,
            title: options.title || template.title,
            message: options.message || template.getMessage(data),
            relatedItem: data.itemId || null,
            relatedClaim: data.claimId || null,
            actionUrl: options.actionUrl || null,
            priority: options.priority || 'medium',
            expiresAt: options.expiresAt || null
        });
        // Here you would emit socket event for real-time notifications
        // if (global.io) {
        //   global.io.to(recipientId.toString()).emit('new_notification', notification);
        // }
        return notification;
    } catch (error) {
        console.error('Error creating notification:', error);
        throw error;
    }
};
const createBulkNotifications = async (notifications)=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const notificationPromises = notifications.map(({ type, recipientId, data, options })=>createNotification(type, recipientId, data, options));
        const results = await Promise.all(notificationPromises);
        return results;
    } catch (error) {
        console.error('Error creating bulk notifications:', error);
        throw error;
    }
};
const getUserNotifications = async (userId, options = {})=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { page = 1, limit = 20, unreadOnly = false, type = null } = options;
        const query = {
            recipient: userId
        };
        if (unreadOnly) {
            query.isRead = false;
        }
        if (type) {
            query.type = type;
        }
        const notifications = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).populate('sender', 'name avatar').populate('relatedItem', 'title type').populate('relatedClaim', 'status').sort({
            createdAt: -1
        }).limit(limit * 1).skip((page - 1) * limit);
        const total = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query);
        return {
            notifications,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    } catch (error) {
        console.error('Error getting user notifications:', error);
        throw error;
    }
};
const markNotificationAsRead = async (notificationId, userId)=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const notification = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            _id: notificationId,
            recipient: userId
        });
        if (!notification) {
            throw new Error('Notification not found');
        }
        await notification.markAsRead();
        return notification;
    } catch (error) {
        console.error('Error marking notification as read:', error);
        throw error;
    }
};
const markAllNotificationsAsRead = async (userId)=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].markAllAsRead(userId);
        return result;
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
    }
};
const getUnreadNotificationCount = async (userId)=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const count = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments({
            recipient: userId,
            isRead: false
        });
        return count;
    } catch (error) {
        console.error('Error getting unread notification count:', error);
        throw error;
    }
};
const cleanupExpiredNotifications = async ()=>{
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Notification$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].deleteMany({
            expiresAt: {
                $lt: new Date()
            }
        });
        console.log(`Cleaned up ${result.deletedCount} expired notifications`);
        return result;
    } catch (error) {
        console.error('Error cleaning up expired notifications:', error);
        throw error;
    }
};
}}),
"[project]/src/app/api/claims/route.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Claim$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Claim.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Item$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Item.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notifier$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/notifier.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { searchParams } = new URL(request.url);
        const type = searchParams.get('type'); // 'sent' or 'received'
        const status = searchParams.get('status');
        const page = parseInt(searchParams.get('page')) || 1;
        const limit = parseInt(searchParams.get('limit')) || 10;
        let query = {};
        if (type === 'sent') {
            query.claimant = session.user.id;
        } else if (type === 'received') {
            query.itemOwner = session.user.id;
        } else {
            // Get both sent and received claims
            query.$or = [
                {
                    claimant: session.user.id
                },
                {
                    itemOwner: session.user.id
                }
            ];
        }
        if (status) {
            query.status = status;
        }
        const claims = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Claim$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).populate('item', 'title type category location images').populate('claimant', 'name email avatar').populate('itemOwner', 'name email avatar').sort({
            createdAt: -1
        }).limit(limit).skip((page - 1) * limit).lean();
        const total = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Claim$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            claims,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Claims fetch error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch claims'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { itemId, message, verificationAnswers, proofImages } = await request.json();
        // Validate required fields
        if (!itemId || !message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Item ID and message are required'
            }, {
                status: 400
            });
        }
        // Get the item
        const item = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Item$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(itemId).populate('postedBy');
        if (!item) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Item not found'
            }, {
                status: 404
            });
        }
        // Check if user is trying to claim their own item
        if (item.postedBy._id.toString() === session.user.id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'You cannot claim your own item'
            }, {
                status: 400
            });
        }
        // Check if user has already claimed this item
        const existingClaim = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Claim$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            item: itemId,
            claimant: session.user.id
        });
        if (existingClaim) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'You have already submitted a claim for this item'
            }, {
                status: 400
            });
        }
        // Create new claim
        const claim = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Claim$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            item: itemId,
            claimant: session.user.id,
            itemOwner: item.postedBy._id,
            message,
            verificationQuestions: verificationAnswers || [],
            proofImages: proofImages || []
        });
        await claim.save();
        // Increment claims count on item
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Item$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(itemId, {
            $inc: {
                claimsCount: 1
            }
        });
        // Create notification for item owner
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notifier$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createNotification"])('new_claim', item.postedBy._id, {
            claimantName: session.user.name,
            itemTitle: item.title,
            itemType: item.type,
            itemId: itemId,
            claimId: claim._id
        }, {
            actionUrl: `/dashboard/claims/${claim._id}`
        });
        // Populate the claim for response
        await claim.populate([
            {
                path: 'item',
                select: 'title type category location'
            },
            {
                path: 'claimant',
                select: 'name email avatar'
            },
            {
                path: 'itemOwner',
                select: 'name email avatar'
            }
        ]);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Claim submitted successfully',
            claim
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Claim creation error:', error);
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map((err)=>err.message);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: errors.join(', ')
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create claim'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b8305af8._.js.map