{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.js"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware';\nimport { NextResponse } from 'next/server';\n\nexport default withAuth(\n  function middleware(req) {\n    const { pathname } = req.nextUrl;\n    const token = req.nextauth.token;\n\n    // Admin routes protection\n    if (pathname.startsWith('/admin')) {\n      if (!token || token.role !== 'admin') {\n        return NextResponse.redirect(new URL('/auth/signin', req.url));\n      }\n    }\n\n    // Protected routes that require authentication\n    const protectedRoutes = ['/dashboard', '/items/post', '/profile', '/settings', '/chat'];\n    \n    if (protectedRoutes.some(route => pathname.startsWith(route))) {\n      if (!token) {\n        return NextResponse.redirect(new URL('/auth/signin', req.url));\n      }\n    }\n\n    // Redirect authenticated users away from auth pages\n    if (pathname.startsWith('/auth/') && token) {\n      if (token.role === 'admin') {\n        return NextResponse.redirect(new URL('/admin', req.url));\n      }\n      return NextResponse.redirect(new URL('/dashboard', req.url));\n    }\n\n    return NextResponse.next();\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Allow access to public routes\n        const publicRoutes = ['/', '/auth/signin', '/auth/signup', '/items/lost', '/items/found', '/about', '/contact'];\n        const { pathname } = req.nextUrl;\n        \n        if (publicRoutes.includes(pathname) || pathname.startsWith('/api/auth/')) {\n          return true;\n        }\n\n        // For all other routes, require authentication\n        return !!token;\n      },\n    },\n  }\n);\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAEhC,0BAA0B;IAC1B,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,IAAI,CAAC,SAAS,MAAM,IAAI,KAAK,SAAS;YACpC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;QAC9D;IACF;IAEA,+CAA+C;IAC/C,MAAM,kBAAkB;QAAC;QAAc;QAAe;QAAY;QAAa;KAAQ;IAEvF,IAAI,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QAC7D,IAAI,CAAC,OAAO;YACV,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;QAC9D;IACF;IAEA,oDAAoD;IACpD,IAAI,SAAS,UAAU,CAAC,aAAa,OAAO;QAC1C,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QACxD;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,gCAAgC;YAChC,MAAM,eAAe;gBAAC;gBAAK;gBAAgB;gBAAgB;gBAAe;gBAAgB;gBAAU;aAAW;YAC/G,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,IAAI,aAAa,QAAQ,CAAC,aAAa,SAAS,UAAU,CAAC,eAAe;gBACxE,OAAO;YACT;YAEA,+CAA+C;YAC/C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}