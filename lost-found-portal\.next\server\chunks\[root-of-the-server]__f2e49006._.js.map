{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/db.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/Item.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst ItemSchema = new mongoose.Schema({\n  title: {\n    type: String,\n    required: [true, 'Item title is required'],\n    trim: true,\n    maxlength: [100, 'Title cannot be more than 100 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot be more than 500 characters']\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: [\n      'Electronics',\n      'Books & Stationery',\n      'Clothing & Accessories',\n      'ID Cards & Documents',\n      'Keys',\n      'Water Bottles',\n      'Bags & Backpacks',\n      'Sports Equipment',\n      'Jewelry',\n      'Other'\n    ]\n  },\n  type: {\n    type: String,\n    required: [true, 'Item type is required'],\n    enum: ['lost', 'found']\n  },\n  status: {\n    type: String,\n    enum: ['active', 'claimed', 'resolved', 'archived'],\n    default: 'active'\n  },\n  images: [{\n    url: {\n      type: String,\n      required: true\n    },\n    publicId: {\n      type: String\n    },\n    name: {\n      type: String\n    },\n    type: {\n      type: String\n    },\n    size: {\n      type: Number\n    }\n  }],\n  location: {\n    type: String,\n    required: [true, 'Location is required'],\n    trim: true,\n    maxlength: [100, 'Location cannot be more than 100 characters']\n  },\n  dateOccurred: {\n    type: Date,\n    required: [true, 'Date is required'],\n    validate: {\n      validator: function(date) {\n        return date <= new Date();\n      },\n      message: 'Date cannot be in the future'\n    }\n  },\n  contactInfo: {\n    email: {\n      type: String,\n      required: true\n    },\n    phone: {\n      type: String,\n      trim: true\n    },\n    preferredContact: {\n      type: String,\n      enum: ['email', 'phone', 'chat'],\n      default: 'email'\n    }\n  },\n  postedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  isUrgent: {\n    type: Boolean,\n    default: false\n  },\n  reward: {\n    offered: {\n      type: Boolean,\n      default: false\n    },\n    amount: {\n      type: Number,\n      min: 0\n    },\n    description: {\n      type: String,\n      trim: true\n    }\n  },\n  views: {\n    type: Number,\n    default: 0\n  },\n  claimsCount: {\n    type: Number,\n    default: 0\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes for better query performance\nItemSchema.index({ type: 1, status: 1, createdAt: -1 });\nItemSchema.index({ category: 1, type: 1 });\nItemSchema.index({ location: 1, type: 1 });\nItemSchema.index({ postedBy: 1 });\nItemSchema.index({ tags: 1 });\nItemSchema.index({ 'contactInfo.email': 1 });\n\n// Virtual for age of the post\nItemSchema.virtual('age').get(function() {\n  return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days\n});\n\n// Method to increment views\nItemSchema.methods.incrementViews = function() {\n  this.views += 1;\n  return this.save();\n};\n\n// Method to increment claims count\nItemSchema.methods.incrementClaims = function() {\n  this.claimsCount += 1;\n  return this.save();\n};\n\nexport default mongoose.models.Item || mongoose.model('Item', ItemSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAK;SAAiD;IACpE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;YAAC;YAAQ;SAAQ;IACzB;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAU;YAAW;YAAY;SAAW;QACnD,SAAS;IACX;IACA,QAAQ;QAAC;YACP,KAAK;gBACH,MAAM;gBACN,UAAU;YACZ;YACA,UAAU;gBACR,MAAM;YACR;YACA,MAAM;gBACJ,MAAM;YACR;YACA,MAAM;gBACJ,MAAM;YACR;YACA,MAAM;gBACJ,MAAM;YACR;QACF;KAAE;IACF,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;QACN,WAAW;YAAC;YAAK;SAA8C;IACjE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,UAAU;YACR,WAAW,SAAS,IAAI;gBACtB,OAAO,QAAQ,IAAI;YACrB;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,OAAO;YACL,MAAM;YACN,UAAU;QACZ;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,kBAAkB;YAChB,MAAM;YACN,MAAM;gBAAC;gBAAS;gBAAS;aAAO;YAChC,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,SAAS;YACP,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,KAAK;QACP;QACA,aAAa;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;IAAG,WAAW,CAAC;AAAE;AACrD,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,MAAM;AAAE;AACxC,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,MAAM;AAAE;AACxC,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,qBAAqB;AAAE;AAE1C,8BAA8B;AAC9B,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC;IAC5B,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,IAAI,OAAO;AACnF;AAEA,4BAA4B;AAC5B,WAAW,OAAO,CAAC,cAAc,GAAG;IAClC,IAAI,CAAC,KAAK,IAAI;IACd,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,eAAe,GAAG;IACnC,IAAI,CAAC,WAAW,IAAI;IACpB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    validate: {\n      validator: function(email) {\n        // Validate UMT email domain\n        return email.endsWith('@umt.edu.pk') || email.endsWith('@student.umt.edu.pk');\n      },\n      message: 'Please use a valid UMT email address'\n    }\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters']\n  },\n  role: {\n    type: String,\n    enum: ['student', 'admin'],\n    default: 'student'\n  },\n  studentId: {\n    type: String,\n    sparse: true,\n    unique: true\n  },\n  phone: {\n    type: String,\n    trim: true\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  verificationToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordExpires: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Remove password from JSON output\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.verificationToken;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpires;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,UAAU;YACR,WAAW,SAAS,KAAK;gBACvB,4BAA4B;gBAC5B,OAAO,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC;YACzD;YACA,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;IAC1D;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAW;SAAQ;QAC1B,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,mBAAmB;QACjB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,sBAAsB;QACpB,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,iBAAiB;IACnC,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,oBAAoB;IACtC,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/auth.js"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { getServerSession } from 'next-auth';\nimport connectDB from './db';\nimport User from '@/models/User';\n\nexport const authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error('Email and password are required');\n        }\n\n        await connectDB();\n\n        const user = await User.findOne({ email: credentials.email });\n\n        if (!user) {\n          throw new Error('No user found with this email');\n        }\n\n        const isPasswordValid = await user.comparePassword(credentials.password);\n\n        if (!isPasswordValid) {\n          throw new Error('Invalid password');\n        }\n\n        if (!user.isVerified) {\n          throw new Error('Please verify your email before logging in');\n        }\n\n        return {\n          id: user._id.toString(),\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          studentId: user.studentId,\n          avatar: user.avatar\n        };\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.studentId = user.studentId;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub;\n        session.user.role = token.role;\n        session.user.studentId = token.studentId;\n        session.user.avatar = token.avatar;\n      }\n      return session;\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error'\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n// Utility functions for authentication\nexport const validateUMTEmail = (email) => {\n  const umtDomains = ['@umt.edu.pk', '@student.umt.edu.pk'];\n  return umtDomains.some(domain => email.endsWith(domain));\n};\n\nexport const generateVerificationToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\nexport const generateResetToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\n// Role-based access control\nexport const requireAuth = (handler, requiredRole = null) => {\n  return async (req, res) => {\n    const session = await getServerSession(req, res, authOptions);\n\n    if (!session) {\n      return res.status(401).json({ error: 'Authentication required' });\n    }\n\n    if (requiredRole && session.user.role !== requiredRole) {\n      return res.status(403).json({ error: 'Insufficient permissions' });\n    }\n\n    req.user = session.user;\n    return handler(req, res);\n  };\n};\n\n// Check if user is admin\nexport const isAdmin = (user) => {\n  return user?.role === 'admin';\n};\n\n// Check if user owns the resource\nexport const isOwner = (user, resourceUserId) => {\n  return user?.id === resourceUserId.toString();\n};\n\n// Check if user can access resource\nexport const canAccess = (user, resourceUserId, requiredRole = null) => {\n  if (requiredRole && user?.role !== requiredRole) {\n    return false;\n  }\n\n  return isAdmin(user) || isOwner(user, resourceUserId);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;gBAEd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,YAAY,KAAK;gBAAC;gBAE3D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,YAAY,QAAQ;gBAEvE,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;oBACrB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;oBACzB,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,aAAa;QAAC;QAAe;KAAsB;IACzD,OAAO,WAAW,IAAI,CAAC,CAAA,SAAU,MAAM,QAAQ,CAAC;AAClD;AAEO,MAAM,4BAA4B;IACvC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,MAAM,qBAAqB;IAChC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,MAAM,cAAc,CAAC,SAAS,eAAe,IAAI;IACtD,OAAO,OAAO,KAAK;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,KAAK;QAEjD,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA0B;QACjE;QAEA,IAAI,gBAAgB,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAc;YACtD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA2B;QAClE;QAEA,IAAI,IAAI,GAAG,QAAQ,IAAI;QACvB,OAAO,QAAQ,KAAK;IACtB;AACF;AAGO,MAAM,UAAU,CAAC;IACtB,OAAO,MAAM,SAAS;AACxB;AAGO,MAAM,UAAU,CAAC,MAAM;IAC5B,OAAO,MAAM,OAAO,eAAe,QAAQ;AAC7C;AAGO,MAAM,YAAY,CAAC,MAAM,gBAAgB,eAAe,IAAI;IACjE,IAAI,gBAAgB,MAAM,SAAS,cAAc;QAC/C,OAAO;IACT;IAEA,OAAO,QAAQ,SAAS,QAAQ,MAAM;AACxC", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/api/items/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport connectDB from '@/lib/db';\nimport Item from '@/models/Item';\nimport { authOptions } from '@/lib/auth';\n\nexport async function GET(request) {\n  try {\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const type = searchParams.get('type'); // 'lost' or 'found'\n    const category = searchParams.get('category');\n    const location = searchParams.get('location');\n    const search = searchParams.get('search');\n    const postedBy = searchParams.get('postedBy');\n    const dateRange = searchParams.get('dateRange');\n    const page = parseInt(searchParams.get('page')) || 1;\n    const limit = parseInt(searchParams.get('limit')) || 12;\n    const sortBy = searchParams.get('sortBy') || 'newest';\n    const sortOrder = searchParams.get('sortOrder') || 'desc';\n\n    // Build query\n    const query = {};\n\n    // Only filter by active status if not filtering by user\n    if (!postedBy) {\n      query.status = 'active';\n    }\n\n    if (type && type !== 'all') {\n      query.type = type;\n    }\n\n    if (category && category !== 'all') {\n      query.category = category;\n    }\n\n    if (location && location !== 'all') {\n      query.location = { $regex: location, $options: 'i' };\n    }\n\n    if (postedBy) {\n      query.postedBy = postedBy;\n    }\n\n    // Date range filtering\n    if (dateRange && dateRange !== 'all') {\n      const now = new Date();\n      let startDate;\n\n      switch (dateRange) {\n        case 'today':\n          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n          break;\n        case 'week':\n          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n          break;\n        case 'month':\n          startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n          break;\n        default:\n          startDate = null;\n      }\n\n      if (startDate) {\n        query.createdAt = { $gte: startDate };\n      }\n    }\n\n    if (search) {\n      query.$or = [\n        { title: { $regex: search, $options: 'i' } },\n        { description: { $regex: search, $options: 'i' } },\n        { tags: { $in: [new RegExp(search, 'i')] } }\n      ];\n    }\n\n    // Build sort object\n    const sort = {};\n\n    switch (sortBy) {\n      case 'newest':\n        sort.createdAt = -1;\n        break;\n      case 'oldest':\n        sort.createdAt = 1;\n        break;\n      case 'title':\n        sort.title = 1;\n        break;\n      case 'views':\n        sort.views = -1;\n        break;\n      default:\n        sort.createdAt = sortOrder === 'desc' ? -1 : 1;\n    }\n\n    // Execute query with pagination\n    const items = await Item.find(query)\n      .populate('postedBy', 'name avatar')\n      .sort(sort)\n      .limit(limit)\n      .skip((page - 1) * limit)\n      .lean();\n\n    // Get total count for pagination\n    const total = await Item.countDocuments(query);\n\n    return NextResponse.json({\n      items,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    });\n\n  } catch (error) {\n    console.error('Items fetch error:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch items' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    if (!session) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    await connectDB();\n\n    const {\n      title,\n      description,\n      category,\n      type,\n      images,\n      location,\n      dateOccurred,\n      contactInfo,\n      tags,\n      isUrgent,\n      reward\n    } = await request.json();\n\n    // Validate required fields\n    if (!title || !description || !category || !type || !location || !dateOccurred) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      );\n    }\n\n    // Validate type\n    if (!['lost', 'found'].includes(type)) {\n      return NextResponse.json(\n        { error: 'Type must be either \"lost\" or \"found\"' },\n        { status: 400 }\n      );\n    }\n\n    // Process images (convert base64 to storable format)\n    const processedImages = [];\n    if (images && images.length > 0) {\n      for (const image of images) {\n        // In production, you'd upload to Cloudinary here\n        // For now, we'll store the base64 data directly\n        processedImages.push({\n          url: image.data, // base64 data URL\n          name: image.name,\n          type: image.type,\n          size: image.size,\n          publicId: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        });\n      }\n    }\n\n    // Create new item\n    const item = new Item({\n      title,\n      description,\n      category,\n      type,\n      images: processedImages,\n      location,\n      dateOccurred: new Date(dateOccurred),\n      contactInfo: {\n        email: contactInfo?.email || session.user.email,\n        phone: contactInfo?.phone || '',\n        preferredContact: contactInfo?.preferredContact || 'email'\n      },\n      postedBy: session.user.id,\n      tags: tags || [],\n      isUrgent: isUrgent || false,\n      reward: reward || { offered: false }\n    });\n\n    await item.save();\n\n    // Populate the postedBy field for response\n    await item.populate('postedBy', 'name avatar');\n\n    return NextResponse.json(\n      {\n        message: 'Item posted successfully',\n        item\n      },\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Item creation error:', error);\n\n    if (error.name === 'ValidationError') {\n      const errors = Object.values(error.errors).map(err => err.message);\n      return NextResponse.json(\n        { error: errors.join(', ') },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { error: 'Failed to create item' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC,SAAS,oBAAoB;QAC3D,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,YAAY;QACnD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,aAAa;QACrD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,cAAc;QACd,MAAM,QAAQ,CAAC;QAEf,wDAAwD;QACxD,IAAI,CAAC,UAAU;YACb,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,QAAQ,SAAS,OAAO;YAC1B,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,YAAY,aAAa,OAAO;YAClC,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,YAAY,aAAa,OAAO;YAClC,MAAM,QAAQ,GAAG;gBAAE,QAAQ;gBAAU,UAAU;YAAI;QACrD;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,uBAAuB;QACvB,IAAI,aAAa,cAAc,OAAO;YACpC,MAAM,MAAM,IAAI;YAChB,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;oBACnE;gBACF,KAAK;oBACH,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;oBACxD;gBACF,KAAK;oBACH,YAAY,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;oBACxD;gBACF;oBACE,YAAY;YAChB;YAEA,IAAI,WAAW;gBACb,MAAM,SAAS,GAAG;oBAAE,MAAM;gBAAU;YACtC;QACF;QAEA,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV;oBAAE,OAAO;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC3C;oBAAE,aAAa;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACjD;oBAAE,MAAM;wBAAE,KAAK;4BAAC,IAAI,OAAO,QAAQ;yBAAK;oBAAC;gBAAE;aAC5C;QACH;QAEA,oBAAoB;QACpB,MAAM,OAAO,CAAC;QAEd,OAAQ;YACN,KAAK;gBACH,KAAK,SAAS,GAAG,CAAC;gBAClB;YACF,KAAK;gBACH,KAAK,SAAS,GAAG;gBACjB;YACF,KAAK;gBACH,KAAK,KAAK,GAAG;gBACb;YACF,KAAK;gBACH,KAAK,KAAK,GAAG,CAAC;gBACd;YACF;gBACE,KAAK,SAAS,GAAG,cAAc,SAAS,CAAC,IAAI;QACjD;QAEA,gCAAgC;QAChC,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC,OAC3B,QAAQ,CAAC,YAAY,eACrB,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,OAClB,IAAI;QAEP,iCAAiC;QACjC,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;QAExC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,IAAI,EACJ,QAAQ,EACR,MAAM,EACP,GAAG,MAAM,QAAQ,IAAI;QAEtB,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc;YAC9E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,IAAI,CAAC;YAAC;YAAQ;SAAQ,CAAC,QAAQ,CAAC,OAAO;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qDAAqD;QACrD,MAAM,kBAAkB,EAAE;QAC1B,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;YAC/B,KAAK,MAAM,SAAS,OAAQ;gBAC1B,iDAAiD;gBACjD,gDAAgD;gBAChD,gBAAgB,IAAI,CAAC;oBACnB,KAAK,MAAM,IAAI;oBACf,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;oBAChB,UAAU,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBAC5E;YACF;QACF;QAEA,kBAAkB;QAClB,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;YACpB;YACA;YACA;YACA;YACA,QAAQ;YACR;YACA,cAAc,IAAI,KAAK;YACvB,aAAa;gBACX,OAAO,aAAa,SAAS,QAAQ,IAAI,CAAC,KAAK;gBAC/C,OAAO,aAAa,SAAS;gBAC7B,kBAAkB,aAAa,oBAAoB;YACrD;YACA,UAAU,QAAQ,IAAI,CAAC,EAAE;YACzB,MAAM,QAAQ,EAAE;YAChB,UAAU,YAAY;YACtB,QAAQ,UAAU;gBAAE,SAAS;YAAM;QACrC;QAEA,MAAM,KAAK,IAAI;QAEf,2CAA2C;QAC3C,MAAM,KAAK,QAAQ,CAAC,YAAY;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT;QACF,GACA;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,IAAI,MAAM,IAAI,KAAK,mBAAmB;YACpC,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO;YACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,OAAO,IAAI,CAAC;YAAM,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}