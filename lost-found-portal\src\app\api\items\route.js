import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import connectDB from '@/lib/db';
import Item from '@/models/Item';
import { authOptions } from '@/lib/auth';

export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'lost' or 'found'
    const category = searchParams.get('category');
    const location = searchParams.get('location');
    const search = searchParams.get('search');
    const postedBy = searchParams.get('postedBy');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 12;
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query = {};

    // Only filter by active status if not filtering by user
    if (!postedBy) {
      query.status = 'active';
    }

    if (type) {
      query.type = type;
    }

    if (category && category !== 'All Categories') {
      query.category = category;
    }

    if (location) {
      query.location = { $regex: location, $options: 'i' };
    }

    if (postedBy) {
      query.postedBy = postedBy;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const items = await Item.find(query)
      .populate('postedBy', 'name avatar')
      .sort(sort)
      .limit(limit)
      .skip((page - 1) * limit)
      .lean();

    // Get total count for pagination
    const total = await Item.countDocuments(query);

    return NextResponse.json({
      items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Items fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch items' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();

    const {
      title,
      description,
      category,
      type,
      images,
      location,
      dateOccurred,
      contactInfo,
      tags,
      isUrgent,
      reward
    } = await request.json();

    // Validate required fields
    if (!title || !description || !category || !type || !location || !dateOccurred) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate type
    if (!['lost', 'found'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be either "lost" or "found"' },
        { status: 400 }
      );
    }

    // Create new item
    const item = new Item({
      title,
      description,
      category,
      type,
      images: images || [],
      location,
      dateOccurred: new Date(dateOccurred),
      contactInfo: {
        email: contactInfo?.email || session.user.email,
        phone: contactInfo?.phone || '',
        preferredContact: contactInfo?.preferredContact || 'email'
      },
      postedBy: session.user.id,
      tags: tags || [],
      isUrgent: isUrgent || false,
      reward: reward || { offered: false }
    });

    await item.save();

    // Populate the postedBy field for response
    await item.populate('postedBy', 'name avatar');

    return NextResponse.json(
      {
        message: 'Item posted successfully',
        item
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Item creation error:', error);

    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { error: errors.join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create item' },
      { status: 500 }
    );
  }
}
