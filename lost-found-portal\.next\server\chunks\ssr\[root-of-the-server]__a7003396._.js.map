{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n  { name: 'Messages', href: '/chat/list' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n\n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n\n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto  px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex mx-auto h-16 w-[90%] items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                {/* <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span> */}\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-[85%] pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n\n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n\n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAjBA;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAY,MAAM;IAAa;CACxC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,2NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,yNAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;YAAgC,cAAW;;8BACxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;8CASrD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,8OAAC,iLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,8OAAC,iLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,8OAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,8OAAC,uLAAA,CAAA,aAAU;oDACT,IAAI,qMAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,8OAAC,iLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,8OAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,8OAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,8OAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,8OAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,8OAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,8OAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,8OAAC,iLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,8OAAC,iLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,8OAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,8OAAC,2NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,8OAAC,uLAAA,CAAA,aAAU;oDACT,IAAI,qMAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,8OAAC,iLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,8OAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,8OAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,8OAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,8OAAC,iPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,8OAAC,+KAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/profile/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport {\n  UserCircleIcon,\n  CameraIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  EyeIcon,\n  EyeSlashIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nexport default function Profile() {\n  const { data: session, update } = useSession();\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showCurrentPassword, setShowCurrentPassword] = useState(false);\n  const [showNewPassword, setShowNewPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    avatar: '',\n    bio: '',\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n\n  const [avatarPreview, setAvatarPreview] = useState('');\n\n  useEffect(() => {\n    if (session?.user) {\n      setFormData(prev => ({\n        ...prev,\n        name: session.user.name || '',\n        email: session.user.email || '',\n        phone: session.user.phone || '',\n        avatar: session.user.avatar || '',\n        bio: session.user.bio || ''\n      }));\n      setAvatarPreview(session.user.avatar || '');\n    }\n  }, [session]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    setError('');\n    setSuccess('');\n  };\n\n  const handleAvatarUpload = async (e) => {\n    const file = e.target.files[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      setError('Avatar image must be less than 5MB');\n      return;\n    }\n\n    if (!file.type.startsWith('image/')) {\n      setError('Please select a valid image file');\n      return;\n    }\n\n    try {\n      const base64 = await convertToBase64(file);\n      setFormData(prev => ({ ...prev, avatar: base64 }));\n      setAvatarPreview(base64);\n    } catch (error) {\n      setError('Failed to process image');\n    }\n  };\n\n  const convertToBase64 = (file) => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = reject;\n      reader.readAsDataURL(file);\n    });\n  };\n\n  const validateForm = () => {\n    if (!formData.name.trim()) {\n      setError('Name is required');\n      return false;\n    }\n\n    if (formData.newPassword) {\n      if (!formData.currentPassword) {\n        setError('Current password is required to change password');\n        return false;\n      }\n\n      if (formData.newPassword.length < 6) {\n        setError('New password must be at least 6 characters long');\n        return false;\n      }\n\n      if (formData.newPassword !== formData.confirmPassword) {\n        setError('New passwords do not match');\n        return false;\n      }\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setSaving(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const updateData = {\n        name: formData.name,\n        phone: formData.phone,\n        bio: formData.bio,\n        avatar: formData.avatar\n      };\n\n      if (formData.newPassword) {\n        updateData.currentPassword = formData.currentPassword;\n        updateData.newPassword = formData.newPassword;\n      }\n\n      const response = await fetch('/api/user/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setError(data.error || 'Failed to update profile');\n      } else {\n        setSuccess('Profile updated successfully!');\n        \n        // Update session data\n        await update({\n          ...session,\n          user: {\n            ...session.user,\n            name: formData.name,\n            phone: formData.phone,\n            bio: formData.bio,\n            avatar: formData.avatar\n          }\n        });\n\n        // Clear password fields\n        setFormData(prev => ({\n          ...prev,\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        }));\n      }\n    } catch (error) {\n      setError('An unexpected error occurred');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (!session) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Access Denied</h1>\n            <p className=\"text-gray-600 mb-8\">Please log in to view your profile.</p>\n            <button onClick={() => router.push('/auth/signin')} className=\"btn-primary\">\n              Sign In\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Profile Settings</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Manage your account information and preferences\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"flex\">\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                <div className=\"ml-3\">\n                  <div className=\"text-sm text-red-700\">{error}</div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {success && (\n            <div className=\"rounded-md bg-green-50 p-4\">\n              <div className=\"flex\">\n                <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                <div className=\"ml-3\">\n                  <div className=\"text-sm text-green-700\">{success}</div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Profile Picture */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Profile Picture</h3>\n            </div>\n            <div className=\"card-body\">\n              <div className=\"flex items-center space-x-6\">\n                <div className=\"relative\">\n                  {avatarPreview ? (\n                    <img\n                      src={avatarPreview}\n                      alt=\"Profile\"\n                      className=\"h-24 w-24 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <UserCircleIcon className=\"h-24 w-24 text-gray-400\" />\n                  )}\n                  <label\n                    htmlFor=\"avatar\"\n                    className=\"absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 text-white cursor-pointer hover:bg-blue-700\"\n                  >\n                    <CameraIcon className=\"h-4 w-4\" />\n                    <input\n                      id=\"avatar\"\n                      type=\"file\"\n                      accept=\"image/*\"\n                      className=\"sr-only\"\n                      onChange={handleAvatarUpload}\n                    />\n                  </label>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-gray-900\">Change your avatar</h4>\n                  <p className=\"text-sm text-gray-500\">\n                    JPG, GIF or PNG. Max size of 5MB.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Basic Information */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Basic Information</h3>\n            </div>\n            <div className=\"card-body space-y-4\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Full Name *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  required\n                  className=\"form-input\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Email Address\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  disabled\n                  className=\"form-input bg-gray-50 text-gray-500 cursor-not-allowed\"\n                  value={formData.email}\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Email cannot be changed. Contact support if needed.\n                </p>\n              </div>\n\n              <div>\n                <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Phone Number\n                </label>\n                <input\n                  type=\"tel\"\n                  id=\"phone\"\n                  name=\"phone\"\n                  className=\"form-input\"\n                  placeholder=\"+92 300 1234567\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Bio\n                </label>\n                <textarea\n                  id=\"bio\"\n                  name=\"bio\"\n                  rows={3}\n                  className=\"form-textarea\"\n                  placeholder=\"Tell us a bit about yourself...\"\n                  value={formData.bio}\n                  onChange={handleInputChange}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Change Password */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Change Password</h3>\n              <p className=\"text-sm text-gray-500\">Leave blank to keep current password</p>\n            </div>\n            <div className=\"card-body space-y-4\">\n              <div>\n                <label htmlFor=\"currentPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Current Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showCurrentPassword ? 'text' : 'password'}\n                    id=\"currentPassword\"\n                    name=\"currentPassword\"\n                    className=\"form-input pr-10\"\n                    value={formData.currentPassword}\n                    onChange={handleInputChange}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}\n                  >\n                    {showCurrentPassword ? (\n                      <EyeIcon className=\"h-4 w-4 text-gray-400\" />\n                    ) : (\n                      <EyeSlashIcon className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"newPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  New Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showNewPassword ? 'text' : 'password'}\n                    id=\"newPassword\"\n                    name=\"newPassword\"\n                    className=\"form-input pr-10\"\n                    value={formData.newPassword}\n                    onChange={handleInputChange}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowNewPassword(!showNewPassword)}\n                  >\n                    {showNewPassword ? (\n                      <EyeIcon className=\"h-4 w-4 text-gray-400\" />\n                    ) : (\n                      <EyeSlashIcon className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Confirm New Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    className=\"form-input pr-10\"\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <EyeIcon className=\"h-4 w-4 text-gray-400\" />\n                    ) : (\n                      <EyeSlashIcon className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-4\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={saving}\n              className=\"btn-primary\"\n            >\n              {saving ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Saving...\n                </div>\n              ) : (\n                'Save Changes'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAbA;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;QACR,KAAK;QACL,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM;YACjB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;oBAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;oBAC7B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;oBAC7B,QAAQ,QAAQ,IAAI,CAAC,MAAM,IAAI;oBAC/B,KAAK,QAAQ,IAAI,CAAC,GAAG,IAAI;gBAC3B,CAAC;YACD,iBAAiB,QAAQ,IAAI,CAAC,MAAM,IAAI;QAC1C;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,SAAS;QACT,WAAW;IACb;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;QAEX,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,SAAS;YACT;QACF;QAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,gBAAgB;YACrC,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAO,CAAC;YAChD,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;YAC3C,OAAO,OAAO,GAAG;YACjB,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,SAAS,WAAW,EAAE;YACxB,IAAI,CAAC,SAAS,eAAe,EAAE;gBAC7B,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnC,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,SAAS,WAAW,KAAK,SAAS,eAAe,EAAE;gBACrD,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,UAAU;QACV,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,aAAa;gBACjB,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,KAAK,SAAS,GAAG;gBACjB,QAAQ,SAAS,MAAM;YACzB;YAEA,IAAI,SAAS,WAAW,EAAE;gBACxB,WAAW,eAAe,GAAG,SAAS,eAAe;gBACrD,WAAW,WAAW,GAAG,SAAS,WAAW;YAC/C;YAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;YACzB,OAAO;gBACL,WAAW;gBAEX,sBAAsB;gBACtB,MAAM,OAAO;oBACX,GAAG,OAAO;oBACV,MAAM;wBACJ,GAAG,QAAQ,IAAI;wBACf,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;wBACrB,KAAK,SAAS,GAAG;wBACjB,QAAQ,SAAS,MAAM;oBACzB;gBACF;gBAEA,wBAAwB;gBACxB,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,iBAAiB;wBACjB,aAAa;wBACb,iBAAiB;oBACnB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,qIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;gCAAiB,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;IAOtF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;4BACrC,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6OAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;sDACnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;4BAM9C,yBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;0CAOjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,8BACC,8OAAC;4DACC,KAAK;4DACL,KAAI;4DACJ,WAAU;;;;;iFAGZ,8OAAC,2NAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;sEAE5B,8OAAC;4DACC,SAAQ;4DACR,WAAU;;8EAEV,8OAAC,mNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,QAAO;oEACP,WAAU;oEACV,UAAU;;;;;;;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,OAAO,SAAS,IAAI;wDACpB,UAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,OAAO,SAAS,KAAK;;;;;;kEAEvB,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAK5C,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,KAAK;wDACrB,UAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAM,WAAU;kEAA+C;;;;;;kEAG9E,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAM;wDACN,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,GAAG;wDACnB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA+C;;;;;;kEAG1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAM,sBAAsB,SAAS;gEACrC,IAAG;gEACH,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,eAAe;gEAC/B,UAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,uBAAuB,CAAC;0EAEtC,oCACC,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC,uNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAMhC,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA+C;;;;;;kEAGtF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAM,kBAAkB,SAAS;gEACjC,IAAG;gEACH,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,WAAW;gEAC3B,UAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,mBAAmB,CAAC;0EAElC,gCACC,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC,uNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAMhC,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA+C;;;;;;kEAG1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAM,sBAAsB,SAAS;gEACrC,IAAG;gEACH,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,eAAe;gEAC/B,UAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,uBAAuB,CAAC;0EAEtC,oCACC,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC,uNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,uBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;gDAAuE;;;;;;mDAIxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}