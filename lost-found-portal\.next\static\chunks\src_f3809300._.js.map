{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n  { name: 'Messages', href: '/chat/list' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n\n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n\n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto  px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex mx-auto h-16 w-[90%] items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                {/* <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span> */}\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n\n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n\n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAY,MAAM;IAAa;CACxC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAgC,cAAW;;8BACxD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;8CASrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/items/found/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  ClockIcon,\n  MapPinIcon,\n  EyeIcon,\n  HandRaisedIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nconst categories = [\n  'All Categories',\n  'Electronics',\n  'Books & Stationery',\n  'Clothing & Accessories',\n  'ID Cards & Documents',\n  'Keys',\n  'Water Bottles',\n  'Bags & Backpacks',\n  'Sports Equipment',\n  'Jewelry',\n  'Other'\n];\n\nexport default function FoundItems() {\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    search: '',\n    category: 'All Categories',\n    location: '',\n    sortBy: 'createdAt',\n    sortOrder: 'desc'\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 12,\n    total: 0,\n    pages: 0\n  });\n\n  const fetchItems = async () => {\n    setLoading(true);\n    try {\n      const queryParams = new URLSearchParams({\n        type: 'found',\n        page: pagination.page,\n        limit: pagination.limit,\n        sortBy: filters.sortBy,\n        sortOrder: filters.sortOrder\n      });\n\n      if (filters.search) queryParams.append('search', filters.search);\n      if (filters.category !== 'All Categories') queryParams.append('category', filters.category);\n      if (filters.location) queryParams.append('location', filters.location);\n\n      const response = await fetch(`/api/items?${queryParams}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setItems(data.items);\n        setPagination(data.pagination);\n      }\n    } catch (error) {\n      console.error('Error fetching items:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchItems();\n  }, [pagination.page]);\n\n  useEffect(() => {\n    setPagination(prev => ({ ...prev, page: 1 }));\n    fetchItems();\n  }, [filters]);\n\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({ ...prev, [key]: value }));\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    fetchItems();\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) return 'Yesterday';\n    if (diffDays < 7) return `${diffDays} days ago`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;\n    return date.toLocaleDateString();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Found Items</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Items found by students and staff waiting to be claimed\n          </p>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <form onSubmit={handleSearch} className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n              {/* Search */}\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Search\n                </label>\n                <div className=\"relative\">\n                  <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search by title, description, or tags...\"\n                    className=\"form-input pl-10\"\n                    value={filters.search}\n                    onChange={(e) => handleFilterChange('search', e.target.value)}\n                  />\n                </div>\n              </div>\n\n              {/* Category */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category\n                </label>\n                <select\n                  className=\"form-select\"\n                  value={filters.category}\n                  onChange={(e) => handleFilterChange('category', e.target.value)}\n                >\n                  {categories.map(category => (\n                    <option key={category} value={category}>\n                      {category}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Location */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Found Location\n                </label>\n                <input\n                  type=\"text\"\n                  placeholder=\"e.g., Library, Cafeteria\"\n                  className=\"form-input\"\n                  value={filters.location}\n                  onChange={(e) => handleFilterChange('location', e.target.value)}\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <div className=\"flex items-center space-x-4\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Sort by:\n                </label>\n                <select\n                  className=\"form-select\"\n                  value={filters.sortBy}\n                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}\n                >\n                  <option value=\"createdAt\">Date Posted</option>\n                  <option value=\"dateOccurred\">Date Found</option>\n                  <option value=\"title\">Title</option>\n                  <option value=\"views\">Most Viewed</option>\n                </select>\n                <select\n                  className=\"form-select\"\n                  value={filters.sortOrder}\n                  onChange={(e) => handleFilterChange('sortOrder', e.target.value)}\n                >\n                  <option value=\"desc\">Newest First</option>\n                  <option value=\"asc\">Oldest First</option>\n                </select>\n              </div>\n\n              <button type=\"submit\" className=\"btn-primary\">\n                <FunnelIcon className=\"h-4 w-4 mr-2\" />\n                Apply Filters\n              </button>\n            </div>\n          </form>\n        </div>\n\n        {/* Results */}\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[...Array(6)].map((_, i) => (\n              <div key={i} className=\"card animate-pulse\">\n                <div className=\"card-body\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-4\"></div>\n                  <div className=\"h-20 bg-gray-200 rounded mb-4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : items.length > 0 ? (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {items.map((item) => (\n                <Link\n                  key={item._id}\n                  href={`/items/${item._id}`}\n                  className=\"card hover:shadow-lg transition-shadow\"\n                >\n                  <div className=\"card-body\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-1\">\n                        {item.title}\n                      </h3>\n                      <span className=\"badge badge-success\">\n                        <HandRaisedIcon className=\"h-3 w-3 mr-1\" />\n                        Found\n                      </span>\n                    </div>\n\n                    <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n                      {item.description}\n                    </p>\n\n                    {item.images && item.images.length > 0 && (\n                      <div className=\"mb-3\">\n                        <img\n                          src={item.images[0].url}\n                          alt={item.title}\n                          className=\"w-full h-32 object-cover rounded-md\"\n                        />\n                      </div>\n                    )}\n\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center text-sm text-gray-500\">\n                        <MapPinIcon className=\"h-4 w-4 mr-1\" />\n                        Found at {item.location}\n                      </div>\n\n                      <div className=\"flex items-center text-sm text-gray-500\">\n                        <ClockIcon className=\"h-4 w-4 mr-1\" />\n                        Found {formatDate(item.dateOccurred)}\n                      </div>\n\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"badge badge-info\">\n                          {item.category}\n                        </span>\n\n                        <div className=\"flex items-center text-xs text-gray-400\">\n                          <EyeIcon className=\"h-3 w-3 mr-1\" />\n                          {item.views || 0} views\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"mt-3 p-2 bg-blue-50 rounded-md\">\n                      <p className=\"text-sm text-blue-700 font-medium\">\n                        📞 Contact finder to claim\n                      </p>\n                    </div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n\n            {/* Pagination */}\n            {pagination.pages > 1 && (\n              <div className=\"mt-8 flex justify-center\">\n                <nav className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n                    disabled={pagination.page === 1}\n                    className=\"btn-secondary disabled:opacity-50\"\n                  >\n                    Previous\n                  </button>\n\n                  <span className=\"px-4 py-2 text-sm text-gray-700\">\n                    Page {pagination.page} of {pagination.pages}\n                  </span>\n\n                  <button\n                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}\n                    disabled={pagination.page === pagination.pages}\n                    className=\"btn-secondary disabled:opacity-50\"\n                  >\n                    Next\n                  </button>\n                </nav>\n              </div>\n            )}\n          </>\n        ) : (\n          <div className=\"text-center py-12\">\n            <HandRaisedIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No found items</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Try adjusting your search criteria or check back later.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAZA;;;;;AAcA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,cAAc,IAAI,gBAAgB;gBACtC,MAAM;gBACN,MAAM,WAAW,IAAI;gBACrB,OAAO,WAAW,KAAK;gBACvB,QAAQ,QAAQ,MAAM;gBACtB,WAAW,QAAQ,SAAS;YAC9B;YAEA,IAAI,QAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC/D,IAAI,QAAQ,QAAQ,KAAK,kBAAkB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAC1F,IAAI,QAAQ,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAErE,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,aAAa;YACxD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,KAAK,KAAK;gBACnB,cAAc,KAAK,UAAU;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC,WAAW,IAAI;KAAC;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAc,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAE,CAAC;;YAC3C;QACF;+BAAG;QAAC;KAAQ;IAEZ,MAAM,qBAAqB,CAAC,KAAK;QAC/B,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;QAC7C,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM;QAChC,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAChE,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,wOAAA,CAAA,sBAAmB;4DAAC,WAAU;;;;;;sEAC/B,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;4DACV,OAAO,QAAQ,MAAM;4DACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sDAMlE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,WAAU;oDACV,OAAO,QAAQ,QAAQ;oDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;8DAE7D,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;4DAAsB,OAAO;sEAC3B;2DADU;;;;;;;;;;;;;;;;sDAQnB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO,QAAQ,QAAQ;oDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAKpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAA0C;;;;;;8DAG3D,6LAAC;oDACC,WAAU;oDACV,OAAO,QAAQ,MAAM;oDACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;;sEAE5D,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAe;;;;;;sEAC7B,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;8DAExB,6LAAC;oDACC,WAAU;oDACV,OAAO,QAAQ,SAAS;oDACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;;sEAE/D,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAM;;;;;;;;;;;;;;;;;;sDAIxB,6LAAC;4CAAO,MAAK;4CAAS,WAAU;;8DAC9B,6LAAC,sNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;oBAQ9C,wBACC,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;+BALT;;;;;;;;;+BAUZ,MAAM,MAAM,GAAG,kBACjB;;0CACE,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;wCAC1B,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,8NAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAK/C,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;gDAGlB,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;wDACvB,KAAK,KAAK,KAAK;wDACf,WAAU;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;gEAC7B,KAAK,QAAQ;;;;;;;sEAGzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAiB;gEAC/B,WAAW,KAAK,YAAY;;;;;;;sEAGrC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ;;;;;;8EAGhB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,gNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAClB,KAAK,KAAK,IAAI;wEAAE;;;;;;;;;;;;;;;;;;;8DAKvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;;;;;;;;;;;;uCArDhD,KAAK,GAAG;;;;;;;;;;4BA+DlB,WAAW,KAAK,GAAG,mBAClB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;oDAAG,CAAC;4CACnF,UAAU,WAAW,IAAI,KAAK;4CAC9B,WAAU;sDACX;;;;;;sDAID,6LAAC;4CAAK,WAAU;;gDAAkC;gDAC1C,WAAW,IAAI;gDAAC;gDAAK,WAAW,KAAK;;;;;;;sDAG7C,6LAAC;4CACC,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,IAAI,GAAG;oDAAG,CAAC;4CAC5F,UAAU,WAAW,IAAI,KAAK,WAAW,KAAK;4CAC9C,WAAU;sDACX;;;;;;;;;;;;;;;;;;qDAQT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAzSwB;KAAA", "debugId": null}}]}