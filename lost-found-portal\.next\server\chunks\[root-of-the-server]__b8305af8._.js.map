{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/db.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/Claim.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst ClaimSchema = new mongoose.Schema({\n  item: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'Item',\n    required: true\n  },\n  claimant: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  itemOwner: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  status: {\n    type: String,\n    enum: ['pending', 'approved', 'rejected', 'completed', 'cancelled'],\n    default: 'pending'\n  },\n  message: {\n    type: String,\n    required: [true, 'Claim message is required'],\n    trim: true,\n    maxlength: [500, 'Message cannot be more than 500 characters']\n  },\n  proofImages: [{\n    url: {\n      type: String,\n      required: true\n    },\n    publicId: {\n      type: String,\n      required: true\n    }\n  }],\n  verificationQuestions: [{\n    question: {\n      type: String,\n      required: true\n    },\n    answer: {\n      type: String,\n      required: true\n    },\n    isCorrect: {\n      type: Boolean,\n      default: null\n    }\n  }],\n  meetingDetails: {\n    location: {\n      type: String,\n      trim: true\n    },\n    dateTime: {\n      type: Date\n    },\n    notes: {\n      type: String,\n      trim: true\n    }\n  },\n  adminNotes: {\n    type: String,\n    trim: true\n  },\n  reviewedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User'\n  },\n  reviewedAt: {\n    type: Date\n  },\n  completedAt: {\n    type: Date\n  },\n  rating: {\n    claimantRating: {\n      score: {\n        type: Number,\n        min: 1,\n        max: 5\n      },\n      comment: {\n        type: String,\n        trim: true\n      }\n    },\n    ownerRating: {\n      score: {\n        type: Number,\n        min: 1,\n        max: 5\n      },\n      comment: {\n        type: String,\n        trim: true\n      }\n    }\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes\nClaimSchema.index({ item: 1, claimant: 1 }, { unique: true });\nClaimSchema.index({ status: 1, createdAt: -1 });\nClaimSchema.index({ claimant: 1 });\nClaimSchema.index({ itemOwner: 1 });\n\n// Virtual for claim age\nClaimSchema.virtual('age').get(function() {\n  return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days\n});\n\n// Method to approve claim\nClaimSchema.methods.approve = function(reviewerId, notes = '') {\n  this.status = 'approved';\n  this.reviewedBy = reviewerId;\n  this.reviewedAt = new Date();\n  this.adminNotes = notes;\n  return this.save();\n};\n\n// Method to reject claim\nClaimSchema.methods.reject = function(reviewerId, notes = '') {\n  this.status = 'rejected';\n  this.reviewedBy = reviewerId;\n  this.reviewedAt = new Date();\n  this.adminNotes = notes;\n  return this.save();\n};\n\n// Method to complete claim\nClaimSchema.methods.complete = function() {\n  this.status = 'completed';\n  this.completedAt = new Date();\n  return this.save();\n};\n\nexport default mongoose.models.Claim || mongoose.model('Claim', ClaimSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACtC,MAAM;QACJ,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,WAAW;QACT,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAY;YAAY;YAAa;SAAY;QACnE,SAAS;IACX;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;QACN,WAAW;YAAC;YAAK;SAA6C;IAChE;IACA,aAAa;QAAC;YACZ,KAAK;gBACH,MAAM;gBACN,UAAU;YACZ;YACA,UAAU;gBACR,MAAM;gBACN,UAAU;YACZ;QACF;KAAE;IACF,uBAAuB;QAAC;YACtB,UAAU;gBACR,MAAM;gBACN,UAAU;YACZ;YACA,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,WAAW;gBACT,MAAM;gBACN,SAAS;YACX;QACF;KAAE;IACF,gBAAgB;QACd,UAAU;YACR,MAAM;YACN,MAAM;QACR;QACA,UAAU;YACR,MAAM;QACR;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;IACP;IACA,YAAY;QACV,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,gBAAgB;YACd,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;YACA,SAAS;gBACP,MAAM;gBACN,MAAM;YACR;QACF;QACA,aAAa;YACX,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,KAAK;YACP;YACA,SAAS;gBACP,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF,GAAG;IACD,YAAY;AACd;AAEA,UAAU;AACV,YAAY,KAAK,CAAC;IAAE,MAAM;IAAG,UAAU;AAAE,GAAG;IAAE,QAAQ;AAAK;AAC3D,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,UAAU;AAAE;AAChC,YAAY,KAAK,CAAC;IAAE,WAAW;AAAE;AAEjC,wBAAwB;AACxB,YAAY,OAAO,CAAC,OAAO,GAAG,CAAC;IAC7B,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,IAAI,OAAO;AACnF;AAEA,0BAA0B;AAC1B,YAAY,OAAO,CAAC,OAAO,GAAG,SAAS,UAAU,EAAE,QAAQ,EAAE;IAC3D,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC,UAAU,GAAG;IAClB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,yBAAyB;AACzB,YAAY,OAAO,CAAC,MAAM,GAAG,SAAS,UAAU,EAAE,QAAQ,EAAE;IAC1D,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC,UAAU,GAAG;IAClB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,2BAA2B;AAC3B,YAAY,OAAO,CAAC,QAAQ,GAAG;IAC7B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,WAAW,GAAG,IAAI;IACvB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,SAAS", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/Item.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst ItemSchema = new mongoose.Schema({\n  title: {\n    type: String,\n    required: [true, 'Item title is required'],\n    trim: true,\n    maxlength: [100, 'Title cannot be more than 100 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot be more than 500 characters']\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: [\n      'Electronics',\n      'Books & Stationery',\n      'Clothing & Accessories',\n      'ID Cards & Documents',\n      'Keys',\n      'Water Bottles',\n      'Bags & Backpacks',\n      'Sports Equipment',\n      'Jewelry',\n      'Other'\n    ]\n  },\n  type: {\n    type: String,\n    required: [true, 'Item type is required'],\n    enum: ['lost', 'found']\n  },\n  status: {\n    type: String,\n    enum: ['active', 'claimed', 'resolved', 'archived'],\n    default: 'active'\n  },\n  images: [{\n    url: {\n      type: String,\n      required: true\n    },\n    publicId: {\n      type: String,\n      required: true\n    }\n  }],\n  location: {\n    type: String,\n    required: [true, 'Location is required'],\n    trim: true,\n    maxlength: [100, 'Location cannot be more than 100 characters']\n  },\n  dateOccurred: {\n    type: Date,\n    required: [true, 'Date is required'],\n    validate: {\n      validator: function(date) {\n        return date <= new Date();\n      },\n      message: 'Date cannot be in the future'\n    }\n  },\n  contactInfo: {\n    email: {\n      type: String,\n      required: true\n    },\n    phone: {\n      type: String,\n      trim: true\n    },\n    preferredContact: {\n      type: String,\n      enum: ['email', 'phone', 'chat'],\n      default: 'email'\n    }\n  },\n  postedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  isUrgent: {\n    type: Boolean,\n    default: false\n  },\n  reward: {\n    offered: {\n      type: Boolean,\n      default: false\n    },\n    amount: {\n      type: Number,\n      min: 0\n    },\n    description: {\n      type: String,\n      trim: true\n    }\n  },\n  views: {\n    type: Number,\n    default: 0\n  },\n  claimsCount: {\n    type: Number,\n    default: 0\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes for better query performance\nItemSchema.index({ type: 1, status: 1, createdAt: -1 });\nItemSchema.index({ category: 1, type: 1 });\nItemSchema.index({ location: 1, type: 1 });\nItemSchema.index({ postedBy: 1 });\nItemSchema.index({ tags: 1 });\nItemSchema.index({ 'contactInfo.email': 1 });\n\n// Virtual for age of the post\nItemSchema.virtual('age').get(function() {\n  return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days\n});\n\n// Method to increment views\nItemSchema.methods.incrementViews = function() {\n  this.views += 1;\n  return this.save();\n};\n\n// Method to increment claims count\nItemSchema.methods.incrementClaims = function() {\n  this.claimsCount += 1;\n  return this.save();\n};\n\nexport default mongoose.models.Item || mongoose.model('Item', ItemSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAK;SAAiD;IACpE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;YAAC;YAAQ;SAAQ;IACzB;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAU;YAAW;YAAY;SAAW;QACnD,SAAS;IACX;IACA,QAAQ;QAAC;YACP,KAAK;gBACH,MAAM;gBACN,UAAU;YACZ;YACA,UAAU;gBACR,MAAM;gBACN,UAAU;YACZ;QACF;KAAE;IACF,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;QACN,WAAW;YAAC;YAAK;SAA8C;IACjE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,UAAU;YACR,WAAW,SAAS,IAAI;gBACtB,OAAO,QAAQ,IAAI;YACrB;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,OAAO;YACL,MAAM;YACN,UAAU;QACZ;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,kBAAkB;YAChB,MAAM;YACN,MAAM;gBAAC;gBAAS;gBAAS;aAAO;YAChC,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,SAAS;YACP,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,KAAK;QACP;QACA,aAAa;YACX,MAAM;YACN,MAAM;QACR;IACF;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;IAAG,WAAW,CAAC;AAAE;AACrD,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,MAAM;AAAE;AACxC,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,MAAM;AAAE;AACxC,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,qBAAqB;AAAE;AAE1C,8BAA8B;AAC9B,WAAW,OAAO,CAAC,OAAO,GAAG,CAAC;IAC5B,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,IAAI,OAAO;AACnF;AAEA,4BAA4B;AAC5B,WAAW,OAAO,CAAC,cAAc,GAAG;IAClC,IAAI,CAAC,KAAK,IAAI;IACd,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,eAAe,GAAG;IACnC,IAAI,CAAC,WAAW,IAAI;IACpB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    validate: {\n      validator: function(email) {\n        // Validate UMT email domain\n        return email.endsWith('@umt.edu.pk') || email.endsWith('@student.umt.edu.pk');\n      },\n      message: 'Please use a valid UMT email address'\n    }\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters']\n  },\n  role: {\n    type: String,\n    enum: ['student', 'admin'],\n    default: 'student'\n  },\n  studentId: {\n    type: String,\n    sparse: true,\n    unique: true\n  },\n  phone: {\n    type: String,\n    trim: true\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  verificationToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordToken: {\n    type: String,\n    default: null\n  },\n  resetPasswordExpires: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Remove password from JSON output\nUserSchema.methods.toJSON = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  delete userObject.verificationToken;\n  delete userObject.resetPasswordToken;\n  delete userObject.resetPasswordExpires;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,UAAU;YACR,WAAW,SAAS,KAAK;gBACvB,4BAA4B;gBAC5B,OAAO,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC;YACzD;YACA,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;IAC1D;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAW;SAAQ;QAC1B,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,mBAAmB;QACjB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,sBAAsB;QACpB,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,mCAAmC;AACnC,WAAW,OAAO,CAAC,MAAM,GAAG;IAC1B,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO,WAAW,iBAAiB;IACnC,OAAO,WAAW,kBAAkB;IACpC,OAAO,WAAW,oBAAoB;IACtC,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/auth.js"], "sourcesContent": ["import CredentialsProvider from 'next-auth/providers/credentials';\nimport { getServerSession } from 'next-auth';\nimport connectDB from './db';\nimport User from '@/models/User';\n\nexport const authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error('Email and password are required');\n        }\n\n        await connectDB();\n\n        const user = await User.findOne({ email: credentials.email });\n\n        if (!user) {\n          throw new Error('No user found with this email');\n        }\n\n        const isPasswordValid = await user.comparePassword(credentials.password);\n\n        if (!isPasswordValid) {\n          throw new Error('Invalid password');\n        }\n\n        if (!user.isVerified) {\n          throw new Error('Please verify your email before logging in');\n        }\n\n        return {\n          id: user._id.toString(),\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          studentId: user.studentId,\n          avatar: user.avatar\n        };\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.studentId = user.studentId;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub;\n        session.user.role = token.role;\n        session.user.studentId = token.studentId;\n        session.user.avatar = token.avatar;\n      }\n      return session;\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error'\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n// Utility functions for authentication\nexport const validateUMTEmail = (email) => {\n  const umtDomains = ['@umt.edu.pk', '@student.umt.edu.pk'];\n  return umtDomains.some(domain => email.endsWith(domain));\n};\n\nexport const generateVerificationToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\nexport const generateResetToken = () => {\n  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n};\n\n// Role-based access control\nexport const requireAuth = (handler, requiredRole = null) => {\n  return async (req, res) => {\n    const session = await getServerSession(req, res, authOptions);\n\n    if (!session) {\n      return res.status(401).json({ error: 'Authentication required' });\n    }\n\n    if (requiredRole && session.user.role !== requiredRole) {\n      return res.status(403).json({ error: 'Insufficient permissions' });\n    }\n\n    req.user = session.user;\n    return handler(req, res);\n  };\n};\n\n// Check if user is admin\nexport const isAdmin = (user) => {\n  return user?.role === 'admin';\n};\n\n// Check if user owns the resource\nexport const isOwner = (user, resourceUserId) => {\n  return user?.id === resourceUserId.toString();\n};\n\n// Check if user can access resource\nexport const canAccess = (user, resourceUserId, requiredRole = null) => {\n  if (requiredRole && user?.role !== requiredRole) {\n    return false;\n  }\n\n  return isAdmin(user) || isOwner(user, resourceUserId);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;gBAEd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,YAAY,KAAK;gBAAC;gBAE3D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,YAAY,QAAQ;gBAEvE,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;oBACrB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;oBACzB,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,aAAa;QAAC;QAAe;KAAsB;IACzD,OAAO,WAAW,IAAI,CAAC,CAAA,SAAU,MAAM,QAAQ,CAAC;AAClD;AAEO,MAAM,4BAA4B;IACvC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAEO,MAAM,qBAAqB;IAChC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAC/F;AAGO,MAAM,cAAc,CAAC,SAAS,eAAe,IAAI;IACtD,OAAO,OAAO,KAAK;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,KAAK;QAEjD,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA0B;QACjE;QAEA,IAAI,gBAAgB,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAc;YACtD,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAAE,OAAO;YAA2B;QAClE;QAEA,IAAI,IAAI,GAAG,QAAQ,IAAI;QACvB,OAAO,QAAQ,KAAK;IACtB;AACF;AAGO,MAAM,UAAU,CAAC;IACtB,OAAO,MAAM,SAAS;AACxB;AAGO,MAAM,UAAU,CAAC,MAAM;IAC5B,OAAO,MAAM,OAAO,eAAe,QAAQ;AAC7C;AAGO,MAAM,YAAY,CAAC,MAAM,gBAAgB,eAAe,IAAI;IACjE,IAAI,gBAAgB,MAAM,SAAS,cAAc;QAC/C,OAAO;IACT;IAEA,OAAO,QAAQ,SAAS,QAAQ,MAAM;AACxC", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/models/Notification.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst NotificationSchema = new mongoose.Schema({\n  recipient: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: true\n  },\n  sender: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User'\n  },\n  type: {\n    type: String,\n    required: true,\n    enum: [\n      'new_claim',\n      'claim_approved',\n      'claim_rejected',\n      'claim_completed',\n      'item_matched',\n      'message_received',\n      'item_expired',\n      'system_announcement'\n    ]\n  },\n  title: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: [100, 'Title cannot be more than 100 characters']\n  },\n  message: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: [300, 'Message cannot be more than 300 characters']\n  },\n  relatedItem: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'Item'\n  },\n  relatedClaim: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'Claim'\n  },\n  actionUrl: {\n    type: String,\n    trim: true\n  },\n  isRead: {\n    type: Boolean,\n    default: false\n  },\n  readAt: {\n    type: Date\n  },\n  priority: {\n    type: String,\n    enum: ['low', 'medium', 'high', 'urgent'],\n    default: 'medium'\n  },\n  expiresAt: {\n    type: Date\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes\nNotificationSchema.index({ recipient: 1, isRead: 1, createdAt: -1 });\nNotificationSchema.index({ type: 1 });\nNotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });\n\n// Virtual for notification age\nNotificationSchema.virtual('age').get(function() {\n  return Math.floor((new Date() - this.createdAt) / (1000 * 60 * 60 * 24)); // days\n});\n\n// Method to mark as read\nNotificationSchema.methods.markAsRead = function() {\n  this.isRead = true;\n  this.readAt = new Date();\n  return this.save();\n};\n\n// Static method to create notification\nNotificationSchema.statics.createNotification = async function(data) {\n  const notification = new this(data);\n  await notification.save();\n  \n  // Here you could emit socket event for real-time notifications\n  // socketIO.to(data.recipient.toString()).emit('new_notification', notification);\n  \n  return notification;\n};\n\n// Static method to mark all as read for a user\nNotificationSchema.statics.markAllAsRead = async function(userId) {\n  return this.updateMany(\n    { recipient: userId, isRead: false },\n    { isRead: true, readAt: new Date() }\n  );\n};\n\nexport default mongoose.models.Notification || mongoose.model('Notification', NotificationSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,qBAAqB,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IAC7C,WAAW;QACT,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;IACZ;IACA,QAAQ;QACN,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;IACP;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;YAAC;YAAK;SAA6C;IAChE;IACA,aAAa;QACX,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;IACP;IACA,cAAc;QACZ,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;IACP;IACA,WAAW;QACT,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,MAAM;YAAC;YAAO;YAAU;YAAQ;SAAS;QACzC,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;AACF,GAAG;IACD,YAAY;AACd;AAEA,UAAU;AACV,mBAAmB,KAAK,CAAC;IAAE,WAAW;IAAG,QAAQ;IAAG,WAAW,CAAC;AAAE;AAClE,mBAAmB,KAAK,CAAC;IAAE,MAAM;AAAE;AACnC,mBAAmB,KAAK,CAAC;IAAE,WAAW;AAAE,GAAG;IAAE,oBAAoB;AAAE;AAEnE,+BAA+B;AAC/B,mBAAmB,OAAO,CAAC,OAAO,GAAG,CAAC;IACpC,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,IAAI,OAAO;AACnF;AAEA,yBAAyB;AACzB,mBAAmB,OAAO,CAAC,UAAU,GAAG;IACtC,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,uCAAuC;AACvC,mBAAmB,OAAO,CAAC,kBAAkB,GAAG,eAAe,IAAI;IACjE,MAAM,eAAe,IAAI,IAAI,CAAC;IAC9B,MAAM,aAAa,IAAI;IAEvB,+DAA+D;IAC/D,iFAAiF;IAEjF,OAAO;AACT;AAEA,+CAA+C;AAC/C,mBAAmB,OAAO,CAAC,aAAa,GAAG,eAAe,MAAM;IAC9D,OAAO,IAAI,CAAC,UAAU,CACpB;QAAE,WAAW;QAAQ,QAAQ;IAAM,GACnC;QAAE,QAAQ;QAAM,QAAQ,IAAI;IAAO;AAEvC;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,YAAY,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/lib/notifier.js"], "sourcesContent": ["import Notification from '@/models/Notification';\nimport connectDB from './db';\n\n// Notification types and their templates\nconst NOTIFICATION_TEMPLATES = {\n  new_claim: {\n    title: 'New Claim Request',\n    getMessage: (data) => `${data.claimantName} has submitted a claim for your ${data.itemType} item \"${data.itemTitle}\"`\n  },\n  claim_approved: {\n    title: 'Claim Approved',\n    getMessage: (data) => `Your claim for \"${data.itemTitle}\" has been approved. You can now contact the owner.`\n  },\n  claim_rejected: {\n    title: 'Claim Rejected',\n    getMessage: (data) => `Your claim for \"${data.itemTitle}\" has been rejected. ${data.reason || ''}`\n  },\n  claim_completed: {\n    title: 'Claim Completed',\n    getMessage: (data) => `The claim for \"${data.itemTitle}\" has been marked as completed.`\n  },\n  item_matched: {\n    title: 'Potential Match Found',\n    getMessage: (data) => `We found a potential match for your ${data.itemType} item. Check it out!`\n  },\n  message_received: {\n    title: 'New Message',\n    getMessage: (data) => `You have a new message from ${data.senderName} about \"${data.itemTitle}\"`\n  },\n  item_expired: {\n    title: 'Item Listing Expired',\n    getMessage: (data) => `Your ${data.itemType} item \"${data.itemTitle}\" listing has expired after 30 days.`\n  },\n  system_announcement: {\n    title: 'System Announcement',\n    getMessage: (data) => data.message\n  }\n};\n\n// Create notification\nexport const createNotification = async (type, recipientId, data, options = {}) => {\n  try {\n    await connectDB();\n\n    const template = NOTIFICATION_TEMPLATES[type];\n    if (!template) {\n      throw new Error(`Unknown notification type: ${type}`);\n    }\n\n    const notification = await Notification.createNotification({\n      recipient: recipientId,\n      sender: data.senderId || null,\n      type,\n      title: options.title || template.title,\n      message: options.message || template.getMessage(data),\n      relatedItem: data.itemId || null,\n      relatedClaim: data.claimId || null,\n      actionUrl: options.actionUrl || null,\n      priority: options.priority || 'medium',\n      expiresAt: options.expiresAt || null\n    });\n\n    // Here you would emit socket event for real-time notifications\n    // if (global.io) {\n    //   global.io.to(recipientId.toString()).emit('new_notification', notification);\n    // }\n\n    return notification;\n  } catch (error) {\n    console.error('Error creating notification:', error);\n    throw error;\n  }\n};\n\n// Create multiple notifications\nexport const createBulkNotifications = async (notifications) => {\n  try {\n    await connectDB();\n\n    const notificationPromises = notifications.map(({ type, recipientId, data, options }) =>\n      createNotification(type, recipientId, data, options)\n    );\n\n    const results = await Promise.all(notificationPromises);\n    return results;\n  } catch (error) {\n    console.error('Error creating bulk notifications:', error);\n    throw error;\n  }\n};\n\n// Get user notifications\nexport const getUserNotifications = async (userId, options = {}) => {\n  try {\n    await connectDB();\n\n    const {\n      page = 1,\n      limit = 20,\n      unreadOnly = false,\n      type = null\n    } = options;\n\n    const query = { recipient: userId };\n    \n    if (unreadOnly) {\n      query.isRead = false;\n    }\n    \n    if (type) {\n      query.type = type;\n    }\n\n    const notifications = await Notification.find(query)\n      .populate('sender', 'name avatar')\n      .populate('relatedItem', 'title type')\n      .populate('relatedClaim', 'status')\n      .sort({ createdAt: -1 })\n      .limit(limit * 1)\n      .skip((page - 1) * limit);\n\n    const total = await Notification.countDocuments(query);\n\n    return {\n      notifications,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    };\n  } catch (error) {\n    console.error('Error getting user notifications:', error);\n    throw error;\n  }\n};\n\n// Mark notification as read\nexport const markNotificationAsRead = async (notificationId, userId) => {\n  try {\n    await connectDB();\n\n    const notification = await Notification.findOne({\n      _id: notificationId,\n      recipient: userId\n    });\n\n    if (!notification) {\n      throw new Error('Notification not found');\n    }\n\n    await notification.markAsRead();\n    return notification;\n  } catch (error) {\n    console.error('Error marking notification as read:', error);\n    throw error;\n  }\n};\n\n// Mark all notifications as read\nexport const markAllNotificationsAsRead = async (userId) => {\n  try {\n    await connectDB();\n    \n    const result = await Notification.markAllAsRead(userId);\n    return result;\n  } catch (error) {\n    console.error('Error marking all notifications as read:', error);\n    throw error;\n  }\n};\n\n// Get unread notification count\nexport const getUnreadNotificationCount = async (userId) => {\n  try {\n    await connectDB();\n    \n    const count = await Notification.countDocuments({\n      recipient: userId,\n      isRead: false\n    });\n    \n    return count;\n  } catch (error) {\n    console.error('Error getting unread notification count:', error);\n    throw error;\n  }\n};\n\n// Clean up expired notifications\nexport const cleanupExpiredNotifications = async () => {\n  try {\n    await connectDB();\n    \n    const result = await Notification.deleteMany({\n      expiresAt: { $lt: new Date() }\n    });\n    \n    console.log(`Cleaned up ${result.deletedCount} expired notifications`);\n    return result;\n  } catch (error) {\n    console.error('Error cleaning up expired notifications:', error);\n    throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEA,yCAAyC;AACzC,MAAM,yBAAyB;IAC7B,WAAW;QACT,OAAO;QACP,YAAY,CAAC,OAAS,GAAG,KAAK,YAAY,CAAC,gCAAgC,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC;IACvH;IACA,gBAAgB;QACd,OAAO;QACP,YAAY,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,SAAS,CAAC,mDAAmD,CAAC;IAC9G;IACA,gBAAgB;QACd,OAAO;QACP,YAAY,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,SAAS,CAAC,qBAAqB,EAAE,KAAK,MAAM,IAAI,IAAI;IACpG;IACA,iBAAiB;QACf,OAAO;QACP,YAAY,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,SAAS,CAAC,+BAA+B,CAAC;IACzF;IACA,cAAc;QACZ,OAAO;QACP,YAAY,CAAC,OAAS,CAAC,oCAAoC,EAAE,KAAK,QAAQ,CAAC,oBAAoB,CAAC;IAClG;IACA,kBAAkB;QAChB,OAAO;QACP,YAAY,CAAC,OAAS,CAAC,4BAA4B,EAAE,KAAK,UAAU,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC;IAClG;IACA,cAAc;QACZ,OAAO;QACP,YAAY,CAAC,OAAS,CAAC,KAAK,EAAE,KAAK,QAAQ,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,oCAAoC,CAAC;IAC3G;IACA,qBAAqB;QACnB,OAAO;QACP,YAAY,CAAC,OAAS,KAAK,OAAO;IACpC;AACF;AAGO,MAAM,qBAAqB,OAAO,MAAM,aAAa,MAAM,UAAU,CAAC,CAAC;IAC5E,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,WAAW,sBAAsB,CAAC,KAAK;QAC7C,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM;QACtD;QAEA,MAAM,eAAe,MAAM,+HAAA,CAAA,UAAY,CAAC,kBAAkB,CAAC;YACzD,WAAW;YACX,QAAQ,KAAK,QAAQ,IAAI;YACzB;YACA,OAAO,QAAQ,KAAK,IAAI,SAAS,KAAK;YACtC,SAAS,QAAQ,OAAO,IAAI,SAAS,UAAU,CAAC;YAChD,aAAa,KAAK,MAAM,IAAI;YAC5B,cAAc,KAAK,OAAO,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;QAClC;QAEA,+DAA+D;QAC/D,mBAAmB;QACnB,iFAAiF;QACjF,IAAI;QAEJ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,GAClF,mBAAmB,MAAM,aAAa,MAAM;QAG9C,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAGO,MAAM,uBAAuB,OAAO,QAAQ,UAAU,CAAC,CAAC;IAC7D,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EACJ,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,aAAa,KAAK,EAClB,OAAO,IAAI,EACZ,GAAG;QAEJ,MAAM,QAAQ;YAAE,WAAW;QAAO;QAElC,IAAI,YAAY;YACd,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,MAAM,gBAAgB,MAAM,+HAAA,CAAA,UAAY,CAAC,IAAI,CAAC,OAC3C,QAAQ,CAAC,UAAU,eACnB,QAAQ,CAAC,eAAe,cACxB,QAAQ,CAAC,gBAAgB,UACzB,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,KAAK,CAAC,QAAQ,GACd,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI;QAErB,MAAM,QAAQ,MAAM,+HAAA,CAAA,UAAY,CAAC,cAAc,CAAC;QAEhD,OAAO;YACL;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAGO,MAAM,yBAAyB,OAAO,gBAAgB;IAC3D,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,eAAe,MAAM,+HAAA,CAAA,UAAY,CAAC,OAAO,CAAC;YAC9C,KAAK;YACL,WAAW;QACb;QAEA,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,aAAa,UAAU;QAC7B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,SAAS,MAAM,+HAAA,CAAA,UAAY,CAAC,aAAa,CAAC;QAChD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,QAAQ,MAAM,+HAAA,CAAA,UAAY,CAAC,cAAc,CAAC;YAC9C,WAAW;YACX,QAAQ;QACV;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAGO,MAAM,8BAA8B;IACzC,IAAI;QACF,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,SAAS,MAAM,+HAAA,CAAA,UAAY,CAAC,UAAU,CAAC;YAC3C,WAAW;gBAAE,KAAK,IAAI;YAAO;QAC/B;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,YAAY,CAAC,sBAAsB,CAAC;QACrE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/api/claims/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport connectDB from '@/lib/db';\nimport Claim from '@/models/Claim';\nimport Item from '@/models/Item';\nimport { authOptions } from '@/lib/auth';\nimport { createNotification } from '@/lib/notifier';\n\nexport async function GET(request) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const type = searchParams.get('type'); // 'sent' or 'received'\n    const status = searchParams.get('status');\n    const page = parseInt(searchParams.get('page')) || 1;\n    const limit = parseInt(searchParams.get('limit')) || 10;\n\n    let query = {};\n    \n    if (type === 'sent') {\n      query.claimant = session.user.id;\n    } else if (type === 'received') {\n      query.itemOwner = session.user.id;\n    } else {\n      // Get both sent and received claims\n      query.$or = [\n        { claimant: session.user.id },\n        { itemOwner: session.user.id }\n      ];\n    }\n\n    if (status) {\n      query.status = status;\n    }\n\n    const claims = await Claim.find(query)\n      .populate('item', 'title type category location images')\n      .populate('claimant', 'name email avatar')\n      .populate('itemOwner', 'name email avatar')\n      .sort({ createdAt: -1 })\n      .limit(limit)\n      .skip((page - 1) * limit)\n      .lean();\n\n    const total = await Claim.countDocuments(query);\n\n    return NextResponse.json({\n      claims,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    });\n\n  } catch (error) {\n    console.error('Claims fetch error:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch claims' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    await connectDB();\n\n    const { itemId, message, verificationAnswers, proofImages } = await request.json();\n\n    // Validate required fields\n    if (!itemId || !message) {\n      return NextResponse.json(\n        { error: 'Item ID and message are required' },\n        { status: 400 }\n      );\n    }\n\n    // Get the item\n    const item = await Item.findById(itemId).populate('postedBy');\n    \n    if (!item) {\n      return NextResponse.json(\n        { error: 'Item not found' },\n        { status: 404 }\n      );\n    }\n\n    // Check if user is trying to claim their own item\n    if (item.postedBy._id.toString() === session.user.id) {\n      return NextResponse.json(\n        { error: 'You cannot claim your own item' },\n        { status: 400 }\n      );\n    }\n\n    // Check if user has already claimed this item\n    const existingClaim = await Claim.findOne({\n      item: itemId,\n      claimant: session.user.id\n    });\n\n    if (existingClaim) {\n      return NextResponse.json(\n        { error: 'You have already submitted a claim for this item' },\n        { status: 400 }\n      );\n    }\n\n    // Create new claim\n    const claim = new Claim({\n      item: itemId,\n      claimant: session.user.id,\n      itemOwner: item.postedBy._id,\n      message,\n      verificationQuestions: verificationAnswers || [],\n      proofImages: proofImages || []\n    });\n\n    await claim.save();\n\n    // Increment claims count on item\n    await Item.findByIdAndUpdate(itemId, {\n      $inc: { claimsCount: 1 }\n    });\n\n    // Create notification for item owner\n    await createNotification(\n      'new_claim',\n      item.postedBy._id,\n      {\n        claimantName: session.user.name,\n        itemTitle: item.title,\n        itemType: item.type,\n        itemId: itemId,\n        claimId: claim._id\n      },\n      {\n        actionUrl: `/dashboard/claims/${claim._id}`\n      }\n    );\n\n    // Populate the claim for response\n    await claim.populate([\n      { path: 'item', select: 'title type category location' },\n      { path: 'claimant', select: 'name email avatar' },\n      { path: 'itemOwner', select: 'name email avatar' }\n    ]);\n\n    return NextResponse.json(\n      { \n        message: 'Claim submitted successfully',\n        claim\n      },\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Claim creation error:', error);\n    \n    if (error.name === 'ValidationError') {\n      const errors = Object.values(error.errors).map(err => err.message);\n      return NextResponse.json(\n        { error: errors.join(', ') },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { error: 'Failed to create claim' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC,SAAS,uBAAuB;QAC9D,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,YAAY;QACnD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,aAAa;QAErD,IAAI,QAAQ,CAAC;QAEb,IAAI,SAAS,QAAQ;YACnB,MAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,EAAE;QAClC,OAAO,IAAI,SAAS,YAAY;YAC9B,MAAM,SAAS,GAAG,QAAQ,IAAI,CAAC,EAAE;QACnC,OAAO;YACL,oCAAoC;YACpC,MAAM,GAAG,GAAG;gBACV;oBAAE,UAAU,QAAQ,IAAI,CAAC,EAAE;gBAAC;gBAC5B;oBAAE,WAAW,QAAQ,IAAI,CAAC,EAAE;gBAAC;aAC9B;QACH;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,MAAM,SAAS,MAAM,wHAAA,CAAA,UAAK,CAAC,IAAI,CAAC,OAC7B,QAAQ,CAAC,QAAQ,uCACjB,QAAQ,CAAC,YAAY,qBACrB,QAAQ,CAAC,aAAa,qBACtB,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,KAAK,CAAC,OACN,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,OAClB,IAAI;QAEP,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,cAAc,CAAC;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEhF,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,QAAQ,CAAC;QAElD,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,QAAQ,OAAO,QAAQ,IAAI,CAAC,EAAE,EAAE;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8CAA8C;QAC9C,MAAM,gBAAgB,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACxC,MAAM;YACN,UAAU,QAAQ,IAAI,CAAC,EAAE;QAC3B;QAEA,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmD,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,QAAQ,IAAI,wHAAA,CAAA,UAAK,CAAC;YACtB,MAAM;YACN,UAAU,QAAQ,IAAI,CAAC,EAAE;YACzB,WAAW,KAAK,QAAQ,CAAC,GAAG;YAC5B;YACA,uBAAuB,uBAAuB,EAAE;YAChD,aAAa,eAAe,EAAE;QAChC;QAEA,MAAM,MAAM,IAAI;QAEhB,iCAAiC;QACjC,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,QAAQ;YACnC,MAAM;gBAAE,aAAa;YAAE;QACzB;QAEA,qCAAqC;QACrC,MAAM,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EACrB,aACA,KAAK,QAAQ,CAAC,GAAG,EACjB;YACE,cAAc,QAAQ,IAAI,CAAC,IAAI;YAC/B,WAAW,KAAK,KAAK;YACrB,UAAU,KAAK,IAAI;YACnB,QAAQ;YACR,SAAS,MAAM,GAAG;QACpB,GACA;YACE,WAAW,CAAC,kBAAkB,EAAE,MAAM,GAAG,EAAE;QAC7C;QAGF,kCAAkC;QAClC,MAAM,MAAM,QAAQ,CAAC;YACnB;gBAAE,MAAM;gBAAQ,QAAQ;YAA+B;YACvD;gBAAE,MAAM;gBAAY,QAAQ;YAAoB;YAChD;gBAAE,MAAM;gBAAa,QAAQ;YAAoB;SAClD;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT;QACF,GACA;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,IAAI,MAAM,IAAI,KAAK,mBAAmB;YACpC,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO;YACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,OAAO,IAAI,CAAC;YAAM,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}