globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/items/post/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/src/components/providers/AuthProvider.js <module evaluation>":{"id":"[project]/src/components/providers/AuthProvider.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js"],"async":false},"[project]/src/components/providers/AuthProvider.js":{"id":"[project]/src/components/providers/AuthProvider.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js"],"async":false},"[project]/src/components/providers/NotificationProvider.js <module evaluation>":{"id":"[project]/src/components/providers/NotificationProvider.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js"],"async":false},"[project]/src/components/providers/NotificationProvider.js":{"id":"[project]/src/components/providers/NotificationProvider.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js"],"async":false},"[project]/src/components/providers/SocketProvider.js <module evaluation>":{"id":"[project]/src/components/providers/SocketProvider.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js"],"async":false},"[project]/src/components/providers/SocketProvider.js":{"id":"[project]/src/components/providers/SocketProvider.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js"],"async":false},"[project]/src/app/items/post/page.js <module evaluation>":{"id":"[project]/src/app/items/post/page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js","/_next/static/chunks/src_4d7268c1._.js","/_next/static/chunks/node_modules_775b0728._.js","/_next/static/chunks/src_app_items_post_page_8fd5a5ae.js"],"async":false},"[project]/src/app/items/post/page.js":{"id":"[project]/src/app/items/post/page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_29efe55f._.js","/_next/static/chunks/src_components_providers_8773af10._.js","/_next/static/chunks/src_app_layout_c0237562.js","/_next/static/chunks/src_4d7268c1._.js","/_next/static/chunks/node_modules_775b0728._.js","/_next/static/chunks/src_app_items_post_page_8fd5a5ae.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/components/providers/AuthProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/AuthProvider.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_d36fd33a._.js","server/chunks/ssr/[root-of-the-server]__e31f78c2._.js"],"async":false}},"[project]/src/components/providers/NotificationProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/NotificationProvider.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_d36fd33a._.js","server/chunks/ssr/[root-of-the-server]__e31f78c2._.js"],"async":false}},"[project]/src/components/providers/SocketProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/SocketProvider.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_d36fd33a._.js","server/chunks/ssr/[root-of-the-server]__e31f78c2._.js"],"async":false}},"[project]/src/app/items/post/page.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/items/post/page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_d36fd33a._.js","server/chunks/ssr/[root-of-the-server]__e31f78c2._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_017dd933._.js","server/chunks/ssr/[root-of-the-server]__9a7ed3d9._.js","server/chunks/ssr/node_modules_next_03588337._.js","server/chunks/ssr/node_modules_@headlessui_react_dist_31278707._.js","server/chunks/ssr/node_modules_@floating-ui_react_dist_d7777e42._.js","server/chunks/ssr/node_modules_fdcb6037._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/src/components/providers/AuthProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/AuthProvider.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/src/components/providers/NotificationProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/NotificationProvider.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/src/components/providers/SocketProvider.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/SocketProvider.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}},"[project]/src/app/items/post/page.js [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/items/post/page.js (client reference/proxy)","name":"*","chunks":["server/app/items/post/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__85eb511c._.css","inlined":false}],"[project]/src/app/items/post/page":[{"path":"static/chunks/[root-of-the-server]__85eb511c._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"[project]/src/app/layout":["static/chunks/node_modules_29efe55f._.js","static/chunks/src_components_providers_8773af10._.js","static/chunks/src_app_layout_c0237562.js"],"[project]/src/app/items/post/page":["static/chunks/node_modules_29efe55f._.js","static/chunks/src_components_providers_8773af10._.js","static/chunks/src_app_layout_c0237562.js","static/chunks/src_4d7268c1._.js","static/chunks/node_modules_775b0728._.js","static/chunks/src_app_items_post_page_8fd5a5ae.js"]}}
