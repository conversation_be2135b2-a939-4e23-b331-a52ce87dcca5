{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n  { name: 'Messages', href: '/chat/list' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n\n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n\n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto  px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex mx-auto h-16 w-[90%] items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                {/* <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span> */}\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n\n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n\n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAY,MAAM;IAAa;CACxC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAgC,cAAW;;8BACxD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;8CASrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/items/%5Bid%5D/claim/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useParams, useRouter } from 'next/navigation';\nimport { \n  PhotoIcon, \n  XMarkIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nexport default function ClaimItem() {\n  const { id } = useParams();\n  const { data: session } = useSession();\n  const router = useRouter();\n  const [item, setItem] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  const [formData, setFormData] = useState({\n    message: '',\n    proofImages: [],\n    verificationAnswers: []\n  });\n\n  const [imagePreviews, setImagePreviews] = useState([]);\n\n  useEffect(() => {\n    if (id) {\n      fetchItem();\n    }\n  }, [id]);\n\n  const fetchItem = async () => {\n    try {\n      const response = await fetch(`/api/items/${id}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setItem(data.item);\n        // Initialize verification questions\n        if (data.item.type === 'found') {\n          setFormData(prev => ({\n            ...prev,\n            verificationAnswers: [\n              { question: 'What color is the item?', answer: '' },\n              { question: 'Where exactly did you lose it?', answer: '' },\n              { question: 'What brand is it (if applicable)?', answer: '' }\n            ]\n          }));\n        }\n      } else {\n        setError(data.error || 'Item not found');\n      }\n    } catch (error) {\n      setError('Failed to load item');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleVerificationChange = (index, value) => {\n    setFormData(prev => ({\n      ...prev,\n      verificationAnswers: prev.verificationAnswers.map((qa, i) => \n        i === index ? { ...qa, answer: value } : qa\n      )\n    }));\n  };\n\n  const handleImageUpload = (e) => {\n    const files = Array.from(e.target.files);\n    \n    if (files.length + formData.proofImages.length > 3) {\n      setError('You can upload maximum 3 proof images');\n      return;\n    }\n\n    files.forEach(file => {\n      if (file.size > 5 * 1024 * 1024) {\n        setError('Each image must be less than 5MB');\n        return;\n      }\n\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreviews(prev => [...prev, e.target.result]);\n      };\n      reader.readAsDataURL(file);\n    });\n\n    setFormData(prev => ({\n      ...prev,\n      proofImages: [...prev.proofImages, ...files]\n    }));\n  };\n\n  const removeImage = (index) => {\n    setFormData(prev => ({\n      ...prev,\n      proofImages: prev.proofImages.filter((_, i) => i !== index)\n    }));\n    setImagePreviews(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const validateForm = () => {\n    if (!formData.message.trim()) {\n      setError('Please provide a detailed message explaining why this item belongs to you');\n      return false;\n    }\n\n    if (item.type === 'found') {\n      const unansweredQuestions = formData.verificationAnswers.filter(qa => !qa.answer.trim());\n      if (unansweredQuestions.length > 0) {\n        setError('Please answer all verification questions');\n        return false;\n      }\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError('');\n    setSuccess('');\n\n    if (!validateForm()) {\n      setSubmitting(false);\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/claims', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          itemId: id,\n          message: formData.message,\n          verificationAnswers: formData.verificationAnswers,\n          // In production, you'd upload images first\n          proofImages: []\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setError(data.error || 'Something went wrong');\n      } else {\n        setSuccess('Claim request submitted successfully! The item owner will be notified.');\n        setTimeout(() => {\n          router.push('/dashboard');\n        }, 3000);\n      }\n    } catch (error) {\n      setError('An unexpected error occurred');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-8\"></div>\n            <div className=\"h-64 bg-gray-200 rounded\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error && !item) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Item Not Found</h1>\n            <p className=\"text-gray-600 mb-8\">{error}</p>\n            <button onClick={() => router.back()} className=\"btn-primary\">\n              Go Back\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <div className=\"mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Claim Item</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Submit a claim request for \"{item?.title}\"\n          </p>\n        </div>\n\n        {/* Item Summary */}\n        <div className=\"card mb-8\">\n          <div className=\"card-body\">\n            <div className=\"flex items-start space-x-4\">\n              {item?.images && item.images.length > 0 ? (\n                <img\n                  src={item.images[0].url}\n                  alt={item.title}\n                  className=\"w-24 h-24 object-cover rounded-lg\"\n                />\n              ) : (\n                <div className=\"w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-gray-400 text-sm\">No Image</span>\n                </div>\n              )}\n              \n              <div className=\"flex-1\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">{item?.title}</h3>\n                <p className=\"text-gray-600 mt-1\">{item?.description}</p>\n                <div className=\"mt-2 flex items-center space-x-4 text-sm text-gray-500\">\n                  <span>Found at: {item?.location}</span>\n                  <span>•</span>\n                  <span>{item?.category}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"flex\">\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n                <div className=\"ml-3\">\n                  <div className=\"text-sm text-red-700\">{error}</div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {success && (\n            <div className=\"rounded-md bg-green-50 p-4\">\n              <div className=\"flex\">\n                <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                <div className=\"ml-3\">\n                  <div className=\"text-sm text-green-700\">{success}</div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Claim Message */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Why is this your item?</h3>\n            </div>\n            <div className=\"card-body\">\n              <textarea\n                name=\"message\"\n                rows={4}\n                required\n                className=\"form-textarea\"\n                placeholder=\"Please provide detailed information about why this item belongs to you. Include specific details that only the owner would know...\"\n                value={formData.message}\n                onChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Verification Questions */}\n          {item?.type === 'found' && (\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Verification Questions</h3>\n                <p className=\"text-sm text-gray-500\">Answer these questions to help verify ownership</p>\n              </div>\n              <div className=\"card-body space-y-4\">\n                {formData.verificationAnswers.map((qa, index) => (\n                  <div key={index}>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      {qa.question} *\n                    </label>\n                    <input\n                      type=\"text\"\n                      required\n                      className=\"form-input\"\n                      placeholder=\"Your answer...\"\n                      value={qa.answer}\n                      onChange={(e) => handleVerificationChange(index, e.target.value)}\n                    />\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Proof Images */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Proof Images (Optional)</h3>\n              <p className=\"text-sm text-gray-500\">Upload images that prove ownership (receipts, similar items, etc.)</p>\n            </div>\n            <div className=\"card-body\">\n              <div className=\"space-y-4\">\n                {/* Upload Area */}\n                <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6\">\n                  <div className=\"text-center\">\n                    <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"mt-4\">\n                      <label htmlFor=\"proofImages\" className=\"cursor-pointer\">\n                        <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                          Upload proof images\n                        </span>\n                        <input\n                          id=\"proofImages\"\n                          name=\"proofImages\"\n                          type=\"file\"\n                          multiple\n                          accept=\"image/*\"\n                          className=\"sr-only\"\n                          onChange={handleImageUpload}\n                        />\n                      </label>\n                      <p className=\"text-xs text-gray-500 mt-1\">PNG, JPG, GIF up to 5MB each (max 3 images)</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Image Previews */}\n                {imagePreviews.length > 0 && (\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                    {imagePreviews.map((preview, index) => (\n                      <div key={index} className=\"relative\">\n                        <img\n                          src={preview}\n                          alt={`Proof ${index + 1}`}\n                          className=\"w-full h-32 object-cover rounded-lg\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => removeImage(index)}\n                          className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-4\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={submitting}\n              className=\"btn-primary\"\n            >\n              {submitting ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Submitting Claim...\n                </div>\n              ) : (\n                'Submit Claim Request'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;;;AAXA;;;;;;AAae,SAAS;;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;QACT,aAAa,EAAE;QACf,qBAAqB,EAAE;IACzB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,IAAI;gBACN;YACF;QACF;8BAAG;QAAC;KAAG;IAEP,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,KAAK,IAAI;gBACjB,oCAAoC;gBACpC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,SAAS;oBAC9B,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,qBAAqB;gCACnB;oCAAE,UAAU;oCAA2B,QAAQ;gCAAG;gCAClD;oCAAE,UAAU;oCAAkC,QAAQ;gCAAG;gCACzD;oCAAE,UAAU;oCAAqC,QAAQ;gCAAG;6BAC7D;wBACH,CAAC;gBACH;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC,OAAO;QACvC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,qBAAqB,KAAK,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,IACrD,MAAM,QAAQ;wBAAE,GAAG,EAAE;wBAAE,QAAQ;oBAAM,IAAI;YAE7C,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;QAEvC,IAAI,MAAM,MAAM,GAAG,SAAS,WAAW,CAAC,MAAM,GAAG,GAAG;YAClD,SAAS;YACT;QACF;QAEA,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,SAAS;gBACT;YACF;YAEA,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM,EAAE,MAAM,CAAC,MAAM;qBAAC;YACrD;YACA,OAAO,aAAa,CAAC;QACvB;QAEA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,WAAW;uBAAK;iBAAM;YAC9C,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACvD,CAAC;QACD,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACvD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,SAAS;YACT,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,MAAM,sBAAsB,SAAS,mBAAmB,CAAC,MAAM,CAAC,CAAA,KAAM,CAAC,GAAG,MAAM,CAAC,IAAI;YACrF,IAAI,oBAAoB,MAAM,GAAG,GAAG;gBAClC,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,cAAc;QACd,SAAS;QACT,WAAW;QAEX,IAAI,CAAC,gBAAgB;YACnB,cAAc;YACd;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,SAAS,SAAS,OAAO;oBACzB,qBAAqB,SAAS,mBAAmB;oBACjD,2CAA2C;oBAC3C,aAAa,EAAE;gBACjB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;YACzB,OAAO;gBACL,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,6LAAC;gCAAO,SAAS,IAAM,OAAO,IAAI;gCAAI,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;IAOxE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;;oCAAqB;oCACH,MAAM;oCAAM;;;;;;;;;;;;;kCAK7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,UAAU,KAAK,MAAM,CAAC,MAAM,GAAG,kBACpC,6LAAC;wCACC,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,GAAG;wCACvB,KAAK,KAAK,KAAK;wCACf,WAAU;;;;;6DAGZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAI5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC,MAAM;;;;;;0DAC3D,6LAAC;gDAAE,WAAU;0DAAsB,MAAM;;;;;;0DACzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DAAW,MAAM;;;;;;;kEACvB,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvB,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;4BACrC,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gPAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;sDACnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;4BAM9C,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;0CAOjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,MAAM;4CACN,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,OAAO;4CACvB,UAAU;;;;;;;;;;;;;;;;;4BAMf,MAAM,SAAS,yBACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;kDACZ,SAAS,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,sBACrC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DACd,GAAG,QAAQ;4DAAC;;;;;;;kEAEf,6LAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO,GAAG,MAAM;wDAChB,UAAU,CAAC,IAAM,yBAAyB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;+CAVzD;;;;;;;;;;;;;;;;0CAmBlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,SAAQ;wEAAc,WAAU;;0FACrC,6LAAC;gFAAK,WAAU;0FAA+C;;;;;;0FAG/D,6LAAC;gFACC,IAAG;gFACH,MAAK;gFACL,MAAK;gFACL,QAAQ;gFACR,QAAO;gFACP,WAAU;gFACV,UAAU;;;;;;;;;;;;kFAGd,6LAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;;;;;;gDAM/C,cAAc,MAAM,GAAG,mBACtB,6LAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEACC,KAAK;oEACL,KAAK,CAAC,MAAM,EAAE,QAAQ,GAAG;oEACzB,WAAU;;;;;;8EAEZ,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,YAAY;oEAC3B,WAAU;8EAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;2DAXf;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAsBtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,2BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;gDAAuE;;;;;;mDAIxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GArYwB;;QACP,qIAAA,CAAA,YAAS;QACE,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}