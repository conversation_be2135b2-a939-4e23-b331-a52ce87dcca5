{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "acde95e2832a134e5904caa9a397905d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "386e3fbb2f1573c49e9813cfed00f1b4294d915333063298ba90248239e12e26", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4ed0a508af8e0b411f99b51a17f3dc8754aaf034a67a208b5fdb3a6442b591d6"}}}, "sortedMiddleware": ["/"], "functions": {}}