{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "24ed1e7a39e5e9d771f9a445b38444b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2a4bcb899d042632ca6a685f252878ce6e437bcdc45ae8217ba34e711d9de6e6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "688780819a4e6df42acae70bbbf9ab622fadc6fa2c904356bc3cf46228671a73"}}}, "sortedMiddleware": ["/"], "functions": {}}