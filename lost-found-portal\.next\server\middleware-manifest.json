{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "9785aecf502510fe2a49fa8e1e7ae144", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "057e4267d9cce3046ce600da6a6cf1c88b97d0ead9f6cb0fe0e6c8e704028852", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a87c877259add0566ce33d64e6a919d78318b31ea88e18e5a63393152e3b8f8b"}}}, "sortedMiddleware": ["/"], "functions": {}}