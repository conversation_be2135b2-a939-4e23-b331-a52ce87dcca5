{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_286d5ce3._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_7b18f62d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "novUP40J6joTGxYfm6xy2C9ks3c13BTY7qJfWOi0vfo=", "__NEXT_PREVIEW_MODE_ID": "c8c751dd6bfeb5b0fcdfb3357feac92d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "26859cfa929086f78c2ab6d49e3fc8f41b7347300a95f32f4a9e235d27820fb7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe05d0a349c4974b6129313f58fa1f51cd9d77829ef232cd17745b3ecdff6c0b"}}}, "sortedMiddleware": ["/"], "functions": {}}