{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/components/layout/Navbar.js"], "sourcesContent": ["'use client';\n\nimport { useState, Fragment } from 'react';\nimport Link from 'next/link';\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';\nimport {\n  Bars3Icon,\n  BellIcon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon,\n  UserCircleIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { useNotifications } from '@/components/providers/NotificationProvider';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Lost Items', href: '/items/lost' },\n  { name: 'Found Items', href: '/items/found' },\n  { name: 'My Dashboard', href: '/dashboard' },\n  { name: 'Messages', href: '/chat/list' },\n];\n\nconst userNavigation = [\n  { name: 'Your Profile', href: '/profile', icon: UserCircleIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n];\n\nfunction classNames(...classes) {\n  return classes.filter(Boolean).join(' ');\n}\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [notificationsOpen, setNotificationsOpen] = useState(false);\n  const { data: session, status } = useSession();\n  const { notifications, unreadCount, markAsRead } = useNotifications();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ redirect: false });\n    router.push('/');\n  };\n\n  const handleNotificationClick = async (notification) => {\n    if (!notification.isRead) {\n      await markAsRead(notification._id);\n    }\n\n    if (notification.actionUrl) {\n      router.push(notification.actionUrl);\n    }\n\n    setNotificationsOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto  px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex mx-auto h-16 w-[90%] items-center justify-between\">\n          {/* Logo and main navigation */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">LF</span>\n                </div>\n                {/* <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  UMT Lost & Found\n                </span> */}\n              </Link>\n            </div>\n\n            {/* Desktop navigation */}\n            <div className=\"hidden md:ml-10 md:block\">\n              <div className=\"flex space-x-8\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors\"\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex-1 max-w-lg mx-8 hidden lg:block\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search lost or found items...\"\n                className=\"block w-[85%] pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse flex space-x-4\">\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n              </div>\n            ) : session ? (\n              <>\n                {/* Post item button */}\n                <Link\n                  href=\"/items/post\"\n                  className=\"btn-primary hidden sm:inline-flex\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Post Item\n                </Link>\n\n                {/* Notifications */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    <BellIcon className=\"h-6 w-6\" />\n                    {unreadCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                        {unreadCount > 9 ? '9+' : unreadCount}\n                      </span>\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"p-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-3\">\n                          Notifications\n                        </h3>\n                        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                          {notifications.slice(0, 5).map((notification) => (\n                            <div\n                              key={notification._id}\n                              onClick={() => handleNotificationClick(notification)}\n                              className={classNames(\n                                'p-3 rounded-md cursor-pointer transition-colors',\n                                notification.isRead\n                                  ? 'bg-gray-50 hover:bg-gray-100'\n                                  : 'bg-blue-50 hover:bg-blue-100'\n                              )}\n                            >\n                              <p className=\"text-sm font-medium text-gray-900\">\n                                {notification.title}\n                              </p>\n                              <p className=\"text-sm text-gray-600 mt-1\">\n                                {notification.message}\n                              </p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {new Date(notification.createdAt).toLocaleDateString()}\n                              </p>\n                            </div>\n                          ))}\n                          {notifications.length === 0 && (\n                            <p className=\"text-sm text-gray-500 text-center py-4\">\n                              No notifications yet\n                            </p>\n                          )}\n                        </div>\n                        {notifications.length > 5 && (\n                          <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                            <Link\n                              href=\"/notifications\"\n                              className=\"text-sm text-blue-600 hover:text-blue-500\"\n                            >\n                              View all notifications\n                            </Link>\n                          </div>\n                        )}\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n\n                {/* User menu */}\n                <Popover className=\"relative\">\n                  <Popover.Button className=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\">\n                    {session.user.avatar ? (\n                      <img\n                        className=\"h-8 w-8 rounded-full\"\n                        src={session.user.avatar}\n                        alt={session.user.name}\n                      />\n                    ) : (\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    )}\n                  </Popover.Button>\n\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-200\"\n                    enterFrom=\"opacity-0 translate-y-1\"\n                    enterTo=\"opacity-1 translate-y-0\"\n                    leave=\"transition ease-in duration-150\"\n                    leaveFrom=\"opacity-1 translate-y-0\"\n                    leaveTo=\"opacity-0 translate-y-1\"\n                  >\n                    <Popover.Panel className=\"absolute right-0 z-10 mt-2 w-52 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\">\n                      <div className=\"py-1\">\n                        <div className=\"px-4 py-2 border-b border-gray-200\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {session.user.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {session.user.email}\n                          </p>\n                        </div>\n\n                        {userNavigation.map((item) => (\n                          <Link\n                            key={item.name}\n                            href={item.href}\n                            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          >\n                            <item.icon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                            {item.name}\n                          </Link>\n                        ))}\n\n                        <button\n                          onClick={handleSignOut}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3 text-gray-400\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </Popover.Panel>\n                  </Transition>\n                </Popover>\n              </>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-gray-500 hover:text-gray-900 text-sm font-medium\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"btn-primary\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                type=\"button\"\n                className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <Dialog as=\"div\" className=\"md:hidden\" open={mobileMenuOpen} onClose={setMobileMenuOpen}>\n          <div className=\"fixed inset-0 z-10\" />\n          <Dialog.Panel className=\"fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                <span className=\"text-xl font-bold text-gray-900\">UMT Lost & Found</span>\n              </Link>\n              <button\n                type=\"button\"\n                className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-gray-500/10\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                {session && (\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/items/post\"\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <PlusIcon className=\"h-4 w-4 mr-2\" />\n                      Post Item\n                    </Link>\n                  </div>\n                )}\n              </div>\n            </div>\n          </Dialog.Panel>\n        </Dialog>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAc,MAAM;IAAc;IAC1C;QAAE,MAAM;QAAe,MAAM;IAAe;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAY,MAAM;IAAa;CACxC;AAED,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAgB,MAAM;QAAY,MAAM,8NAAA,CAAA,iBAAc;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;IAAC;CAC5D;AAED,SAAS,WAAW,GAAG,OAAO;IAC5B,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;YAAE,UAAU;QAAM;QAChC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,WAAW,aAAa,GAAG;QACnC;QAEA,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,IAAI,CAAC,aAAa,SAAS;QACpC;QAEA,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;YAAgC,cAAW;;8BACxD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;8CASrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;+CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAYxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,0BACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAEf,wBACF;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAKvC,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;;sEACxB,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,cAAc,mBACb,6LAAC;4DAAK,WAAU;sEACb,cAAc,IAAI,OAAO;;;;;;;;;;;;8DAKhC,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;wEACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gFAEC,SAAS,IAAM,wBAAwB;gFACvC,WAAW,WACT,mDACA,aAAa,MAAM,GACf,iCACA;;kGAGN,6LAAC;wFAAE,WAAU;kGACV,aAAa,KAAK;;;;;;kGAErB,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO;;;;;;kGAEvB,6LAAC;wFAAE,WAAU;kGACV,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB;;;;;;;+EAhBjD,aAAa,GAAG;;;;;wEAoBxB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAKzD,cAAc,MAAM,GAAG,mBACtB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWb,6LAAC,oLAAA,CAAA,UAAO;4CAAC,WAAU;;8DACjB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;oDAAC,WAAU;8DACvB,QAAQ,IAAI,CAAC,MAAM,iBAClB,6LAAC;wDACC,WAAU;wDACV,KAAK,QAAQ,IAAI,CAAC,MAAM;wDACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;;;;;6EAGxB,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAI9B,6LAAC,0LAAA,CAAA,aAAU;oDACT,IAAI,6JAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,KAAK;wDAAC,WAAU;kEACvB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,IAAI;;;;;;sFAEpB,6LAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;gEAItB,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wEAEH,MAAM,KAAK,IAAI;wEACf,WAAU;;0FAEV,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;4EACpB,KAAK,IAAI;;uEALL,KAAK,IAAI;;;;;8EASlB,6LAAC;oEACC,SAAS;oEACT,WAAU;;sFAEV,6LAAC,oPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;wEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEAShF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,6LAAC,kLAAA,CAAA,SAAM;oBAAC,IAAG;oBAAM,WAAU;oBAAY,MAAM;oBAAgB,SAAS;;sCACpE,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEjC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDALL,KAAK,IAAI;;;;;;;;;;4CASnB,yBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;;sEAEjC,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3D;GAtSwB;;QAGY,iJAAA,CAAA,aAAU;QACO,yJAAA,CAAA,mBAAgB;QACpD,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%283%29/lost-found-portal/src/app/items/post/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport {\n  PhotoIcon,\n  XMarkIcon,\n  CalendarIcon,\n  MapPinIcon,\n  TagIcon\n} from '@heroicons/react/24/outline';\nimport Navbar from '@/components/layout/Navbar';\n\nconst categories = [\n  'Electronics',\n  'Books & Stationery',\n  'Clothing & Accessories',\n  'ID Cards & Documents',\n  'Keys',\n  'Water Bottles',\n  'Bags & Backpacks',\n  'Sports Equipment',\n  'Jewelry',\n  'Other'\n];\n\nconst commonLocations = [\n  'Library 1st Floor',\n  'Library 2nd Floor',\n  'Library 3rd Floor',\n  'Main Cafeteria',\n  'Food Court',\n  'Lecture Hall 1',\n  'Lecture Hall 2',\n  'Computer Lab',\n  'Main Gate',\n  'Parking Area',\n  'Sports Complex',\n  'Auditorium',\n  'Admin Block',\n  'Student Center'\n];\n\nexport default function PostItem() {\n  const { data: session } = useSession();\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: '',\n    type: 'lost',\n    location: '',\n    dateOccurred: '',\n    contactInfo: {\n      email: session?.user?.email || '',\n      phone: '',\n      preferredContact: 'email'\n    },\n    tags: '',\n    isUrgent: false,\n    reward: {\n      offered: false,\n      amount: '',\n      description: ''\n    }\n  });\n\n  const [images, setImages] = useState([]);\n  const [imagePreviews, setImagePreviews] = useState([]);\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: type === 'checkbox' ? checked : value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: type === 'checkbox' ? checked : value\n      }));\n    }\n  };\n\n  const handleImageUpload = async (e) => {\n    const files = Array.from(e.target.files);\n\n    if (files.length + images.length > 5) {\n      setError('You can upload maximum 5 images');\n      return;\n    }\n\n    setError(''); // Clear any previous errors\n\n    for (const file of files) {\n      if (file.size > 5 * 1024 * 1024) {\n        setError('Each image must be less than 5MB');\n        return;\n      }\n\n      try {\n        const base64 = await convertToBase64(file);\n        const imageData = {\n          name: file.name,\n          type: file.type,\n          size: file.size,\n          data: base64\n        };\n\n        setImages(prev => [...prev, imageData]);\n        setImagePreviews(prev => [...prev, base64]);\n      } catch (error) {\n        setError('Failed to process image: ' + file.name);\n        console.error('Image processing error:', error);\n      }\n    }\n  };\n\n  const convertToBase64 = (file) => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = reject;\n      reader.readAsDataURL(file);\n    });\n  };\n\n  const removeImage = (index) => {\n    setImages(prev => prev.filter((_, i) => i !== index));\n    setImagePreviews(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const validateForm = () => {\n    if (!formData.title.trim()) {\n      setError('Title is required');\n      return false;\n    }\n    if (!formData.description.trim()) {\n      setError('Description is required');\n      return false;\n    }\n    if (!formData.category) {\n      setError('Category is required');\n      return false;\n    }\n    if (!formData.location.trim()) {\n      setError('Location is required');\n      return false;\n    }\n    if (!formData.dateOccurred) {\n      setError('Date is required');\n      return false;\n    }\n\n    const selectedDate = new Date(formData.dateOccurred);\n    const today = new Date();\n    if (selectedDate > today) {\n      setError('Date cannot be in the future');\n      return false;\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    if (!validateForm()) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      // Prepare images for upload\n      const processedImages = images.map(img => ({\n        name: img.name,\n        type: img.type,\n        size: img.size,\n        data: img.data\n      }));\n\n      const submitData = {\n        ...formData,\n        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),\n        images: processedImages\n      };\n\n      const response = await fetch('/api/items', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submitData),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setError(data.error || 'Something went wrong');\n      } else {\n        setSuccess('Item posted successfully!');\n        setTimeout(() => {\n          router.push(`/items/${data.item._id}`);\n        }, 2000);\n      }\n    } catch (error) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Post an Item</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Help the UMT community by posting lost or found items\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          {success && (\n            <div className=\"rounded-md bg-green-50 p-4\">\n              <div className=\"text-sm text-green-700\">{success}</div>\n            </div>\n          )}\n\n          {/* Basic Information */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Basic Information</h3>\n            </div>\n            <div className=\"card-body space-y-6\">\n              {/* Type Selection */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                  Item Type *\n                </label>\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <label className=\"relative\">\n                    <input\n                      type=\"radio\"\n                      name=\"type\"\n                      value=\"lost\"\n                      checked={formData.type === 'lost'}\n                      onChange={handleInputChange}\n                      className=\"sr-only\"\n                    />\n                    <div className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      formData.type === 'lost'\n                        ? 'border-red-500 bg-red-50'\n                        : 'border-gray-300 hover:border-gray-400'\n                    }`}>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl mb-2\">😢</div>\n                        <div className=\"font-medium\">Lost Item</div>\n                        <div className=\"text-sm text-gray-500\">I lost something</div>\n                      </div>\n                    </div>\n                  </label>\n\n                  <label className=\"relative\">\n                    <input\n                      type=\"radio\"\n                      name=\"type\"\n                      value=\"found\"\n                      checked={formData.type === 'found'}\n                      onChange={handleInputChange}\n                      className=\"sr-only\"\n                    />\n                    <div className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      formData.type === 'found'\n                        ? 'border-green-500 bg-green-50'\n                        : 'border-gray-300 hover:border-gray-400'\n                    }`}>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl mb-2\">🎉</div>\n                        <div className=\"font-medium\">Found Item</div>\n                        <div className=\"text-sm text-gray-500\">I found something</div>\n                      </div>\n                    </div>\n                  </label>\n                </div>\n              </div>\n\n              {/* Title */}\n              <div>\n                <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700\">\n                  Title *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  name=\"title\"\n                  required\n                  className=\"form-input mt-1\"\n                  placeholder=\"e.g., iPhone 13 Pro, Blue Water Bottle, Student ID Card\"\n                  value={formData.title}\n                  onChange={handleInputChange}\n                />\n              </div>\n\n              {/* Category */}\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category *\n                </label>\n                <select\n                  id=\"category\"\n                  name=\"category\"\n                  required\n                  className=\"form-select mt-1\"\n                  value={formData.category}\n                  onChange={handleInputChange}\n                >\n                  <option value=\"\">Select a category</option>\n                  {categories.map(category => (\n                    <option key={category} value={category}>\n                      {category}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Description */}\n              <div>\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description *\n                </label>\n                <textarea\n                  id=\"description\"\n                  name=\"description\"\n                  rows={4}\n                  required\n                  className=\"form-textarea mt-1\"\n                  placeholder=\"Provide detailed description including color, brand, size, distinctive features...\"\n                  value={formData.description}\n                  onChange={handleInputChange}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Location and Date */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Location & Date</h3>\n            </div>\n            <div className=\"card-body space-y-6\">\n              {/* Location */}\n              <div>\n                <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700\">\n                  <MapPinIcon className=\"inline h-4 w-4 mr-1\" />\n                  Location *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"location\"\n                  name=\"location\"\n                  required\n                  list=\"locations\"\n                  className=\"form-input mt-1\"\n                  placeholder=\"Where was it lost/found?\"\n                  value={formData.location}\n                  onChange={handleInputChange}\n                />\n                <datalist id=\"locations\">\n                  {commonLocations.map(location => (\n                    <option key={location} value={location} />\n                  ))}\n                </datalist>\n              </div>\n\n              {/* Date */}\n              <div>\n                <label htmlFor=\"dateOccurred\" className=\"block text-sm font-medium text-gray-700\">\n                  <CalendarIcon className=\"inline h-4 w-4 mr-1\" />\n                  Date {formData.type === 'lost' ? 'Lost' : 'Found'} *\n                </label>\n                <input\n                  type=\"date\"\n                  id=\"dateOccurred\"\n                  name=\"dateOccurred\"\n                  required\n                  max={new Date().toISOString().split('T')[0]}\n                  className=\"form-input mt-1\"\n                  value={formData.dateOccurred}\n                  onChange={handleInputChange}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Images */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Images</h3>\n              <p className=\"text-sm text-gray-500\">Upload up to 5 images (max 5MB each)</p>\n            </div>\n            <div className=\"card-body\">\n              <div className=\"space-y-4\">\n                {/* Upload Area */}\n                <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6\">\n                  <div className=\"text-center\">\n                    <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"mt-4\">\n                      <label htmlFor=\"images\" className=\"cursor-pointer\">\n                        <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                          Upload images\n                        </span>\n                        <input\n                          id=\"images\"\n                          name=\"images\"\n                          type=\"file\"\n                          multiple\n                          accept=\"image/*\"\n                          className=\"sr-only\"\n                          onChange={handleImageUpload}\n                        />\n                      </label>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Image Previews */}\n                {imagePreviews.length > 0 && (\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                    {imagePreviews.map((preview, index) => (\n                      <div key={index} className=\"relative\">\n                        <img\n                          src={preview}\n                          alt={`Preview ${index + 1}`}\n                          className=\"w-full h-32 object-cover rounded-lg\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => removeImage(index)}\n                          className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                        >\n                          <XMarkIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Additional Information */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Additional Information</h3>\n            </div>\n            <div className=\"card-body space-y-6\">\n              {/* Tags */}\n              <div>\n                <label htmlFor=\"tags\" className=\"block text-sm font-medium text-gray-700\">\n                  <TagIcon className=\"inline h-4 w-4 mr-1\" />\n                  Tags (Optional)\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"tags\"\n                  name=\"tags\"\n                  className=\"form-input mt-1\"\n                  placeholder=\"e.g., black, leather, apple, small (separate with commas)\"\n                  value={formData.tags}\n                  onChange={handleInputChange}\n                />\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  Add keywords to help others find your item\n                </p>\n              </div>\n\n              {/* Contact Preferences */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                  Contact Information\n                </label>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label htmlFor=\"contactEmail\" className=\"block text-sm text-gray-600\">\n                      Email\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"contactEmail\"\n                      name=\"contactInfo.email\"\n                      className=\"form-input mt-1\"\n                      value={formData.contactInfo.email}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"contactPhone\" className=\"block text-sm text-gray-600\">\n                      Phone (Optional)\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"contactPhone\"\n                      name=\"contactInfo.phone\"\n                      className=\"form-input mt-1\"\n                      placeholder=\"+92 300 1234567\"\n                      value={formData.contactInfo.phone}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Urgent Flag */}\n              <div className=\"flex items-center\">\n                <input\n                  id=\"isUrgent\"\n                  name=\"isUrgent\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  checked={formData.isUrgent}\n                  onChange={handleInputChange}\n                />\n                <label htmlFor=\"isUrgent\" className=\"ml-2 block text-sm text-gray-900\">\n                  Mark as urgent (important documents, keys, etc.)\n                </label>\n              </div>\n\n              {/* Reward (for lost items) */}\n              {formData.type === 'lost' && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      id=\"rewardOffered\"\n                      name=\"reward.offered\"\n                      type=\"checkbox\"\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                      checked={formData.reward.offered}\n                      onChange={handleInputChange}\n                    />\n                    <label htmlFor=\"rewardOffered\" className=\"ml-2 block text-sm text-gray-900\">\n                      Offer a reward\n                    </label>\n                  </div>\n\n                  {formData.reward.offered && (\n                    <div className=\"ml-6 space-y-3\">\n                      <div>\n                        <label htmlFor=\"rewardAmount\" className=\"block text-sm text-gray-600\">\n                          Reward Amount (Optional)\n                        </label>\n                        <input\n                          type=\"number\"\n                          id=\"rewardAmount\"\n                          name=\"reward.amount\"\n                          min=\"0\"\n                          className=\"form-input mt-1\"\n                          placeholder=\"Amount in PKR\"\n                          value={formData.reward.amount}\n                          onChange={handleInputChange}\n                        />\n                      </div>\n\n                      <div>\n                        <label htmlFor=\"rewardDescription\" className=\"block text-sm text-gray-600\">\n                          Reward Description\n                        </label>\n                        <input\n                          type=\"text\"\n                          id=\"rewardDescription\"\n                          name=\"reward.description\"\n                          className=\"form-input mt-1\"\n                          placeholder=\"e.g., Cash reward, Gift card, etc.\"\n                          value={formData.reward.description}\n                          onChange={handleInputChange}\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-4\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"btn-secondary\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"btn-primary\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Posting...\n                </div>\n              ) : (\n                `Post ${formData.type === 'lost' ? 'Lost' : 'Found'} Item`\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AAZA;;;;;;AAcA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,cAAc;QACd,aAAa;YACX,OAAO,SAAS,MAAM,SAAS;YAC/B,OAAO;YACP,kBAAkB;QACpB;QACA,MAAM;QACN,UAAU;QACV,QAAQ;YACN,SAAS;YACT,QAAQ;YACR,aAAa;QACf;IACF;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAErD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAE/C,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC;YACnC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;wBACR,GAAG,IAAI,CAAC,OAAO;wBACf,CAAC,MAAM,EAAE,SAAS,aAAa,UAAU;oBAC3C;gBACF,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;gBAC1C,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;QAEvC,IAAI,MAAM,MAAM,GAAG,OAAO,MAAM,GAAG,GAAG;YACpC,SAAS;YACT;QACF;QAEA,SAAS,KAAK,4BAA4B;QAE1C,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,SAAS;gBACT;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,MAAM,gBAAgB;gBACrC,MAAM,YAAY;oBAChB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM;gBACR;gBAEA,UAAU,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;gBACtC,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAO;YAC5C,EAAE,OAAO,OAAO;gBACd,SAAS,8BAA8B,KAAK,IAAI;gBAChD,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;YAC3C,OAAO,OAAO,GAAG;YACjB,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC9C,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACvD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,SAAS;YACT,OAAO;QACT;QAEA,MAAM,eAAe,IAAI,KAAK,SAAS,YAAY;QACnD,MAAM,QAAQ,IAAI;QAClB,IAAI,eAAe,OAAO;YACxB,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI,CAAC,gBAAgB;YACnB,WAAW;YACX;QACF;QAEA,IAAI;YACF,4BAA4B;YAC5B,MAAM,kBAAkB,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;oBACzC,MAAM,IAAI,IAAI;oBACd,MAAM,IAAI,IAAI;oBACd,MAAM,IAAI,IAAI;oBACd,MAAM,IAAI,IAAI;gBAChB,CAAC;YAED,MAAM,aAAa;gBACjB,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO;gBACpE,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;YACzB,OAAO;gBACL,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE;gBACvC,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;4BACrC,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;4BAI1C,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;0CAK7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;;kFACf,6LAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAM;wEACN,SAAS,SAAS,IAAI,KAAK;wEAC3B,UAAU;wEACV,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAW,CAAC,yDAAyD,EACxE,SAAS,IAAI,KAAK,SACd,6BACA,yCACJ;kFACA,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAgB;;;;;;8FAC/B,6LAAC;oFAAI,WAAU;8FAAc;;;;;;8FAC7B,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;;;;;;0EAK7C,6LAAC;gEAAM,WAAU;;kFACf,6LAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAM;wEACN,SAAS,SAAS,IAAI,KAAK;wEAC3B,UAAU;wEACV,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAW,CAAC,yDAAyD,EACxE,SAAS,IAAI,KAAK,UACd,iCACA,yCACJ;kFACA,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAgB;;;;;;8FAC/B,6LAAC;oFAAI,WAAU;8FAAc;;;;;;8FAC7B,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQjD,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA0C;;;;;;kEAG3E,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,KAAK;wDACrB,UAAU;;;;;;;;;;;;0DAKd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAA0C;;;;;;kEAG9E,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,OAAO,SAAS,QAAQ;wDACxB,UAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oEAAsB,OAAO;8EAC3B;mEADU;;;;;;;;;;;;;;;;;0DAQnB,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA0C;;;;;;kEAGjF,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAM;wDACN,QAAQ;wDACR,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;;0EAClC,6LAAC,sNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;kEAGhD,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,QAAQ;wDACxB,UAAU;;;;;;kEAEZ,6LAAC;wDAAS,IAAG;kEACV,gBAAgB,GAAG,CAAC,CAAA,yBACnB,6LAAC;gEAAsB,OAAO;+DAAjB;;;;;;;;;;;;;;;;0DAMnB,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAe,WAAU;;0EACtC,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAwB;4DAC1C,SAAS,IAAI,KAAK,SAAS,SAAS;4DAAQ;;;;;;;kEAEpD,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,QAAQ;wDACR,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wDAC3C,WAAU;wDACV,OAAO,SAAS,YAAY;wDAC5B,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAM,SAAQ;oEAAS,WAAU;;sFAChC,6LAAC;4EAAK,WAAU;sFAA+C;;;;;;sFAG/D,6LAAC;4EACC,IAAG;4EACH,MAAK;4EACL,MAAK;4EACL,QAAQ;4EACR,QAAO;4EACP,WAAU;4EACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAQnB,cAAc,MAAM,GAAG,mBACtB,6LAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEACC,KAAK;oEACL,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG;oEAC3B,WAAU;;;;;;8EAEZ,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,YAAY;oEAC3B,WAAU;8EAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;2DAXf;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAsBtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAO,WAAU;;0EAC9B,6LAAC,gNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;kEAG7C,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO,SAAS,IAAI;wDACpB,UAAU;;;;;;kEAEZ,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAM5C,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAe,WAAU;kFAA8B;;;;;;kFAGtE,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,WAAU;wEACV,OAAO,SAAS,WAAW,CAAC,KAAK;wEACjC,UAAU;;;;;;;;;;;;0EAId,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAe,WAAU;kFAA8B;;;;;;kFAGtE,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,WAAU;wEACV,aAAY;wEACZ,OAAO,SAAS,WAAW,CAAC,KAAK;wEACjC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,WAAU;wDACV,SAAS,SAAS,QAAQ;wDAC1B,UAAU;;;;;;kEAEZ,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAmC;;;;;;;;;;;;4CAMxE,SAAS,IAAI,KAAK,wBACjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,MAAM,CAAC,OAAO;gEAChC,UAAU;;;;;;0EAEZ,6LAAC;gEAAM,SAAQ;gEAAgB,WAAU;0EAAmC;;;;;;;;;;;;oDAK7E,SAAS,MAAM,CAAC,OAAO,kBACtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAe,WAAU;kFAA8B;;;;;;kFAGtE,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,KAAI;wEACJ,WAAU;wEACV,aAAY;wEACZ,OAAO,SAAS,MAAM,CAAC,MAAM;wEAC7B,UAAU;;;;;;;;;;;;0EAId,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAoB,WAAU;kFAA8B;;;;;;kFAG3E,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,WAAU;wEACV,aAAY;wEACZ,OAAO,SAAS,MAAM,CAAC,WAAW;wEAClC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAW1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;gDAAuE;;;;;;mDAIxF,CAAC,KAAK,EAAE,SAAS,IAAI,KAAK,SAAS,SAAS,QAAQ,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1E;GA/kBwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}